package io.wyden.rate.client;

import com.hazelcast.map.IMap;
import com.hazelcast.query.PredicateBuilder;
import io.opentelemetry.api.trace.SpanKind;
import io.wyden.cloudutils.telemetry.tracing.Tracing;
import io.wyden.cloudutils.tools.DateUtils;
import io.wyden.pricing.client.ClientSideSubscriptionCacheFacade;
import io.wyden.published.marketdata.MarketDataEvent;
import io.wyden.rate.domain.map.EventType;
import io.wyden.rate.domain.map.MarketDataKey;
import io.wyden.rate.domain.map.Side;
import io.wyden.rate.domain.map.VenueType;
import org.apache.commons.lang3.StringUtils;
import org.springframework.lang.Nullable;

import java.util.Collection;
import java.util.Comparator;
import java.util.Optional;

import static io.wyden.rate.domain.map.MarketDataConfig.getKeyPredicate;

public class MarketDataCacheFacade {

    private final IMap<MarketDataKey, MarketDataEvent> marketDataMap;
    private final ClientSideSubscriptionCacheFacade clientSideSubscriptionCacheFacade;
    private final Tracing otlTracing;

    public MarketDataCacheFacade(IMap<MarketDataKey, MarketDataEvent> marketDataMap, ClientSideSubscriptionCacheFacade clientSideSubscriptionCacheFacade, Tracing otlTracing) {
        this.marketDataMap = marketDataMap;
        this.clientSideSubscriptionCacheFacade = clientSideSubscriptionCacheFacade;
        this.otlTracing = otlTracing;
    }

    public Optional<MarketDataEvent> findClientSideMarketData(String instrumentId, String portfolioId) {
        return findClientSideMarketDataBySide(instrumentId, portfolioId, null);
    }

    public Optional<MarketDataEvent> findClientSideMarketDataByEventType(String instrumentId, String portfolioId, EventType eventType) {
        try (var ignored = otlTracing.createSpan("marketdata.client.find", SpanKind.CLIENT)) {
            String streamId = clientSideSubscriptionCacheFacade.findStreamId(instrumentId, portfolioId);
            if (StringUtils.isBlank(streamId)) {
                return Optional.empty();
            }
            MarketDataKey marketDataKey = new MarketDataKey(instrumentId, VenueType.CLIENT, streamId, null, eventType);
            return Optional.ofNullable(marketDataMap.get(marketDataKey));
        }
    }

    public Optional<MarketDataEvent> findClientSideMarketDataBySide(String instrumentId, String portfolioId, @Nullable Side side) {
        try (var ignored = otlTracing.createSpan("marketdata.client.find", SpanKind.CLIENT)) {
            String streamId = clientSideSubscriptionCacheFacade.findStreamId(instrumentId, portfolioId);
            if (StringUtils.isBlank(streamId)) {
                return Optional.empty();
            }
            return getLastMarketDataEvent(getKeyPredicate(instrumentId, VenueType.CLIENT, streamId, null, side));
        }
    }

    public Optional<MarketDataEvent> findStreetSideMarketData(String instrumentId, String venueAccount) {
        return findStreetSideMarketDataBySide(instrumentId, venueAccount, null);
    }

    public Optional<MarketDataEvent> findStreetSideMarketDataByEventType(String instrumentId, String venueAccount, EventType eventType) {
        try (var ignored = otlTracing.createSpan("marketdata.street.find", SpanKind.CLIENT)) {
            MarketDataKey marketDataKey = new MarketDataKey(instrumentId, VenueType.STREET, null, venueAccount, eventType);
            return Optional.ofNullable(marketDataMap.get(marketDataKey));
        }
    }

    public Optional<MarketDataEvent> findStreetSideMarketDataBySide(String instrumentId, String venueAccount, Side side) {
        try (var ignored = otlTracing.createSpan("marketdata.street.find", SpanKind.CLIENT)) {
            return getLastMarketDataEvent(getKeyPredicate(instrumentId, VenueType.STREET, null, venueAccount, side));
        }
    }

    private Optional<MarketDataEvent> getLastMarketDataEvent(PredicateBuilder eventPredicate) {
        Collection<MarketDataEvent> values = marketDataMap.values(eventPredicate);
        return values.stream()
            .max(Comparator.comparing(marketDataEvent -> DateUtils.isoUtcTimeToZonedDateTime(marketDataEvent.getIdentifier().getDateTime())));
    }

    public boolean existsClientSideMarketData(String instrumentId, String portfolioId, EventType eventType) {
        try (var ignored = otlTracing.createSpan("marketdata.client.exists", SpanKind.CLIENT)) {
            String streamId = clientSideSubscriptionCacheFacade.findStreamId(instrumentId, portfolioId);
            if (StringUtils.isBlank(streamId)) {
                return false;
            }
            MarketDataKey marketDataKey = new MarketDataKey(instrumentId, VenueType.CLIENT, streamId, null, eventType);
            return marketDataMap.containsKey(marketDataKey);
        }
    }

    public boolean existsStreetSideMarketData(String instrumentId, String venueAccount, EventType eventType) {
        try (var ignored = otlTracing.createSpan("marketdata.street.exists", SpanKind.CLIENT)) {
            MarketDataKey marketDataKey = new MarketDataKey(instrumentId, VenueType.STREET, null, venueAccount, eventType);
            return marketDataMap.containsKey(marketDataKey);
        }
    }
}
