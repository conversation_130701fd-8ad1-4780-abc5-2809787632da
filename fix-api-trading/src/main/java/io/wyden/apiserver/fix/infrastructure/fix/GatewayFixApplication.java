package io.wyden.apiserver.fix.infrastructure.fix;

import io.micrometer.core.instrument.MeterRegistry;
import io.wyden.apiserver.fix.common.fix.FixMessageHandler;
import io.wyden.apiserver.fix.inbound.logon.LogoutHandler;
import io.wyden.cloudutils.telemetry.Telemetry;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;
import quickfix.Application;
import quickfix.BusinessRejectReasonText;
import quickfix.FieldNotFound;
import quickfix.IncorrectTagValue;
import quickfix.Message;
import quickfix.MessageCracker;
import quickfix.RejectLogon;
import quickfix.Session;
import quickfix.SessionID;
import quickfix.UnsupportedMessageType;
import quickfix.field.BusinessRejectReason;
import quickfix.field.MsgSeqNum;
import quickfix.field.RefMsgType;
import quickfix.field.RefSeqNum;
import quickfix.field.Text;
import quickfix.fix44.BusinessMessageReject;

import java.util.List;

/**
 * Handler for incoming FIX session messages.
 */
public class GatewayFixApplication implements Application {

    private static final Logger LOGGER = LoggerFactory.getLogger(GatewayFixApplication.class);

    private final MeterRegistry meterRegistry;
    private int templateSessionCounter = 0;
    private MessageCracker messageCracker;
    private final LogoutHandler logoutHandler;

    public GatewayFixApplication(List<FixMessageHandler> messageHandlers,
                                 Telemetry telemetry,
                                 LogoutHandler logoutHandler) {
        this.meterRegistry = telemetry.getMeterRegistry();
        this.logoutHandler = logoutHandler;
        messageHandlers.forEach(this::registerHandler);
    }

    private void registerHandler(FixMessageHandler fixMessageHandler) {
        LOGGER.debug("Registering FIX message handler: {}", fixMessageHandler.getClass().getSimpleName());

        if (messageCracker == null) {
            messageCracker = new MessageCracker(fixMessageHandler);
        } else {
            messageCracker.initialize(fixMessageHandler);
        }
    }

    @Override
    public void onCreate(SessionID sessionId) {
        try {
            LOGGER.info("Session created for client {}: {}", sessionId.getTargetCompID(), sessionId);
            trackTemplateSessionCount(sessionId);
        } catch (Exception ex) {
            LOGGER.error("Exception creating session: {}", sessionId, ex);
            throw ex;
        }
    }

    private void trackTemplateSessionCount(SessionID sessionId) {
        if (StringUtils.isBlank(sessionId.getTargetCompID())) {
            // empty only allowed for initial (template) session, if we detect more than 1, something is wrong...
            if (templateSessionCounter != 0) {
                LOGGER.error("Received more than one session creation request with empty TargetCompID: " + sessionId);
            }
            templateSessionCounter++;
        }
    }

    @Override
    public void onLogon(SessionID sessionId) {
        try {
            LOGGER.info("Session LOGGED_ON: {}", sessionId);
        } catch (Exception ex) {
            LOGGER.error("Exception logging on session: {}", sessionId, ex);
            throw ex;
        }
    }

    @Override
    public void onLogout(SessionID sessionId) {
        try {
            LOGGER.info("Session LOGGED_OFF: {}", sessionId);

            // We cannot rely on messageCracker here, after session is terminated, message may not arrive to cracker anymore (but will still arrive to FixApplication)
            logoutHandler.onMessage(null, sessionId);
        } catch (Exception ex) {
            LOGGER.error("Exception logging off session: {}", sessionId, ex);
            throw ex;
        }
    }

    @Override
    public void toAdmin(Message message, SessionID sessionId) {
        LOGGER.info("Handling toAdmin event {}. Session: {}, message: {}", message.getClass().getSimpleName(), sessionId, message);
    }

    @Override
    public void fromAdmin(Message message, SessionID sessionId) throws FieldNotFound, RejectLogon {
        try {
            LOGGER.info("Handling fromAdmin event {}. Session: {}, message: {}", message.getClass().getSimpleName(), sessionId, message);
            messageCracker.crack(message, sessionId);
        } catch (UnsupportedMessageType ex) {
            generateBusinessReject(message, BusinessRejectReason.UNSUPPORTED_MESSAGE_TYPE, 0, sessionId);
        } catch (IncorrectTagValue ex) {
            generateBusinessReject(message, ex.getSessionRejectReason(), ex.getField(), sessionId);
        } catch (Exception ex) {
            LOGGER.error("Exception when processing admin message {}, session: {}", message.getClass().getSimpleName(), sessionId, ex);

            if (ex.getCause() instanceof RejectLogon cause) {
                throw cause;
            }

            throw ex;
        }
    }

    private void generateBusinessReject(Message message, int reason, int field, SessionID sessionId) {
        try {
            BusinessMessageReject reject = new BusinessMessageReject(
                new RefMsgType(message.getClass().getSimpleName()),
                new BusinessRejectReason(reason)
            );
            reject.set(new RefSeqNum(message.getHeader().getInt(MsgSeqNum.FIELD)));
            String reasonText = BusinessRejectReasonText.getMessage(reason);
            String fieldSuffix = field > 0 ? ", field=" + field : "";
            reject.set(new Text(reasonText + fieldSuffix));
            Session.sendToTarget(reject, sessionId);
        } catch (Exception ex) {
            LOGGER.error("Exception when sending business reject message", ex);
        }
    }

    @Override
    public void toApp(Message message, SessionID sessionId) {
        LOGGER.info("Handling toApp event {}, session: {}, message: {}", message.getClass().getSimpleName(), sessionId, message);
    }

    @Override
    public void fromApp(Message message, SessionID sessionId) throws UnsupportedMessageType, IncorrectTagValue, FieldNotFound {
        try {
            LOGGER.info("Handling fromApp event {}, session: {}, message: {}", message.getClass().getSimpleName(), sessionId, message);
            updateMetrics(message);
            messageCracker.crack(message, sessionId);
        } catch (Exception ex) {
            LOGGER.error("Exception when processing {}, session: {}", message.getClass().getSimpleName(), sessionId, ex);
            throw ex;
        }
    }

    private void updateMetrics(Message message) {
        try {
            String messageType = message.getClass().getSimpleName();
            meterRegistry.counter("wyden.message.incoming.count",
                "protocol", "fix",
                "messageType", messageType).increment();
        } catch (Exception ex) {
            LOGGER.warn("Metrics update failed", ex);
        }
    }
}
