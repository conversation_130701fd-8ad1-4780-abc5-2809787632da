package io.wyden.rest.management.domain;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;

public final class VenueModel {

    private VenueModel() {
        // Empty
    }

    public enum VenueType {
        STREET,
        CLIENT,
        CLOB
    }

    public record VenueCreateRequest(
        @NotBlank(message = "name cannot be empty") String name,
        @NotNull(message = "venue type cannot be null") VenueType venueType,
        String tradingViewId
    ) {
    }
}
