mode: deployment
replicaCount: 2

imagePullSecrets:
  - name: "regcred"

image:
  repository: docker.wyden.io/mirror/otel/opentelemetry-collector-contrib
  tag: 0.120.0

config:
  receivers:
    otlp:
      protocols:
        grpc:
          endpoint: "0.0.0.0:4317"
  exporters:
    otlp:
      endpoint: tempo.monitoring:4317
      tls:
        insecure: true
  processors:
    batch: {}
    resourcedetection/env:
      detectors: [env]
      timeout: 2s
      override: false
  service:
    pipelines:
      traces:
        receivers: [otlp]
        processors: [batch,resourcedetection/env]
        exporters: [otlp]