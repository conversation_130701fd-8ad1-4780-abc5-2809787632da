image:
  pullSecrets:
    - regcred

service:
  type: ClusterIP

proxy: reencrypt

postgresql:
  enabled: false

initContainers:
  - name: themes
    image: docker.wyden.io/cloud/dev/algotrader/wyden-ui/keycloak-theme:dev
    # Pull policy Always is necessary because we use dev image tag
    imagePullPolicy: Always
    volumeMounts:
      - name: themes
        mountPath: /themes

extraVolumes:
  - name: themes
    emptyDir: {}

extraVolumeMounts:
  - name: themes
    mountPath: /opt/bitnami/keycloak/themes
