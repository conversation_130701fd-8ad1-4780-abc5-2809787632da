package io.wyden.accessgateway.keycloak;

import com.google.common.collect.Sets;
import io.micrometer.core.instrument.MeterRegistry;
import io.micrometer.core.instrument.Tags;
import io.wyden.accessgateway.client.permission.WydenRole;
import jakarta.ws.rs.NotFoundException;
import org.keycloak.admin.client.resource.RealmResource;
import org.keycloak.admin.client.resource.UsersResource;
import org.keycloak.representations.idm.GroupRepresentation;
import org.keycloak.representations.idm.RoleRepresentation;
import org.keycloak.representations.idm.UserRepresentation;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.Set;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

@Service
public class KeycloakClient {

    private final RealmResource realm;
    private final MeterRegistry meterRegistry;

    public KeycloakClient(RealmResource realm, MeterRegistry meterRegistry) {
        this.realm = realm;
        this.meterRegistry = meterRegistry;
    }

    public Optional<UserRepresentation> getUser(String username) {
        long start = System.currentTimeMillis();
        Optional<UserRepresentation> first = realm
            .users()
            .searchByUsername(username, true)
            .stream().findFirst();
        this.meterRegistry.timer("wyden.ag.keycloak.client.get.user", Tags.empty()).record(System.currentTimeMillis() - start, TimeUnit.MILLISECONDS);
        return first;
    }

    public Set<String> getUserNames() {
        long start = System.currentTimeMillis();
        Set<String> collect = fetchAllUsers()
            .stream()
            .map(UserRepresentation::getUsername)
            .collect(Collectors.toSet());
        this.meterRegistry.timer("wyden.ag.keycloak.client.get.user.names", Tags.empty()).record(System.currentTimeMillis() - start, TimeUnit.MILLISECONDS);
        return collect;
    }

    public List<UserRepresentation> fetchAllUsers() {
        UsersResource usersResource = realm.users();
        int first = 0;
        int max = 100;
        List<UserRepresentation> allUsers = new ArrayList<>();
        List<UserRepresentation> batch;

        do {
            batch = usersResource.list(first, max);
            allUsers.addAll(batch);
            first += max;
        } while (!batch.isEmpty());

        return allUsers;
    }

    public List<KeycloakUserGroupsResponseDto> getUsersGroups() {
        long start = System.currentTimeMillis();
        List<KeycloakUserGroupsResponseDto> keycloakUserGroupsResponseDtos = realm.users().list().stream().map(user -> {
            String username = user.getUsername();
            Set<String> groups = fetchGroupNames(user);
            return new KeycloakUserGroupsResponseDto(username, groups);
        }).collect(Collectors.toList());
        this.meterRegistry.timer("wyden.ag.keycloak.client.get.all.users.and.groups", Tags.empty()).record(System.currentTimeMillis() - start, TimeUnit.MILLISECONDS);
        return keycloakUserGroupsResponseDtos;
    }

    private Set<String> fetchGroupNames(UserRepresentation user) {
        return realm.users()
            .get(user.getId())
            .groups()
            .stream()
            .map(GroupRepresentation::getName)
            .collect(Collectors.toSet());
    }

    private Set<String> fetchGroupIds(String userId) {
        return realm.users()
            .get(userId)
            .groups()
            .stream()
            .map(GroupRepresentation::getId)
            .collect(Collectors.toSet());
    }

    public Set<String> fetchGroupNames() {
        long start = System.currentTimeMillis();
        Set<String> allGroups = realm
            .groups()
            .groups()
            .stream()
            .map(GroupRepresentation::getName)
            .collect(Collectors.toSet());
        this.meterRegistry.timer("wyden.ag.keycloak.client.get.all.groups", Tags.empty()).record(System.currentTimeMillis() - start, TimeUnit.MILLISECONDS);
        return allGroups;
    }

    public Set<String> fetchGroupNames(String username) {
        long start = System.currentTimeMillis();
        Optional<UserRepresentation> user = getUser(username);
        Set<String> groups = user.map(this::fetchGroupNames).orElse(Set.of());
        this.meterRegistry.timer("wyden.ag.keycloak.client.get.user.groups", Tags.empty()).record(System.currentTimeMillis() - start, TimeUnit.MILLISECONDS);
        return groups;
    }

    public Set<WydenRole> fetchWydenRoles(String username) {
        String userId = getUser(username)
            .orElseThrow(() -> new NotFoundException("User with username " + username + " not found in Keycloak"))
            .getId();
        Set<String> groupIds = fetchGroupIds(userId);
        Set<String> groupRoles = groupIds
            .stream()
            .flatMap(groupId -> realm.groups().group(groupId).roles().realmLevel().listEffective().stream())
            .map(RoleRepresentation::getName)
            .collect(Collectors.toSet());
        Set<String> userRoles = realm.users().get(userId).roles().realmLevel().listEffective()
            .stream()
            .map(RoleRepresentation::getName)
            .collect(Collectors.toSet());

        return Sets.union(groupRoles, userRoles)
            .stream()
            .filter(WydenRole::isWydenRole)
            .map(WydenRole::valueOf)
            .collect(Collectors.toSet());
    }

    public Set<String> getUsers(Set<String> groups) {
        long start = System.currentTimeMillis();
        Set<String> userNames = realm.groups().groups()
            .stream().filter(g -> groups.contains(g.getName()))
            .flatMap(g -> realm.groups().group(g.getId()).members().stream())
            .map(UserRepresentation::getUsername)
            .collect(Collectors.toSet());
        this.meterRegistry.timer("wyden.ag.keycloak.client.get.user.names.by.groups", Tags.empty()).record(System.currentTimeMillis() - start, TimeUnit.MILLISECONDS);
        return userNames;
    }
}
