{"info": {"_postman_id": "11c22468-dc7a-4b9d-988b-0e4d2dcfcc25", "name": "Fix API Server", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "item": [{"name": "Health check", "id": "856a9d85-dc11-43a9-b283-ddff7fe42f73", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "GET", "header": [], "url": {"raw": "{{fix.api.url}}/actuator/health/readiness", "host": ["{{fix.api.url}}"], "path": ["actuator", "health", "readiness"]}}, "response": []}, {"name": "Get session qualifier", "id": "5762d9a2-5a0d-4fd8-8c9f-71076523dfde", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "GET", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"username\": \"admin\",\r\n    \"password\": \"password\"\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{fix.api.url}}/session/admin", "host": ["{{fix.api.url}}"], "path": ["session", "admin"]}}, "response": []}]}