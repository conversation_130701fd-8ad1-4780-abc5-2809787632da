{"info": {"_postman_id": "3a791829-5fa0-408e-a282-c1644186da86", "name": "Rest API Server", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "item": [{"name": "Refresh securities", "id": "a339d11f-1657-40e6-a108-ab637b181c77", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "POST", "header": [], "url": {"raw": "http://localhost:8095/api/securities/refresh", "protocol": "http", "host": ["localhost"], "port": "8095", "path": ["api", "securities", "refresh"]}}, "response": []}, {"name": "Create client-side security", "id": "1f5c95d3-2a06-400a-b04c-3f52c9c1b039", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "POST", "header": [], "body": {"mode": "graphql", "graphql": {"query": "mutation CreateClientSideSecurity($request: CreateClientSideSecurityInput!) {\r\n\tcreateClientSideSecurity(request: $request) {\r\n        status\r\n  }  \r\n}", "variables": "{\r\n  \"request\": {\r\n                \"baseSecurity\": {\r\n                    \"assetClass\": \"FOREX\",\r\n                    \"description\": null,\r\n                    \"feeCurrency\": null,\r\n                    \"inverseContract\": false,\r\n                    \"quoteCurrency\": \"test2\",\r\n                    \"settlementCurrency\": null,\r\n                    \"venueName\": \"First_Sirian_BANK\"\r\n                },\r\n                \"forexSpotProperties\": {\r\n                    \"baseCurrency\": \"test1\"\r\n                },\r\n                \"tradingConstraints\": {\r\n                    \"contractSize\": null,\r\n                    \"maxPrice\": null,\r\n                    \"maxQty\": \"1\",\r\n                    \"minNotional\": null,\r\n                    \"minPrice\": null,\r\n                    \"minQty\": \"1\",\r\n                    \"priceIncr\": null,\r\n                    \"priceScale\": null,\r\n                    \"qtyIncr\": null,\r\n                    \"qtyScale\": null,\r\n                    \"tradeable\": true\r\n                },\r\n                \"securityIdentifiers\": {\r\n                }\r\n            }\r\n  }"}}, "url": {"raw": "{{gqlUrl}}", "host": ["{{gqlUrl}}"]}}, "response": []}, {"name": "Get user", "id": "f124c1b6-23ed-4650-9a56-beacf88fb5ed", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "POST", "header": [], "body": {"mode": "graphql", "graphql": {"query": "{\r\n  user(username: \"admin\") {\r\n      username\r\n      permissions {\r\n          resource\r\n          resourceId\r\n      }\r\n      groups {\r\n          name\r\n          permissions {\r\n              resource\r\n              resourceId\r\n          }\r\n      }\r\n  }\r\n }", "variables": ""}}, "url": {"raw": "{{gqlUrl}}", "host": ["{{gqlUrl}}"]}}, "response": []}, {"name": "Get groups", "id": "7da85d13-f4dc-491a-b253-af59555dce1f", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "POST", "header": [], "body": {"mode": "graphql", "graphql": {"query": "{\r\n  groups {\r\n    name\r\n    permissions {\r\n        resource\r\n        resourceId\r\n        scope\r\n    }\r\n  }\r\n }", "variables": ""}}, "url": {"raw": "{{gqlUrl}}", "host": ["{{gqlUrl}}"]}}, "response": []}, {"name": "Get venue accounts", "id": "904b6fb9-3a2f-426d-9f41-b56187042e77", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "POST", "header": [{"key": "Authorization", "value": "{{Auth}}", "type": "text"}], "body": {"mode": "graphql", "graphql": {"query": "query {\r\n    venueAccounts {\r\n        venue\r\n        venueAccounts\r\n    }\r\n}", "variables": ""}}, "url": {"raw": "{{gqlUrl}}", "host": ["{{gqlUrl}}"]}}, "response": []}, {"name": "Get venue names (CSS)", "id": "355e708a-f7b2-4fb8-a2a5-a68ea8e5105c", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "POST", "header": [], "body": {"mode": "graphql", "graphql": {"query": "{\r\n    clientSideSecuritiesVenueNames\r\n}", "variables": ""}}, "url": {"raw": "{{gqlUrl}}", "host": ["{{gqlUrl}}"]}}, "response": []}, {"name": "Update client-side security", "id": "********-650d-42ed-849a-73771805f09a", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "POST", "header": [], "body": {"mode": "graphql", "graphql": {"query": "mutation UpdateSecurity($request: UpdateSecurityInput) {\r\n\tupdateSecurity(request: $request) {\r\n        status\r\n  }  \r\n}", "variables": "{\r\n  \"request\": {\r\n                \"baseSecurity\": {\r\n                    \"description\": \"E4FF\",\r\n                    \"feeCurrency\": \"8EA4\",\r\n                    \"inverseContract\": false,\r\n                    \"settlementCurrency\": \"A978\",\r\n                    \"displayName\": \"0B05\"\r\n                },\r\n                \"tradingConstraints\": {\r\n                    \"contractSize\": \"713\",\r\n                    \"maxPrice\": \"357\",\r\n                    \"maxQty\": \"70\",\r\n                    \"minNotional\": \"840\",\r\n                    \"minPrice\": \"405\",\r\n                    \"minQty\": \"835\",\r\n                    \"priceIncr\": \"374\",\r\n                    \"priceScale\": \"643\",\r\n                    \"qtyIncr\": \"829\",\r\n                    \"qtyScale\": \"401\"\r\n                },\r\n                \"archived\": false,\r\n                \"symbol\": \"7FFD75E5@FOREX@WydenMock\"\r\n  }\r\n}"}}, "url": {"raw": "{{gqlUrl}}", "host": ["{{gqlUrl}}"]}}, "response": []}, {"name": "Update street-side security", "id": "85c51d53-c65a-4d7a-b801-58b18ff07167", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "POST", "header": [], "body": {"mode": "graphql", "graphql": {"query": "mutation UpdateSecurity($request: UpdateSecurityInput) {\r\n\tupdateSecurity(request: $request) {\r\n        status\r\n  }  \r\n}", "variables": "{\r\n  \"request\": {\r\n    \"baseSecurity\": {\r\n        \"displayName\": \"elo\"\r\n    },\r\n    \"symbol\": \"BTCUSDT@FOREX@WydenMock\"\r\n  }\r\n}"}}, "url": {"raw": "{{gqlUrl}}", "host": ["{{gqlUrl}}"]}}, "response": []}, {"name": "Create venue account", "id": "bd8d9e28-4516-4985-9aa6-3faa324de8df", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "POST", "header": [{"key": "Authorization", "value": "{{Auth}}", "type": "text"}], "body": {"mode": "graphql", "graphql": {"query": "mutation CreateVenueAccount($request: CreateVenueAccountInput!) {\r\n\tcreateVenueAccount(request: $request) {\r\n        status\r\n    }\r\n}", "variables": "{\r\n    \"request\": {\r\n        \"venueAccountName\": \"First_Sirian_BANK\",\r\n        \"venueName\": \"tomek_test_venue\"\r\n    }\r\n}"}}, "url": {"raw": "{{gqlUrl}}", "host": ["{{gqlUrl}}"]}}, "response": []}, {"name": "Get user data", "id": "5b4bd0cf-9f48-42d8-9939-3a5be2c1ca30", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer eyJhbGciOiJSUzI1NiIsInR5cCIgOiAiSldUIiwia2lkIiA6ICJHeXI0cjU0UVFoTW01WmpyUm9CaDMza3UtZjg3Vm54d21jRFFDZmF3MXhRIn0.******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.PsPu79V-iVt5CFUEtFnUrGRZ6s_j8mMOEjuYvtghQHAFszPmbv4c5d_yvRneo18rfafgKwtYEFHl75jtVyLqurSXJ3BmdsREf4DRJXzHRkreov7TH9Cqf6srjovlIsIzfKE56jh8iYrHS-q87w-95NTlRLDr9ipnYhYw2fyh7WFEDgKCeqnTreYrzCnO3BQVXfg_4DyXrfzAumzqkb13W7zJkL9BJmvB_qjpHeBx7w0yxk2QQOryMNhpkNXBMktIV2yMhxIgXU34eI_70NpucJ9YxZkWSnTRFgmlL_3MSQCcK1s7xOSnbPkyTHOupTw6iZIciWR_4qO1fGtxeAJLFA", "type": "text"}], "body": {"mode": "graphql", "graphql": {"query": "query UserData {\r\n    userData {\r\n        data\r\n    }\r\n}", "variables": ""}}, "url": {"raw": "{{gqlUrl}}", "host": ["{{gqlUrl}}"]}}, "response": []}, {"name": "Update user data", "id": "0bdb0b71-2423-4dc2-8180-32df8b662902", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "POST", "header": [{"key": "Authorization", "value": "{{Auth}}", "type": "text"}], "body": {"mode": "graphql", "graphql": {"query": "mutation UpdateUserData($request: UpdateUserDataInput!) {\r\n\tupdateUserData(request: $request) {\r\n        data\r\n    }\r\n}", "variables": "{\r\n    \"request\": {    \r\n        \"data\": \"\"\r\n    }\r\n}"}}, "url": {"raw": "{{gqlUrl}}", "host": ["{{gqlUrl}}"]}}, "response": []}, {"name": "Send order - client", "id": "52dd7da3-b62e-43cc-aebf-f4acfd597907", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "POST", "header": [{"key": "Authorization", "value": "{{Auth}}", "type": "text"}], "body": {"mode": "graphql", "graphql": {"query": "mutation SendOrder($request: NewOrderSingleRequestInput!) {\r\n  sendOrder(request: $request) {\r\n    clientId\r\n    __typename\r\n  }\r\n}", "variables": "{\r\n  \"request\": {\r\n    \"clientId\": \"admin\",\r\n    \"clOrderId\": \"admin-26\",\r\n    \"symbol\": \"MATICUSDT@FOREX@tomek_test_venue\",\r\n    \"side\": \"BUY\",\r\n    \"orderType\": \"MARKET\",\r\n    \"quantity\": \"1\",\r\n    \"price\": null,\r\n    \"tif\": \"DAY\",\r\n    \"targetVenueAccount\": \"First_Sirian_BANK\",\r\n    \"portfolioId\": \"a6ae3223-7562-4790-8a02-b00f4e70a035\"\r\n  }\r\n}"}}, "url": {"raw": "{{gqlUrl}}", "host": ["{{gqlUrl}}"]}}, "response": []}, {"name": "Send order - street", "id": "47c395ca-d006-49a1-8ff3-8dd8adafbca2", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "POST", "header": [{"key": "Authorization", "value": "{{Auth}}", "type": "text"}], "body": {"mode": "graphql", "graphql": {"query": "mutation SendOrder($request: NewOrderSingleRequestInput!) {\r\n  sendOrder(request: $request) {\r\n    clientId\r\n    __typename\r\n  }\r\n}", "variables": "{\r\n  \"request\": {\r\n    \"clientId\": \"admin\",\r\n    \"clOrderId\": \"admin-041\",\r\n    \"symbol\": \"MATICUSDT@FOREX@BitMEX\",\r\n    \"side\": \"SELL\",\r\n    \"orderType\": \"MARKET\",\r\n    \"quantity\": \"1\",\r\n    \"price\": null,\r\n    \"tif\": \"DAY\",\r\n    \"targetVenueAccount\": \"bitmex-testnet1\",\r\n    \"portfolioId\": \"8aeca2f8-a9fd-4754-ad67-dd2196eb849e\"\r\n  }\r\n}"}}, "url": {"raw": "{{gqlUrl}}", "host": ["{{gqlUrl}}"]}}, "response": []}, {"name": "Healthcheck", "id": "24cd38dc-5db4-4666-9878-46f0d4ed8339", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "GET", "header": [], "url": {"raw": "http://localhost:8095/actuator/health", "protocol": "http", "host": ["localhost"], "port": "8095", "path": ["actuator", "health"]}}, "response": []}, {"name": "Get securities by venueNames", "id": "f4a595f4-cda2-4794-a21e-10b0e8732029", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "POST", "header": [{"key": "Authorization", "value": "{{Auth}}", "type": "text"}], "body": {"mode": "graphql", "graphql": {"query": "query FindAllSecuritiesByVenueName($request: FindSecuritiesInput!) {\r\n  securities(request: $request) {\r\n    baseSecurity {\r\n        assetClass\r\n        description\r\n        feeCurrency\r\n        inverseContract\r\n        quoteCurrency\r\n        settlementCurrency\r\n        venueName\r\n        venueType\r\n        displayName\r\n    }\r\n    forexSpotProperties {\r\n        baseCurrency\r\n    }\r\n    tradingConstraints {\r\n        contractSize\r\n        maxPrice\r\n        maxQty\r\n        minNotional\r\n        minPrice\r\n        minQty\r\n        priceIncr\r\n        priceScale\r\n        qtyIncr\r\n        qtyScale\r\n        tradeable\r\n    }\r\n    securityIdentifiers {\r\n      adapterTicker\r\n      symbol\r\n    }\r\n    archivedAt\r\n  }\r\n}", "variables": "{\r\n    \"request\": {\r\n        \"venueNames\": []\r\n    }\r\n}"}}, "url": {"raw": "{{gqlUrl}}", "host": ["{{gqlUrl}}"]}}, "response": []}, {"name": "Create portfolio", "id": "fe761129-fc1e-441d-96de-34817d22d4f8", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "POST", "header": [{"key": "Authorization", "value": "{{Auth}}", "type": "text"}], "body": {"mode": "graphql", "graphql": {"query": "mutation CreatePortfolio($request: CreatePortfolioInput!) {\r\n    createPortfolio(request: $request) {\r\n        status\r\n    }\r\n}", "variables": "{\r\n    \"request\": {\r\n      \t\"name\": \"portfolio_trader_2\",\r\n        \"portfolioCurrency\": \"USD\",\r\n\t    \"tags\": [\r\n\t\t    {\r\n                \"key\": \"region\",\r\n                \"value\": \"EMEA\"\r\n            },\r\n\t\t    {\r\n                \"key\": \"asset_class\",\r\n                \"value\": \"FOREX\"\r\n            }\r\n        ]\r\n    }  \r\n}"}}, "url": {"raw": "{{gqlUrl}}", "host": ["{{gqlUrl}}"]}}, "response": []}, {"name": "Update portfolio", "id": "b5c8a461-653f-494c-aff3-be54a3a6b7b1", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "POST", "header": [], "body": {"mode": "graphql", "graphql": {"query": "mutation UpdatePortfolio($request: UpdatePortfolioInput!) {\r\n    updatePortfolio(request: $request) {\r\n        status\r\n    }\r\n}", "variables": "{\r\n    \"request\": {\r\n        \"id\": \"1fad5d09-3239-40c7-83a5-42595245238d\",\r\n        \"name\": \"portfolio1_archived\",\r\n        \"archived\": true\r\n    }\r\n}"}}, "url": {"raw": "{{gqlUrl}}", "host": ["{{gqlUrl}}"]}}, "response": []}, {"name": "Update user permissions", "id": "9229f6b8-3420-4748-8c86-ccd471e1fb71", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "POST", "header": [], "body": {"mode": "graphql", "graphql": {"query": "mutation UpdateUserPermissions($request: UpdateUserPermissionsInput!) {\r\n    updateUserPermissions(request: $request) {\r\n        username\r\n        permissions {\r\n            resource\r\n            scope\r\n            resourceId\r\n        }\r\n    }\r\n}\r\n", "variables": "{\r\n    \"request\": {\r\n        \"username\": \"admin\", \r\n        \"permissions\": [\r\n            {\r\n                \"resource\": \"venue.account\",\r\n                \"scope\": \"read\",\r\n                \"resourceId\": \"bitmex-testnet1\"\r\n            },\r\n            {\r\n                \"resource\": \"venue.account\",\r\n                \"scope\": \"trading\",\r\n                \"resourceId\": \"bitmex-testnet1\"\r\n            }\r\n        ]\r\n    }\r\n}"}}, "url": {"raw": "{{gqlUrl}}", "host": ["{{gqlUrl}}"]}}, "response": []}, {"name": "Get portfolios", "id": "a31d0e2e-ff5a-4409-aeed-daf4f4c83638", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "POST", "header": [{"key": "Authorization", "value": "{{Auth}}", "type": "text"}], "body": {"mode": "graphql", "graphql": {"query": "query GetPortfolios {\r\n    portfolios {\r\n        id\r\n        name\r\n        createdAt\r\n        portfolioCurrency\r\n        archivedAt\r\n        tags {\r\n            key\r\n            value\r\n        }\r\n    }\r\n}", "variables": ""}}, "url": {"raw": "{{gqlUrl}}", "host": ["{{gqlUrl}}"]}}, "response": []}, {"name": "Current orders", "id": "e57fe51d-793b-46e8-aa70-6bdd4857667a", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "POST", "header": [{"key": "Authorization", "value": "{{Auth}}", "type": "text"}], "body": {"mode": "graphql", "graphql": {"query": "query CurrentOrders($clientId: String!) {\r\n  currentOrders(clientId: $clientId) {\r\n    avgPrice\r\n    clOrderId\r\n    clientId\r\n    cumQty\r\n    execType\r\n    executionId\r\n    lastPrice\r\n    lastQty\r\n    leavesQty\r\n    orderId\r\n    orderQty\r\n    orderStatus\r\n    orderStatusRequestId\r\n    origClOrderId\r\n    reason\r\n    securityType\r\n    side\r\n    symbol\r\n    targetVenueAccount\r\n    targetVenueTicker\r\n    targetVenueTimestamp\r\n    text\r\n    timestamp\r\n    __typename\r\n  }\r\n}", "variables": "{\r\n  \"clientId\": \"admin\"\r\n}"}}, "url": {"raw": "{{gqlUrl}}", "host": ["{{gqlUrl}}"]}}, "response": []}]}