package io.wyden.booking.spring.lookup;

import io.wyden.booking.application.querylimit.QueryLimitExceededException;
import io.wyden.booking.application.transaction.TransactionQueryService;
import io.wyden.booking.domain.instrument.Currency;
import io.wyden.booking.domain.transaction.ExecType;
import io.wyden.booking.domain.transaction.Transaction;
import io.wyden.booking.domain.transaction.TransactionFee;
import io.wyden.booking.domain.transaction.TransactionFeeType;
import io.wyden.booking.domain.transaction.payment.Deposit;
import io.wyden.booking.domain.transaction.payment.Withdrawal;
import io.wyden.booking.domain.transaction.trade.ClientCashTrade;
import io.wyden.booking.domain.transaction.trade.StreetCashTrade;
import io.wyden.booking.domain.transaction.transfer.AccountCashTransfer;
import io.wyden.booking.domain.transaction.transfer.PortfolioCashTransfer;
import io.wyden.booking.interfaces.rest.RequestModel;
import io.wyden.booking.interfaces.rest.RequestModel.TransactionSearch;
import io.wyden.booking.spring.AbstractSpringTest;
import io.wyden.cloudutils.tools.DateUtils;
import io.wyden.published.booking.TransactionSnapshot;
import io.wyden.published.common.CursorEdge;
import io.wyden.published.common.CursorNode;
import io.wyden.published.referencedata.AccountType;
import io.wyden.published.referencedata.Portfolio;
import io.wyden.published.referencedata.PortfolioType;
import io.wyden.published.referencedata.VenueAccount;
import io.wyden.published.referencedata.WalletType;
import jakarta.transaction.Transactional;
import org.junit.Before;
import org.junit.Test;
import org.springframework.test.annotation.Commit;
import org.springframework.test.annotation.DirtiesContext;
import org.springframework.test.context.jdbc.Sql;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Arrays;
import java.util.Collection;
import java.util.Comparator;
import java.util.List;
import java.util.Set;
import java.util.stream.IntStream;

import static io.wyden.booking.assertions.BookingEngineAssertions.assertThat;
import static io.wyden.booking.common.TestUtils.randomString;
import static io.wyden.booking.domain.common.Identifiers.randomIdentifier;
import static io.wyden.booking.interfaces.rest.RequestModel.TransactionSearch.all;
import static io.wyden.booking.interfaces.rest.RequestModel.TransactionSearch.fromTo;
import static io.wyden.booking.interfaces.rest.RequestModel.TransactionSearch.justAccount;
import static io.wyden.booking.interfaces.rest.RequestModel.TransactionSearch.justCurrency;
import static io.wyden.booking.interfaces.rest.RequestModel.TransactionSearch.justExecutionId;
import static io.wyden.booking.interfaces.rest.RequestModel.TransactionSearch.justOrderId;
import static io.wyden.booking.interfaces.rest.RequestModel.TransactionSearch.justPortfolio;
import static io.wyden.booking.interfaces.rest.RequestModel.TransactionSearch.justSymbol;
import static io.wyden.booking.interfaces.rest.RequestModel.TransactionSearch.justTransactionType;
import static io.wyden.booking.interfaces.rest.RequestModel.TransactionSearch.justTransactionUuid;
import static io.wyden.booking.interfaces.rest.RequestModel.TransactionSearch.justVenueExecutionId;
import static io.wyden.booking.interfaces.rest.RequestModel.TransactionSearch.simple;
import static io.wyden.booking.interfaces.rest.RequestModel.TransactionSearch.symbolAndPortfolio;
import static io.wyden.cloudutils.tools.DateUtils.instantToEpochMillis;
import static java.math.BigDecimal.ONE;
import static java.math.BigDecimal.ZERO;
import static org.assertj.core.api.Assertions.assertThatExceptionOfType;
import static org.springframework.test.annotation.DirtiesContext.MethodMode.BEFORE_METHOD;

@Sql(scripts = "/clear-database.sql", executionPhase = Sql.ExecutionPhase.AFTER_TEST_METHOD)
public class TransactionLookupJpaTest extends AbstractSpringTest {

    private static final Comparator<String> TRANSACTION_COMPARATOR = Comparator
        .comparing(String::length)
        .thenComparing(Comparator.naturalOrder());

    private Transaction t1;
    private Transaction t2;
    private Transaction t3;
    private Transaction t4;
    private Transaction t5;
    private Transaction t6;
    private Transaction t7;
    private Transaction t8;
    private Transaction t9;
    private Transaction t10;
    private Transaction t11;
    private Transaction walletVostroToNostro;
    private Transaction walletVostroToVostro;
    private Transaction walletVostroToVostroWithoutFee;
    private Transaction walletNostroToVostro;
    private Transaction walletNostroToNostro;
    private Transaction t12;
    private Transaction t13;

    @Before
    @Override
    public void setUp() {
        super.setUp();
        stateManager.clear();

        portfoliosMap.put("Client-A", Portfolio.newBuilder().setId("Client-A").setPortfolioCurrency("USD").setPortfolioType(PortfolioType.VOSTRO).build());
        portfoliosMap.put("Client-B", Portfolio.newBuilder().setId("Client-B").setPortfolioCurrency("USD").setPortfolioType(PortfolioType.NOSTRO).build());
        portfoliosMap.put("Bank", Portfolio.newBuilder().setId("BANK").setPortfolioCurrency("USD").setPortfolioType(PortfolioType.NOSTRO).build());
        portfoliosMap.put("FeePortfolio", Portfolio.newBuilder().setId("FeePortfolio").setPortfolioCurrency("USD").setPortfolioType(PortfolioType.NOSTRO).build());

        portfoliosMap.put("P1", Portfolio.newBuilder().setId("P1").setPortfolioCurrency("USD").setPortfolioType(PortfolioType.VOSTRO).build());

        venueAccountsMap.put("BitMEX", VenueAccount.newBuilder().setId("BitMEX").setAccountType(AccountType.EXCHANGE).build());
        venueAccountsMap.put("Coinbase", VenueAccount.newBuilder().setId("Coinbase").setAccountType(AccountType.EXCHANGE).build());

        venueAccountsMap.put("Wallet-A-V", VenueAccount.newBuilder().setId("Wallet-A-V").setAccountType(AccountType.WALLET).setWalletType(WalletType.WALLET_VOSTRO).build());
        venueAccountsMap.put("Wallet-B-N", VenueAccount.newBuilder().setId("Wallet-B-N").setAccountType(AccountType.WALLET).setWalletType(WalletType.WALLET_NOSTRO).build());
        venueAccountsMap.put("Wallet-C-V", VenueAccount.newBuilder().setId("Wallet-C-V").setAccountType(AccountType.WALLET).setWalletType(WalletType.WALLET_VOSTRO).build());
        venueAccountsMap.put("Wallet-D-N", VenueAccount.newBuilder().setId("Wallet-B-N").setAccountType(AccountType.WALLET).setWalletType(WalletType.WALLET_NOSTRO).build());

        venueAccountsMap.put("A1", VenueAccount.newBuilder().setId("A1").setAccountType(AccountType.WALLET).setWalletType(WalletType.WALLET_VOSTRO).build());


        setupTransactions();

        transactionQueryService = new TransactionQueryService(transactionRepository, accessGatewayService, portfolioProvider, accountProvider);
    }

    private void setupTransactions() {
        // Client-A buys 1 BTC for 12'000 USD
        t1 = createTransaction("t1", 1, "BTC", "USD", 12_000, "Client-A", "Bank", "2023-01-01", "transaction-id", "ext-transaction-id", "custom-uuid");

        // Client-A sells 1 BTC for 15'000 USD
        t2 = createTransaction("t2", -1, "BTC", "USD", 15_000, "Client-A", "Bank", "2023-01-02", "custom-transaction-id", "ext-transaction-id", randomIdentifier());

        // Client-B buys 1'200 DOGE for 0.05 USD
        t3 = createTransaction("t3", 1_200, "DOGE", "USD", 0.05, "Client-B", "Bank", "2023-01-03", "transaction-id", "custom-ext-transaction-id", randomIdentifier());

        // Bank hedges -0.25 BTC for -3'000 USD on BitMEX
        t4 = createTransaction("t4", -0.25, "BTC", "USD", 12_000, "Bank", "BitMEX", "2023-01-04", "transaction-id", "ext-transaction-id", randomIdentifier());

        // Bank hedges 10 ETH for 0.5 BTC on Coinbase
        t5 = createTransaction("t5", 10, "ETH", "BTC", 0.05, "Bank", "Coinbase", "2023-01-05", "transaction-id", "ext-transaction-id", randomIdentifier());

        // Client-A buys 0.5 BTC for 18'000 USD on Coinbase
        t6 = createTransaction("t6", 0.5, "BTC", "USD", 15_000, "Client-A", "Coinbase", "2023-01-06", "transaction-id", "ext-transaction-id", randomIdentifier());

        // Client-B buys 800 DOGE for 0.06 USD on BitMEX
        t7 = createTransaction("t7", 800, "DOGE", "USD", 0.06, "Client-B", "BitMEX", "2023-01-07", "transaction-id", "ext-transaction-id", randomIdentifier());

        // Client transfers 1'000 USD from Coinbase to BitMEX
        t8 = createAccountCashTransfer("t8", 1_000, "USD", "Coinbase", "BitMEX", "2023-01-08", "FeePortfolio");

        // Client-A transfers 50 DOGE to Client-B
        t9 = createPortfolioCashTransfer("t9", 50, "DOGE", "Client-A", "Client-B", "2023-01-09");

        // Client-B deposits 200 ETH to BitMEX account
        t10 = createDeposit("t10", 200, "ETH", "Client-B", "BitMEX", "2023-01-10", true);

        // Client-A withdraws 2'000 DOGE from Coinbase account
        t11 = createWithdrawal("t11", 2_000, "DOGE", "Client-A", "Coinbase", "2023-01-11");


        // Client transfers 1'000 USD from wallet vostro to wallet nostro
        walletVostroToNostro = createAccountCashTransfer("wV2wN", 1_000, "USD", "Wallet-A-V", "Wallet-B-N", "2023-01-08", "FeePortfolio");

        // Client transfers 1'000 USD from wallet vostro to wallet vostro
        walletVostroToVostro = createAccountCashTransfer("wV2wV", 1_000, "USD", "Wallet-A-V", "Wallet-C-V", "2023-01-08", "FeePortfolio");

        // Client transfers 1'000 USD from wallet vostro to wallet vostro without fee
        walletVostroToVostroWithoutFee = createAccountCashTransfer("wV2wVNoFee", 1_000, "USD", "Wallet-A-V", "Wallet-C-V", "2023-01-08", null);

        // Client transfers 1'000 USD from wallet nostro to wallet vostro
        walletNostroToVostro = createAccountCashTransfer("wN2wV", 1_000, "USD", "Wallet-B-N", "Wallet-C-V", "2023-01-08", "FeePortfolio");

        // Client transfers 1'000 USD from wallet nostro to wallet vostro
        walletNostroToNostro = createAccountCashTransfer("wN2wN", 1_000, "USD", "Wallet-B-N", "Wallet-D-N", "2023-01-08", "FeePortfolio");

        t12 = createWithdrawal("t11", 2_000, "DOGE", "P1", "A1", "2023-01-11");

        t13 = createDeposit("t13", 200, "RPL", "Client-B", "BitMEX", "2099-01-10", false);

        // grant access to all portfolios and venue accounts
        grantAccess(List.of("Client-A", "Client-B", "Bank"), List.of("BitMEX", "Coinbase"));
        grantStaticPortfolioAccess(SCOPE_READ);

        transactionRepository.findAll().forEach(System.out::println);
    }

    private void clearTransactions() {
        transactionRepository.findAll()
            .forEach(entityManager::remove);
    }

    @Test
    @Commit
    @Transactional
    public void testTransactionInEmptyDatabase() {
        clearTransactions();

        Collection<TransactionSnapshot> transactions = lookup(all());
        assertThat(transactions).isEmpty();
    }

    @Test
    @Commit
    public void testTransactionFindAll() {
        Collection<TransactionSnapshot> transactions = lookup(all());
        assertThatContains(transactions, t1, t2, t3, t4, t5, t6, t7, t8, t9, t10, t11, t13);
    }

    @Test
    @Commit
    public void testTransactionFindBySymbol() {
        Collection<TransactionSnapshot> transactions = lookup(justSymbol("BTC"));
        assertThatContains(transactions, t1, t2, t4, t5, t6);

        transactions = lookup(justSymbol("USD"));
        assertThatContains(transactions, t1, t2, t3, t4, t6, t7, t8);

        transactions = lookup(justSymbol("ETH"));
        assertThatContains(transactions, t5, t10);

        transactions = lookup(justSymbol("DOGE"));
        assertThatContains(transactions, t3, t7, t9, t11);

        transactions = lookup(justSymbol("BTC", "USD", "ETH"));
        assertThatContains(transactions, t1, t2, t3, t4, t5, t6, t7, t8, t10);

        transactions = lookup(justSymbol("unknown"));
        assertThat(transactions).isEmpty();
    }

    @Test
    @Commit
    public void testTransactionFindByVenueAccount() {
        Collection<TransactionSnapshot> transactions = lookup(justAccount("BitMEX"));
        assertThatContains(transactions, t4, t7, t10, t13);

        transactions = lookup(justAccount("BitMEX", "Coinbase"));
        assertThatContains(transactions, t4, t5, t6, t7, t8, t10, t11, t13);

        transactions = lookup(justAccount("Client-A"));
        assertThat(transactions).isEmpty();

        transactions = lookup(justAccount("unknown"));
        assertThat(transactions).isEmpty();
    }

    @Test
    @Commit
    public void testTransactionFindByPortfolio() {
        Collection<TransactionSnapshot> transactions = lookup(justPortfolio("Client-A"));
        assertThatContains(transactions, t1, t2, t6, t9, t11);

        transactions = lookup(justPortfolio("Bank"));
        assertThatContains(transactions, t1, t2, t3, t4, t5);

        transactions = lookup(justPortfolio("Coinbase"));
        assertThat(transactions).isEmpty();

        transactions = lookup(justPortfolio("unknown"));
        assertThat(transactions).isEmpty();
    }

    @Test
    @Commit
    public void testTransactionFindByCurrency() {
        Collection<TransactionSnapshot> transactions = lookup(justCurrency("BTC"));
        assertThatContains(transactions, t1, t2, t4, t5, t6);

        transactions = lookup(justCurrency("USD"));
        assertThatContains(transactions, t1, t2, t3, t4, t6, t7, t8);

        transactions = lookup(justCurrency("ETH"));
        assertThatContains(transactions, t5, t10);

        transactions = lookup(justCurrency("DOGE"));
        assertThatContains(transactions, t3, t7, t9, t11);

        transactions = lookup(justCurrency("BTC", "USD", "ETH"));
        assertThatContains(transactions, t1, t2, t3, t4, t5, t6, t7, t8, t10);

        transactions = lookup(justCurrency("unknown"));
        assertThat(transactions).isEmpty();
    }

    @Test
    @Commit
    public void testTransactionFindByTransactionType() {
        Collection<TransactionSnapshot> transactions = lookup(justTransactionType("client"));
        assertThatContains(transactions, t1, t2, t3);

        transactions = lookup(justTransactionType("STREET_CASH_TRADE"));
        assertThatContains(transactions, t4, t5, t6, t7);

        transactions = lookup(justTransactionType("ACCOUNT_CASH_TRANSFER"));
        assertThatContains(transactions, t8);

        transactions = lookup(justTransactionType("PORTFOLIO_CASH_TRANSFER"));
        assertThatContains(transactions, t9);

        transactions = lookup(justTransactionType("DEPOSIT"));
        assertThatContains(transactions, t10, t13);

        transactions = lookup(justTransactionType("WITHDRAWAL"));
        assertThatContains(transactions, t11);

        transactions = lookup(justTransactionType("client", "street"));
        assertThatContains(transactions, t1, t2, t3, t4, t5, t6, t7);

        transactions = lookup(justTransactionType("unknown"));
        assertThat(transactions).isEmpty();
    }

    @Test
    @Commit
    public void testTransactionFindByOrderId() {
        Collection<TransactionSnapshot> transactions = lookup(justOrderId("t1"));
        assertThatContains(transactions, t1);

        transactions = lookup(justOrderId("unknown"));
        assertThat(transactions).isEmpty();
    }

    @Test
    @Commit
    public void testTransactionFindByExecutionId() {
        Collection<TransactionSnapshot> transactions = lookup(justExecutionId("custom-transaction-id"));
        assertThatContains(transactions, t2);

        transactions = lookup(justExecutionId("unknown"));
        assertThat(transactions).isEmpty();
    }

    @Test
    @Commit
    public void testTransactionFindByTransactionUuid() {
        Collection<TransactionSnapshot> transactions = lookup(justTransactionUuid("custom-uuid"));
        assertThatContains(transactions, t1);

        transactions = lookup(justTransactionUuid("unknown"));
        assertThat(transactions).isEmpty();
    }

    @Test
    @Commit
    public void testTransactionFindByVenueExecutionId() {
        Collection<TransactionSnapshot> transactions = lookup(justVenueExecutionId("custom-ext-transaction-id"));
        assertThatContains(transactions, t3);

        transactions = lookup(justVenueExecutionId("unknown"));
        assertThat(transactions).isEmpty();
    }

    @Test
    @Commit
    public void testTransactionFindByDateRange() {
        ZonedDateTime from = ZonedDateTime.of(2023, 1, 2, 0, 0, 0, 0, ZoneId.systemDefault());
        String fromStr = instantToEpochMillis(from.toInstant());

        ZonedDateTime to = ZonedDateTime.of(2023, 1, 4, 0, 0, 0, 0, ZoneId.systemDefault());
        String toStr = instantToEpochMillis(to.toInstant());

        Collection<TransactionSnapshot> transactions = lookup(fromTo(fromStr, null));
        assertThatContains(transactions, t2, t3, t4, t5, t6, t7, t8, t9, t10, t11, t13);

        transactions = lookup(fromTo(null, toStr));
        assertThatContains(transactions, t1, t2, t3);

        transactions = lookup(fromTo(fromStr, toStr));
        assertThatContains(transactions, t2, t3);
    }

    @Test
    @Commit
    public void testTransactionFindByProperties() {
        Collection<TransactionSnapshot> transactions;

        transactions = lookup(simple(List.of("BTC"), List.of(), List.of("Client-A"), List.of()));
        assertThatContains(transactions, t1, t2, t6);

        transactions = lookup(simple(List.of("BTC"), List.of("Coinbase"), List.of("Bank"), List.of()));
        assertThatContains(transactions, t5);

        transactions = lookup(simple(List.of("USD"), List.of("BitMEX"), List.of(), List.of()));
        assertThatContains(transactions, t4, t7);

        transactions = lookup(simple(List.of(), List.of(), List.of(), List.of()));
        assertThatContains(transactions, t1, t2, t3, t4, t5, t6, t7, t8, t9, t10, t11, t13);

        transactions = lookup(simple(List.of("BTC"), List.of(), List.of(), List.of()));
        assertThatContains(transactions, t1, t2, t4, t5, t6);

        transactions = lookup(simple(List.of("ETH"), List.of(), List.of(), List.of()));
        assertThatContains(transactions, t5, t10);

        transactions = lookup(simple(List.of("ETH"), List.of("BitMEX"), List.of(), List.of()));
        assertThatContains(transactions, t10);

        transactions = lookup(simple(List.of("SOL"), List.of("BitMEX"), List.of(), List.of()));
        assertThat(transactions).isEmpty();

        transactions = lookup(simple(List.of(), List.of(), List.of(), List.of("USD")));
        assertThatContains(transactions, t1, t2, t3, t4, t6, t7, t8);
    }

    @Test
    @Commit
    public void testRequestAllButNoAccess() {
        revokeAccess();

        List<TransactionSnapshot> transactions = lookup(all());

        assertThat(transactions).isEmpty();
    }

    @Test
    @Commit
    public void testRequestPortfolioButNoAccess() {
        revokeAccess();

        List<TransactionSnapshot> transactions = lookup(symbolAndPortfolio("BTC", "Client-A"));

        assertThat(transactions).isEmpty();
    }

    @Test
    @Commit
    public void testRequestAccountAndNoAccess() {
        revokeAccess();

        List<TransactionSnapshot> transactions = lookup(justAccount("Coinbase"));

        assertThat(transactions).isEmpty();
    }

    @Test
    @Commit
    public void testRequestAllWithPortfolioAndBankAccess() {
        grantAccess(List.of("Client-A", "Bank"), List.of());

        List<TransactionSnapshot> transactions = lookup(all());

        assertThatContains(transactions, t1, t2, t3, t9);
    }

    @Test
    @Commit
    public void testRequestAllWithOtherPortfolioAndBankAccess() {
        grantAccess(List.of("Client-B"), List.of());

        List<TransactionSnapshot> transactions = lookup(all());

        assertThatContains(transactions, t3, t9);
    }

    @Test
    @Commit
    public void testRequestAllWithPortfolioAccess() {
        grantAccess("Client-A", "");

        List<TransactionSnapshot> transactions = lookup(all());

        assertThatContains(transactions, t1, t2, t9);
    }

    @Test
    @Commit
    public void testRequestWithPortfolioAccess() {
        grantAccess("Client-A", "");
        grantStaticPortfolioAccess(SCOPE_READ);

        List<TransactionSnapshot> transactions = lookup(justPortfolio("Client-A"));

        assertThatContains(transactions, t1, t2, t9);
    }

    @Test
    @Commit
    public void testRequestWithPortfolioAccessAndAccountStaticAccess() {
        grantAccess("Client-A", "");
        grantStaticVenueAccountAccess(SCOPE_READ);

        List<TransactionSnapshot> transactions = lookup(justPortfolio("Client-A"));

        assertThatContains(transactions, t1, t2, t6, t9, t11);
    }

    @Test
    @Commit
    public void testRequestFullStaticAccess_justPortfolio() {
        revokeAccess();
        grantStaticVenueAccountAccess(SCOPE_READ);
        grantStaticPortfolioAccess(SCOPE_READ);

        List<TransactionSnapshot> transactions = lookup(justPortfolio("Client-A"));

        assertThatContains(transactions, t1, t2, t6, t9, t11);
    }

    @Test
    @Commit
    public void testRequestFullStaticAccess_justAccount() {
        revokeAccess();
        grantStaticVenueAccountAccess(SCOPE_READ);
        grantStaticPortfolioAccess(SCOPE_READ);

        List<TransactionSnapshot> transactions = lookup(justAccount("Coinbase"));

        assertThatContains(transactions, t5, t6, t11);
    }

    @Test
    @Commit
    public void testRequestWithMultiplePortfolioAccess() {
        grantAccess(List.of("Client-B", "Bank"), List.of());
        grantStaticPortfolioAccess(SCOPE_READ);

        assertThatContains(lookup(justPortfolio("Client-B")), t3, t9);
        assertThatContains(lookup(justPortfolio("Bank")), t1, t2, t3);
    }

    @Test
    @Commit
    public void testRequestAllWithBankAccess() {
        grantAccess("Bank", "");

        List<TransactionSnapshot> transactions = lookup(all());

        assertThatContains(transactions, t1, t2, t3);
    }

    @Test
    @Commit
    public void testRequestAllWithAccountAccess() {
        grantAccess("", "Coinbase");

        List<TransactionSnapshot> transactions = lookup(all());

        assertThat(transactions).isEmpty();
    }

    @Test
    @Commit
    public void testRequestAllWithAccountAndPortfolioAccess() {
        grantAccess("Client-A", "Coinbase");

        List<TransactionSnapshot> transactions = lookup(all());

        assertThatContains(transactions, t1, t2, t6, t9, t11);
    }

    @Test
    @Commit
    public void testRequestWithAccountAndPortfolioAccess() {
        grantAccess("Client-A", "Coinbase");

        TransactionSearch searchByPortfolioAndAccount = justAccount("Coinbase");

        List<TransactionSnapshot> transactions = lookup(searchByPortfolioAndAccount);

        assertThatContains(transactions, t6, t11);
    }

    @Test
    @Commit
    public void testRequestSpecificStreetSideTransaction() {
        List<TransactionSnapshot> transactions = lookup(justPortfolio("Client-A").withAccount("Coinbase"));

        assertThatContains(transactions, t6, t11);
    }

    @Test
    @Commit
    public void testRequestSpecificClientSideTransaction() {
        List<TransactionSnapshot> transactions = lookup(justPortfolio("Client-A", "Bank"));

        assertThatContains(transactions, t1, t2, t3, t4, t5, t6, t9, t11);
    }

    @Test
    @Commit
    public void testRequestSpecificPortfolio() {
        grantAccess("Client-B", "BitMEX");
        grantStaticPortfolioAccess(SCOPE_READ);

        assertThatContains(lookup(justPortfolio("Client-B")), t3, t7, t9, t10, t13);
        assertThatContains(lookup(justPortfolio("Client-B").withAccount("BitMEX")), t7, t10, t13);
    }

    @Test
    @Commit
    public void testRequestSpecificPortfolioWithAccountAccess() {
        grantAccess(List.of("Bank"), List.of("Coinbase", "BitMEX"));
        grantStaticPortfolioAccess(SCOPE_READ);

        List<TransactionSnapshot> transactions = lookup(justPortfolio("Bank"));

        assertThatContains(transactions, t1, t2, t3, t4, t5);

    }

    @Test
    @Commit
    public void testRequestSpecificAccount() {
        grantAccess("Bank", "Coinbase");

        List<TransactionSnapshot> transactions = lookup(justAccount("Coinbase"));

        assertThatContains(transactions, t5);
    }

    @Test
    @Commit
    public void testRequestPortfolioWithAnotherPortfolioAccess() {
        // user requested 'Client-A' portfolio, but has access only to 'Bank' portfolio
        grantAccess("Bank", "Coinbase");

        List<TransactionSnapshot> transactions = lookup(symbolAndPortfolio("BTC", "Client-A"));

        assertThat(transactions).isEmpty();
    }

    @Test
    @Commit
    public void testRequestAccountWithAnotherAccountAccess() {
        // user queries for transactions on 'Coinbase' account, but has access only to 'BitMEX' account
        grantAccess("Bank", "BitMEX");

        List<TransactionSnapshot> transactions = lookup(justAccount("Coinbase"));

        assertThat(transactions).isEmpty();
    }

    @Test
    @Commit
    public void testRequestPortfoliosVostro() {
        revokeAccess();
        grantStaticVenueAccountAccess(SCOPE_READ);
        grantStaticPortfolioVostroAccess(SCOPE_READ);

        List<TransactionSnapshot> transactions = lookup(all());

        assertThat(transactions).hasSize(9);
    }

    @Test
    @Commit
    public void testRequestPortfoliosNostro() {
        revokeAccess();
        grantStaticVenueAccountAccess(SCOPE_READ);
        grantStaticPortfolioNostroAccess(SCOPE_READ);

        List<TransactionSnapshot> transactions = lookup(all());

        assertThat(transactions).hasSize(12);
    }

    @Test
    @Commit
    public void testRequestPortfoliosNostro_whenRequestingValidPortfolioId_shouldReturnRequestedTransaction() {
        revokeAccess();
        grantStaticVenueAccountAccess(SCOPE_READ);
        grantStaticPortfolioNostroAccess(SCOPE_READ);

        List<TransactionSnapshot> transactions = lookup(simple(List.of(), List.of(), List.of("Client-B")));

        assertThat(transactions).hasSize(4);
    }

    @Test
    @Commit
    @DirtiesContext(methodMode = BEFORE_METHOD)
    public void testRequestSpecificPortfolios_queryLimitShouldBeExceeded() {
        revokeAccess();
        grantStaticPortfolioAccess(SCOPE_READ);
        grantStaticVenueAccountAccess(SCOPE_READ);

        List<String> requested = IntStream.range(0, 100).mapToObj(i -> ("Portfolio-" + i)).toList();

        assertThatExceptionOfType(QueryLimitExceededException.class)
            .isThrownBy(() -> lookup(simple(List.of(), List.of(), requested, List.of())));
    }

    @Test
    @Commit
    @DirtiesContext(methodMode = BEFORE_METHOD)
    public void testGetTransactionsForWalletVostro_shouldReturnVostroToVostroTransactions() {
        revokeAccess();
        grantAccess(Set.of("FeePortfolio"), Set.of()); //need to have permission to fees potfolio!
        grantStaticWalletVostroAccess(SCOPE_READ);

        List<TransactionSnapshot> transactions = lookup(all());

        assertThatContains(transactions, walletVostroToVostro, walletVostroToVostroWithoutFee);
    }

    @Test
    @Commit
    @DirtiesContext(methodMode = BEFORE_METHOD)
    public void requestedWalletVostro_shouldReturnVostroToVostroTransactions() {
        revokeAccess();
        grantAccess(Set.of("FeePortfolio"), Set.of()); //need to have permission to fees potfolio!
        grantStaticWalletVostroAccess(SCOPE_READ);

        List<TransactionSnapshot> transactions = lookup(all().withAccountType(RequestModel.AccountType.WALLET).withWalletType(RequestModel.WalletType.VOSTRO));

        assertThatContains(transactions, walletVostroToVostro, walletVostroToVostroWithoutFee);
    }

    @Test
    @Commit
    @DirtiesContext(methodMode = BEFORE_METHOD)
    public void testGetTransactionsForWalletNostro_shouldReturnNostroToNostroTransactions() {
        revokeAccess();
        grantAccess(Set.of("FeePortfolio"), Set.of()); //need to have permission to fees potfolio!
        grantStaticWalletNostroAccess(SCOPE_READ);

        List<TransactionSnapshot> transactions = lookup(all());

        assertThatContains(transactions, walletNostroToNostro);
    }

    @Test
    @Commit
    @DirtiesContext(methodMode = BEFORE_METHOD)
    public void testGetTransactionsForWalletsNostroVostro_shouldReturnNostroAndVostroTransactions() {
        revokeAccess();
        grantAccess(Set.of("FeePortfolio"), Set.of()); //need to have permission to fees potfolio!
        grantStaticWalletNostroAccess(SCOPE_READ);
        grantStaticWalletVostroAccess(SCOPE_READ);

        List<TransactionSnapshot> transactions = lookup(all());

        assertThatContains(transactions, walletVostroToVostro, walletVostroToNostro, walletNostroToVostro, walletNostroToNostro, walletVostroToVostroWithoutFee);
    }

    @Test
    @Commit
    @DirtiesContext(methodMode = BEFORE_METHOD)
    public void testGetTransactionsForAllWallets_shouldReturnNostroAndVostroTransactions() {
        revokeAccess();
        grantAccess(Set.of("FeePortfolio"), Set.of()); //need to have permission to fees potfolio!
        grantStaticWalletAccess(SCOPE_READ);

        List<TransactionSnapshot> transactions = lookup(all());

        assertThatContains(transactions, walletVostroToVostro, walletVostroToNostro, walletNostroToVostro, walletNostroToNostro, walletVostroToVostroWithoutFee);
    }

    @Test
    @Commit
    @DirtiesContext(methodMode = BEFORE_METHOD)
    public void testGetTransactionsForVostroWalletsVostroPortfolio_shouldReturnVostroToVostroTransactions() {
        revokeAccess();
        grantStaticWalletVostroAccess(SCOPE_READ);
        grantStaticPortfolioVostroAccess(SCOPE_READ);

        List<TransactionSnapshot> transactions = lookup(all());

        assertThatContains(transactions, t12, walletVostroToVostro, walletVostroToVostroWithoutFee);
    }

    @Test
    @Commit
    @DirtiesContext(methodMode = BEFORE_METHOD)
    public void depositWithFeesAndWithoutFeesByDynamicIds() {
        revokeAccess();
        grantAccess("Client-B", "BitMEX");

        List<TransactionSnapshot> transactions = lookup(all());

        assertThatContains(transactions, t10, t13, t3, t7, t9);
    }


    @Test
    @Commit
    @DirtiesContext(methodMode = BEFORE_METHOD)
    public void accountCashTransferWithoutFee() {
        revokeAccess();
        grantStaticWalletVostroAccess("read");

        List<TransactionSnapshot> transactions = lookup(justAccount("Wallet-A-V", "Wallet-C-V"));

        assertThatContains(transactions, walletVostroToVostroWithoutFee);
    }

    private List<TransactionSnapshot> lookup(TransactionSearch request) {
        TransactionSearch clientRequest = request.withClientId(CLIENT_ID);
        return transactionQueryService.lookupByProperties(clientRequest).getEdgesList().stream()
            .map(CursorEdge::getNode)
            .map(CursorNode::getTransaction)
            .toList();
    }

    private Transaction createTransaction(String orderId, double quantity, String baseCurrencyStr, String quoteCurrencyStr, double price, String reference, String counterReference, String dateStr, String executionId, String venueExecutionId, String uuid) {

        Currency baseCurrency = new Currency(baseCurrencyStr);
        Currency quoteCurrency = new Currency(quoteCurrencyStr);

        LocalDate date = LocalDate.parse(dateStr, DateTimeFormatter.ISO_DATE);
        ZonedDateTime zonedDateTime = LocalDateTime.of(date, LocalTime.NOON).atZone(DateUtils.UTC);

        String description = quantity > 0 ? "buy" : "sell";
        List<TransactionFee> fees = List.of(new TransactionFee(BigDecimal.valueOf(10), quoteCurrency, "", TransactionFeeType.TRANSACTION_FEE));

        if (isPortfolio(counterReference)) {
            ClientCashTrade trade = new ClientCashTrade(
                uuid,
                orderId,
                zonedDateTime,
                executionId,
                venueExecutionId,
                fees,
                description,
                false,
                BigDecimal.valueOf(quantity),
                ZERO,
                BigDecimal.valueOf(price),
                baseCurrency,
                "int-order-id",
                "ext-order-id",
                orderId,
                randomString(),
                randomString(),
                randomString(),
                randomString(),
                randomString(),
                quoteCurrency,
                BigDecimal.ONE,
                BigDecimal.ONE,
                ofPortfolioId(reference),
                ofPortfolioId(counterReference),
                ExecType.FILL);

            return transactionStorageService.store(trade);
        }

        StreetCashTrade trade = new StreetCashTrade(
            uuid,
            orderId,
            zonedDateTime,
            executionId,
            venueExecutionId,
            fees,
            description,
            false,
            BigDecimal.valueOf(quantity),
            ZERO,
            BigDecimal.valueOf(price),
            baseCurrency,
            "int-order-id",
            "ext-order-id",
            "order-id",
            randomString(),
            randomString(),
            randomString(),
            randomString(),
            randomString(),
            quoteCurrency,
            BigDecimal.ONE,
            BigDecimal.ONE,
            ofPortfolioId(reference),
            ofAccountId(counterReference),
            ExecType.FILL);

        return transactionStorageService.store(trade);
    }

    private Transaction createAccountCashTransfer(String orderId, int quantity, String currencyStr, String sourceAccount, String targetAccount, String dateStr, String feesPortfolioId) {
        Currency currency = new Currency(currencyStr);

        LocalDate date = LocalDate.parse(dateStr, DateTimeFormatter.ISO_DATE);
        ZonedDateTime zonedDateTime = LocalDateTime.of(date, LocalTime.NOON).atZone(DateUtils.UTC);

        Collection<TransactionFee> fees = List.of(new TransactionFee(BigDecimal.valueOf(10), currency, "", TransactionFeeType.TRANSACTION_FEE));

        AccountCashTransfer transfer = new AccountCashTransfer(
            randomIdentifier(),
            orderId,
            zonedDateTime,
            "transaction-id",
            "ext-transaction-id",
            ONE,
            ONE,
            fees,
            "transfer",
            BigDecimal.valueOf(quantity),
            "int-transfer-id",
            "ext-transfer-id",
            currency,
            ofAccountId(sourceAccount),
            ofAccountId(targetAccount),
            ofAccountId(sourceAccount),
            feesPortfolioId == null ? null : ofPortfolioId(feesPortfolioId)
        );

        return transactionStorageService.store(transfer);
    }

    private Transaction createPortfolioCashTransfer(String orderId, int quantity, String currencyStr, String sourcePortfolio, String targetPortfolio, String dateStr) {
        Currency currency = new Currency(currencyStr);

        LocalDate date = LocalDate.parse(dateStr, DateTimeFormatter.ISO_DATE);
        ZonedDateTime zonedDateTime = LocalDateTime.of(date, LocalTime.NOON).atZone(DateUtils.UTC);

        Collection<TransactionFee> fees = List.of(new TransactionFee(BigDecimal.valueOf(10), currency, "", TransactionFeeType.TRANSACTION_FEE));

        PortfolioCashTransfer transfer = new PortfolioCashTransfer(
            randomIdentifier(),
            orderId,
            zonedDateTime,
            "transaction-id",
            "ext-transaction-id",
            ONE,
            ONE,
            fees,
            "transfer",
            BigDecimal.valueOf(quantity),
            "int-transfer-id",
            "ext-transfer-id",
            currency,
            ofPortfolioId(sourcePortfolio),
            ofPortfolioId(targetPortfolio),
            ofPortfolioId(sourcePortfolio)
        );

        return transactionStorageService.store(transfer);
    }


    private Transaction createDeposit(String orderId, int quantity, String currencyStr, String targetPortfolioId, String targetAccountId, String dateStr, boolean withFee) {
        Currency currency = new Currency(currencyStr);

        LocalDate date = LocalDate.parse(dateStr, DateTimeFormatter.ISO_DATE);
        ZonedDateTime zonedDateTime = LocalDateTime.of(date, LocalTime.NOON).atZone(DateUtils.UTC);

        List<TransactionFee> fees = List.of(new TransactionFee(BigDecimal.valueOf(10), currency, "", TransactionFeeType.TRANSACTION_FEE));

        Deposit payment = new Deposit(
            randomIdentifier(),
            orderId,
            zonedDateTime,
            "transaction-id",
            "ext-transaction-id",
            fees,
            "payment",
            currency,
            BigDecimal.valueOf(quantity),
            ofPortfolioId(targetPortfolioId),
            ofAccountId(targetAccountId),
            withFee ? ofPortfolioId(targetPortfolioId) : null,
            withFee ? ofAccountId(targetAccountId) : null,
            ONE,
            ONE
        );

        return transactionStorageService.store(payment);
    }

    private Transaction createWithdrawal(String orderId, int quantity, String currencyStr, String sourcePortfolioId, String sourceAccountId, String dateStr) {
        Currency currency = new Currency(currencyStr);

        LocalDate date = LocalDate.parse(dateStr, DateTimeFormatter.ISO_DATE);
        ZonedDateTime zonedDateTime = LocalDateTime.of(date, LocalTime.NOON).atZone(DateUtils.UTC);

        List<TransactionFee> fees = List.of(new TransactionFee(BigDecimal.valueOf(10), currency, "", TransactionFeeType.TRANSACTION_FEE));

        Withdrawal payment = new Withdrawal(
            randomIdentifier(),
            orderId,
            zonedDateTime,
            "transaction-id",
            "ext-transaction-id",
            fees,
            "payment",
            currency,
            BigDecimal.valueOf(quantity),
            ofPortfolioId(sourcePortfolioId),
            ofAccountId(sourceAccountId),
            ofPortfolioId(sourcePortfolioId),
            ofAccountId(sourceAccountId),
            ONE,
            ONE
        );

        return transactionStorageService.store(payment);
    }

    private boolean isPortfolio(String reference) {
        return reference.startsWith("Client") || reference.startsWith("Bank");
    }

    private void assertThatContains(Collection<TransactionSnapshot> transactions, Transaction... expected) {
        List<String> actualIds = transactions.stream()
            .map(t -> {
                if (t.hasClientCashTrade()) return t.getClientCashTrade().getReservationRef();
                if (t.hasStreetCashTrade()) return t.getStreetCashTrade().getReservationRef();
                if (t.hasClientAssetTrade()) return t.getClientAssetTrade().getReservationRef();
                if (t.hasStreetAssetTrade()) return t.getStreetAssetTrade().getReservationRef();
                if (t.hasDeposit()) return t.getDeposit().getReservationRef();
                if (t.hasWithdrawal()) return t.getWithdrawal().getReservationRef();
                if (t.hasAccountCashTransfer()) return t.getAccountCashTransfer().getReservationRef();
                if (t.hasPortfolioCashTransfer()) return t.getPortfolioCashTransfer().getReservationRef();
                return "";
            })
            .sorted(TRANSACTION_COMPARATOR)
            .toList();

        List<String> expectedIds = Arrays.stream(expected)
            .map(Transaction::getReservationRef)
            .sorted(TRANSACTION_COMPARATOR)
            .toList();

        assertThat(actualIds).containsExactlyElementsOf(expectedIds);
    }
}
