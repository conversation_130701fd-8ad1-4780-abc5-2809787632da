package io.wyden.booking.spring;

import io.wyden.booking.common.OemsRequestFactory;
import io.wyden.booking.domain.instrument.Currency;
import io.wyden.booking.domain.position.CashPosition;
import io.wyden.booking.domain.position.PortfolioReference;
import io.wyden.booking.domain.reservation.Reservation;
import io.wyden.published.oems.OemsRequest;
import io.wyden.published.referencedata.Portfolio;
import org.junit.Before;
import org.junit.Test;
import org.springframework.dao.DataIntegrityViolationException;
import org.springframework.test.annotation.Commit;
import org.springframework.test.annotation.DirtiesContext;

import java.math.BigDecimal;
import java.util.List;
import java.util.concurrent.Callable;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.Future;
import java.util.stream.IntStream;

import static io.wyden.booking.assertions.BookingEngineAssertions.assertThat;
import static io.wyden.cloudutils.tools.BigDecimalUtils.bd;
import static java.util.Collections.emptyList;
import static org.assertj.core.api.Assertions.fail;
import static org.mockito.Mockito.doCallRealMethod;
import static org.springframework.test.annotation.DirtiesContext.MethodMode.BEFORE_METHOD;

public class DuplicateReservationProcessingTest extends AbstractSpringTest {

    @Before
    public void setUp() {
        super.setUp();
        updatePrice("ETH", "USD", BigDecimal.valueOf(1_250));
    }

    @Test
    @Commit
    @DirtiesContext(methodMode = BEFORE_METHOD)
    public void testDuplicateMessageAreDiscarded() throws InterruptedException {
        // Verifies that consecutive messages are marked as duplicates and not processed by Booking Engine.
        // Only THE FIRST message will be processed. All other messages will be discarded.
        // As a result, only one transaction is allowed to be created.

        OemsRequest oemsRequest = OemsRequestFactory.createOemsRequestLimit(BigDecimal.valueOf(0.1), BigDecimal.ONE)
            .setBaseCurrency("ETH")
            .setQuoteCurrency("BTC")
            .build();

        String portfolioId = oemsRequest.getPortfolioId();
        String counterPortfolioId = oemsRequest.getExecutionConfig().getCounterPortfolio();

        portfoliosMap.put(portfolioId, Portfolio.newBuilder().setPortfolioCurrency("USD").build());
        portfoliosMap.put(counterPortfolioId, Portfolio.newBuilder().setPortfolioCurrency("USD").build());

        PortfolioReference portfolio = stateManager.findOrCreatePortfolio(portfolioId);
        PortfolioReference counterPortfolio = stateManager.findOrCreatePortfolio(counterPortfolioId);

        // first, set up a deposit for Client
        stateManager.store(new CashPosition(bd("10"), portfolio, new Currency("BTC")));

        // and set up a deposit for Bank
        stateManager.store(new CashPosition(bd("100"), counterPortfolio, new Currency("ETH")));

        String requestId = oemsRequest.getMetadata().getRequestId();

        // About error messages in console:
        // When running hazelcast-related tests, you might find exceptions caused by missing serializer for type XYZ.
        // This might happen when another hazelcast member is running. All member in cluster must have the same serializer configuration.
        // Since it's not necessary in test scope, you can disregard exceptions or shutdown other hazelcast members
        // to get rid of exceptions.

        oemsRequestProcessor.enqueue(oemsRequest);
        oemsRequestProcessor.enqueue(oemsRequest);
        oemsRequestProcessor.enqueue(oemsRequest);
        oemsRequestProcessor.enqueue(oemsRequest);
        oemsRequestProcessor.enqueue(oemsRequest);
        Thread.sleep(3000);

        // only one reservation was processed
        statePersistenceScheduler.flushAll();
        List<Reservation> reservations = reservationRepository.findAll();
        assertThat(reservations).hasSize(1);

        // verify processing is marked as completed
        boolean completed = messageHistoryService.isCompleted(requestId);
        assertThat(completed).isTrue();
    }

    @Test
    @Commit
    @DirtiesContext(methodMode = BEFORE_METHOD)
    public void testDuplicateParallelMessageAreDiscarded() throws InterruptedException {
        // Verifies that parallel messages are marked as duplicates and not processed by Booking Engine.
        // ALL messages will start processing and will go through booking engine system. The first message
        // that will finish processing will be the only one that will be stored.
        // All other messages will cause DataIntegrityException violation and will cause the main transaction to roll back all changes.
        // As a result, only one transaction is allowed to be created.

        OemsRequest oemsRequest = OemsRequestFactory.createOemsRequestLimit(BigDecimal.valueOf(0.1), BigDecimal.ONE)
            .setBaseCurrency("ETH")
            .setQuoteCurrency("BTC")
            .build();

        String portfolioId = oemsRequest.getPortfolioId();
        String counterPortfolioId = oemsRequest.getExecutionConfig().getCounterPortfolio();

        portfoliosMap.put(portfolioId, Portfolio.newBuilder().setPortfolioCurrency("USD").build());
        portfoliosMap.put(counterPortfolioId, Portfolio.newBuilder().setPortfolioCurrency("USD").build());

        PortfolioReference portfolio = stateManager.findOrCreatePortfolio(portfolioId);
        PortfolioReference counterPortfolio = stateManager.findOrCreatePortfolio(counterPortfolioId);

        // first, set up a deposit for Client
        stateManager.store(new CashPosition(bd("10"), portfolio, new Currency("BTC")));

        // and set up a deposit for Bank
        stateManager.store(new CashPosition(bd("100"), counterPortfolio, new Currency("ETH")));

        String requestId = oemsRequest.getMetadata().getRequestId();

        // simulate unique constraint exception in ProcessedMessage database table
        // hazelcast cluster member in storage project is configured with write-through mapStore for ProcessedMessage
        // so it will save it's content to database immediately throwing SQL unique constraint exceptions
        doCallRealMethod()
            .doThrow(DataIntegrityViolationException.class)
            .when(messageHistoryService).markCompleted(requestId);

        ExecutorService executorService = Executors.newFixedThreadPool(10);

        List<Callable<Void>> tasks = IntStream.range(0, 10)
            .mapToObj(i -> (Callable<Void>) () -> {
                oemsRequestProcessor.enqueue(oemsRequest);
                return null;
            })
            .toList();

        List<Future<Void>> futures = emptyList();
        try {
            futures = executorService.invokeAll(tasks);
        } catch (InterruptedException e) {
            fail(e.getMessage());
        }

        futures.stream().map(resultFuture -> {
                try {
                    return resultFuture.get();
                } catch (InterruptedException | ExecutionException e) {
                    System.err.println(e.getMessage());
                    return null;
                }
            })
            .toList();

        Thread.sleep(1000);

        statePersistenceScheduler.flushAll();

        // only one reservation was processed
        List<Reservation> reservations = reservationRepository.findAll();
        assertThat(reservations).hasSize(1);

        // verify processing is marked as completed
        boolean completed = messageHistoryService.isCompleted(requestId);
        assertThat(completed).isTrue();
    }
}
