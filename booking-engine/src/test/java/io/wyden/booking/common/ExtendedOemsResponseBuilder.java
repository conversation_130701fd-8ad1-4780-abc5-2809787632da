package io.wyden.booking.common;

import io.wyden.cloudutils.tools.DateUtils;
import io.wyden.published.common.Metadata;
import io.wyden.published.oems.OemsExecType;
import io.wyden.published.oems.OemsInstrumentType;
import io.wyden.published.oems.OemsOrderStatus;
import io.wyden.published.oems.OemsRequest;
import io.wyden.published.oems.OemsResponse;
import io.wyden.published.oems.OemsSide;

import java.math.BigDecimal;
import java.time.ZonedDateTime;

import static io.wyden.booking.common.TestUtils.randomString;
import static io.wyden.cloudutils.tools.ProtobufUtils.toProtoString;
import static io.wyden.published.oems.OemsResponse.OemsResponseType.EXECUTION_REPORT;

public class ExtendedOemsResponseBuilder {

    private final OemsResponse.Builder builder;

    private ExtendedOemsResponseBuilder(OemsResponse.Builder builder) {
        this.builder = builder;
    }

    public static ExtendedOemsResponseBuilder newBuilder() {
        return new ExtendedOemsResponseBuilder(OemsResponse.newBuilder());
    }

    public OemsResponse.Builder streetSideFill(BigDecimal qty, BigDecimal price) {
        fill(qty, price);

        Metadata.Builder streetSideMetadata = builder.getMetadataBuilder()
            .setSource("wydenmock-account")
            .setSourceType(Metadata.ServiceType.EXTERNAL_VENUE_ACCOUNT);

        return builder
            .setMetadata(streetSideMetadata)
            .setVenueAccount("wydenmock-account");
    }

    public OemsResponse.Builder clientSideFill(BigDecimal qty, BigDecimal price) {
        fill(qty, price);

        Metadata.Builder clientSideMetadata = builder.getMetadataBuilder()
            .setSource("Agency")
            .setSourceType(Metadata.ServiceType.BROKER_DESK);

        return builder
            .setMetadata(clientSideMetadata)
            .setCounterPortfolioId("Bank");
    }

    public OemsResponse.Builder fill(BigDecimal qty, BigDecimal price) {
        String qtyProto = toProtoString(qty);
        String priceProto = toProtoString(price);

        return builder
            .setOrderQty(qtyProto)
            .setLastQty(qtyProto)
            .setLeavesQty("0")
            .setCumQty(qtyProto)
            .setLastPrice(priceProto)
            .setAvgPrice(priceProto);
    }

    public OemsResponse.Builder isRejectedByExchange() {
        return builder
            .setExecType(OemsExecType.REJECTED)
            .setOrderStatus(OemsOrderStatus.STATUS_REJECTED)
            .setRequestResult(OemsResponse.Result.OEMS_ORDER_REGISTRATION_ERROR_VALIDATION)
            .setReason("Field instrumentId is blank");
    }

    public OemsResponse.Builder isRejectedByPTC() {
        return builder
            .setExecType(OemsExecType.REJECTED)
            .setOrderStatus(OemsOrderStatus.STATUS_REJECTED)
            .setRequestResult(OemsResponse.Result.PTC_REJECT)
            .setReason("Exceeds max exposure")
            .setAvgPrice("0")
            .setLastPrice("0")
            .setCumQty("0")
            .setLastQty("0")
            .setLeavesQty("0");
    }

    public ExtendedOemsResponseBuilder inResponseTo(OemsRequest oemsRequest) {
        builder
            .setPortfolioId(oemsRequest.getPortfolioId())
            .setCounterPortfolioId(oemsRequest.getExecutionConfig().getCounterPortfolio())
            .setInstrumentId(oemsRequest.getInstrumentId())
            .setBaseCurrency(oemsRequest.getBaseCurrency())
            .setQuoteCurrency(oemsRequest.getQuoteCurrency())
            .setOrderId(oemsRequest.getOrderId())
            .setOrderQty(oemsRequest.getQuantity())
            .setLastQty(oemsRequest.getQuantity())
            .setLeavesQty("0")
            .setCumQty(oemsRequest.getQuantity())
            .setLastPrice(oemsRequest.getPrice())
            .setAvgPrice(oemsRequest.getPrice());

        return this;
    }

    public ExtendedOemsResponseBuilder randomFill() {
        Metadata.Builder metadata = Metadata.newBuilder()
            .setResponseId(randomString())
            .setRequestId(randomString())
            .setSourceType(Metadata.ServiceType.BROKER_DESK)
            .setSource("Agency");

        builder
            .setMetadata(metadata)
            .setOrderId("order-A")
            .setIntId("int-id-A")
            .setExtId("ext-id-A")
            .setResponseType(EXECUTION_REPORT)
            .setCounterPortfolioId("Bank")
            .setExecType(OemsExecType.FILL)
            .setOrderStatus(OemsOrderStatus.STATUS_FILLED)
            .setInstrumentType(OemsInstrumentType.FOREX)
            .setSide(OemsSide.BUY)
            .setInstrumentId("BTC/USD")
            .setOrderQty("1")
            .setLastQty("1")
            .setLeavesQty("0")
            .setCumQty("1")
            .setLastPrice("12000")
            .setAvgPrice("12000")
            .setClientId(randomString())
            .setBaseCurrency("BTC")
            .setQuoteCurrency("USD")
            .setFee("10")
            .setFeeCurrency("USD")
            .setPortfolioId("Client 1")
            .setReason(randomString())
            .setVenueTimestamp(DateUtils.toIsoUtcTime(ZonedDateTime.now()));

        return this;
    }
}
