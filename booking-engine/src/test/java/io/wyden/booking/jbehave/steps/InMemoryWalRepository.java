package io.wyden.booking.jbehave.steps;

import io.wyden.booking.application.wal.WalCommand;
import io.wyden.booking.application.wal.WalCommandsRepository;
import org.springframework.data.domain.Example;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.repository.query.FluentQuery;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.concurrent.ConcurrentHashMap;
import java.util.function.Function;

public class InMemoryWalRepository implements WalCommandsRepository {

    private final Map<Long, WalCommand> commands = new ConcurrentHashMap<>();
    private Long sequence = 0L;

    @Override
    public List<WalCommand> findAllByIdGreaterThan(Long id) {
        return commands.values()
            .stream()
            .filter(i -> i.getSequenceNumber() > id)
            .toList();
    }

    @Override
    public List<WalCommand> findAllByIdGreaterThanWithLimit(Long id, int limit) {
        return commands.values()
            .stream()
            .filter(i -> i.getSequenceNumber() > id)
            .limit(limit)
            .toList();
    }

    @Override
    public void flush() {

    }

    @Override
    public <S extends WalCommand> S saveAndFlush(S entity) {
        throw new UnsupportedOperationException();
    }

    @Override
    public <S extends WalCommand> List<S> saveAllAndFlush(Iterable<S> entities) {
        throw new UnsupportedOperationException();
    }

    @Override
    public void deleteAllInBatch(Iterable<WalCommand> entities) {
        throw new UnsupportedOperationException();

    }

    @Override
    public void deleteAllByIdInBatch(Iterable<Long> longs) {
        throw new UnsupportedOperationException();

    }

    @Override
    public void deleteAllInBatch() {
        throw new UnsupportedOperationException();

    }

    @Override
    public WalCommand getOne(Long aLong) {
        throw new UnsupportedOperationException();

    }

    @Override
    public WalCommand getById(Long aLong) {
        throw new UnsupportedOperationException();

    }

    @Override
    public WalCommand getReferenceById(Long aLong) {
        throw new UnsupportedOperationException();

    }

    @Override
    public <S extends WalCommand> Optional<S> findOne(Example<S> example) {
        throw new UnsupportedOperationException();

    }

    @Override
    public <S extends WalCommand> List<S> findAll(Example<S> example) {
        return (List<S>) commands.values();
    }

    @Override
    public <S extends WalCommand> List<S> findAll(Example<S> example, Sort sort) {
        throw new UnsupportedOperationException();

    }

    @Override
    public <S extends WalCommand> Page<S> findAll(Example<S> example, Pageable pageable) {
        throw new UnsupportedOperationException();

    }

    @Override
    public <S extends WalCommand> long count(Example<S> example) {
        throw new UnsupportedOperationException();

    }

    @Override
    public <S extends WalCommand> boolean exists(Example<S> example) {
        throw new UnsupportedOperationException();

    }

    @Override
    public <S extends WalCommand, R> R findBy(Example<S> example, Function<FluentQuery.FetchableFluentQuery<S>, R> queryFunction) {
        throw new UnsupportedOperationException();

    }

    @Override
    public <S extends WalCommand> S save(S entity) {
        Long sequenceNumber = entity.getSequenceNumber() == null ? ++sequence : entity.getSequenceNumber();
        entity.setSequenceNumber(sequenceNumber);
        return (S) commands.put(sequenceNumber, entity);
    }

    @Override
    public <S extends WalCommand> List<S> saveAll(Iterable<S> entities) {
        throw new UnsupportedOperationException();


    }

    @Override
    public Optional<WalCommand> findById(Long aLong) {
        throw new UnsupportedOperationException();

    }

    @Override
    public boolean existsById(Long aLong) {
        throw new UnsupportedOperationException();

    }

    @Override
    public List<WalCommand> findAll() {
        return new ArrayList<>(commands.values());
    }

    @Override
    public List<WalCommand> findAllById(Iterable<Long> longs) {
        throw new UnsupportedOperationException();

    }

    @Override
    public long count() {
        throw new UnsupportedOperationException();

    }

    @Override
    public void deleteById(Long aLong) {
        throw new UnsupportedOperationException();

    }

    @Override
    public void delete(WalCommand entity) {
        throw new UnsupportedOperationException();

    }

    @Override
    public void deleteAllById(Iterable<? extends Long> longs) {
        throw new UnsupportedOperationException();

    }

    @Override
    public void deleteAll(Iterable<? extends WalCommand> entities) {
        throw new UnsupportedOperationException();

    }

    @Override
    public void deleteAll() {
        commands.clear();
    }

    @Override
    public List<WalCommand> findAll(Sort sort) {
        throw new UnsupportedOperationException();

    }

    @Override
    public Page<WalCommand> findAll(Pageable pageable) {
        throw new UnsupportedOperationException();

    }
}
