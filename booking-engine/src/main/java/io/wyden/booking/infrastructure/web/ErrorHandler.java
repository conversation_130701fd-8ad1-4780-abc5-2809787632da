package io.wyden.booking.infrastructure.web;

import com.fasterxml.jackson.databind.ObjectMapper;
import io.wyden.cloud.utils.rest.errorhandling.Error;
import io.wyden.cloudutils.tools.DateUtils;
import jakarta.servlet.ServletException;
import org.jetbrains.annotations.NotNull;
import org.slf4j.Logger;
import org.springframework.dao.IncorrectResultSizeDataAccessException;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.MissingServletRequestParameterException;
import org.springframework.web.bind.annotation.ControllerAdvice;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.client.HttpServerErrorException;
import org.springframework.web.context.request.ServletWebRequest;

import java.io.IOException;
import java.io.PrintWriter;
import java.io.StringWriter;
import java.time.Instant;
import java.time.ZonedDateTime;

import static org.apache.commons.lang3.StringUtils.isBlank;
import static org.slf4j.LoggerFactory.getLogger;

@ControllerAdvice
public class ErrorHandler {

    private static final Logger LOGGER = getLogger(ErrorHandler.class);

    private final ObjectMapper objectMapper;

    public ErrorHandler(ObjectMapper objectMapper) {
        this.objectMapper = objectMapper;
    }

    private static String getStackTrace(Throwable ex) {
        StringWriter stackTrace = new StringWriter();
        ex.printStackTrace(new PrintWriter(stackTrace));
        stackTrace.flush();
        return stackTrace.toString();
    }

    private static String getException(Throwable ex) {
        Throwable error = ex;
        while (true) {
            if (!(error instanceof ServletException) || error.getCause() == null) {
                return error.getClass().getName();
            }

            error = error.getCause();
        }
    }

    private static String getMessage(Throwable ex) {
        String message = ex.getMessage();
        if (isBlank(message)) {
            return "No message available";
        }

        return message;
    }

    @NotNull
    private Error getError(HttpStatus status, String msg, Exception ex, ServletWebRequest request) {
        Instant timestamp = Instant.now();
        String reason = status.getReasonPhrase();
        String path = request.getRequest().getRequestURI();
        String exception = getException(ex);
        String stackTrace = getStackTrace(ex);
        return new Error(timestamp, status.value(), reason, msg, path, exception, stackTrace);
    }

    @ExceptionHandler({MissingServletRequestParameterException.class})
    public ResponseEntity<Error> handleMissingParameterException(MissingServletRequestParameterException ex, ServletWebRequest request) {
        HttpStatus status = HttpStatus.BAD_REQUEST;
        String message = ex.getBody().getDetail();

        LOGGER.error(message, ex);

        Error error = getError(status, message, ex, request);
        return new ResponseEntity<>(error, status);
    }

    @ExceptionHandler({HttpServerErrorException.class})
    public ResponseEntity<Error> handleHttpServerErrorException(HttpServerErrorException ex, ServletWebRequest request) {
        HttpStatus status = HttpStatus.INTERNAL_SERVER_ERROR;
        String message = getMessage(ex);

        Error cause = getCause(ex);
        if (cause != null) {
            message = cause.message();
            status = HttpStatus.resolve(cause.status());
        }

        LOGGER.error(message, ex);

        Error error = getError(status, message, ex, request);
        return new ResponseEntity<>(error, status);
    }

    @ExceptionHandler({IncorrectResultSizeDataAccessException.class})
    public ResponseEntity<Error> handleIncorrectResultSizeException(IncorrectResultSizeDataAccessException ex, ServletWebRequest request) {
        HttpStatus status = HttpStatus.NOT_FOUND;
        String message = getMessage(ex);

        LOGGER.error(message, ex);

        Error error = getError(status, message, ex, request);
        return new ResponseEntity<>(error, status);
    }

    @ExceptionHandler({RuntimeException.class})
    public ResponseEntity<Error> handleRuntimeException(Exception ex, ServletWebRequest request) {
        HttpStatus status = HttpStatus.INTERNAL_SERVER_ERROR;
        String message = getMessage(ex);

        LOGGER.error(message, ex);

        Error error = getError(status, message, ex, request);
        return new ResponseEntity<>(error, status);
    }

    public static io.wyden.published.common.Error wrap(Exception e, String path) {
        return io.wyden.published.common.Error.newBuilder()
            .setDateTime(DateUtils.toIsoUtcTime(ZonedDateTime.now()))
            .setStatusCode(400)
            .setCategory("BAD_REQUEST")
            .setMessage(getMessage(e))
            .setPath(path)
            .setException(getException(e))
            .build();
    }

    private Error getCause(HttpServerErrorException ex) {
        try {
            return objectMapper.reader().readValue(ex.getResponseBodyAsString(), Error.class);
        } catch (IOException e) {
            return null;
        }
    }
}
