package io.wyden.booking.interfaces.rest;

import io.wyden.booking.application.position.PositionFromProtoMapper;
import io.wyden.booking.application.position.PositionQueryService;
import io.wyden.booking.domain.position.PositionType;
import io.wyden.published.booking.PositionSearch;
import io.wyden.published.common.CursorConnection;
import org.slf4j.Logger;
import org.springframework.retry.annotation.Retryable;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import java.sql.SQLException;
import java.util.List;

import static org.slf4j.LoggerFactory.getLogger;
import static org.springframework.http.MediaType.APPLICATION_PROTOBUF_VALUE;

@RestController
public class PositionController {

    private static final Logger LOGGER = getLogger(PositionController.class);

    private final PositionQueryService positionQueryService;

    public PositionController(PositionQueryService positionQueryService) {
        this.positionQueryService = positionQueryService;
    }

    @Retryable(retryFor = SQLException.class)
    @PostMapping(value = "/positions/search", params = "!version", produces = APPLICATION_PROTOBUF_VALUE)
    public CursorConnection search(@RequestBody RequestModel.PositionSearch request) {
        LOGGER.info("Lookup positions requested for: {}. The final lookup will depend on user access rights", request);
        return positionQueryService.lookupPositions(request);
    }

    @Retryable(retryFor = SQLException.class)
    @PostMapping(value = "/positions/search", params = "version=proto", produces = APPLICATION_PROTOBUF_VALUE)
    public CursorConnection search(@RequestBody PositionSearch request) {
        LOGGER.info("Lookup positions requested for: {}. The final lookup will depend on user access rights", request);
        RequestModel.PositionSearch positionSearch = PositionFromProtoMapper.map(request);
        return positionQueryService.lookupPositions(positionSearch);
    }

    @Retryable(retryFor = SQLException.class)
    @PostMapping(value = "/internal/positions/search", params = "version=proto", produces = APPLICATION_PROTOBUF_VALUE)
    public CursorConnection internalSearch(@RequestBody PositionSearch request) {
        LOGGER.info("Internal lookup positions requested for: {}. The final lookup will depend on user access rights", request);
        RequestModel.PositionSearch positionSearch = PositionFromProtoMapper.map(request);
        RequestModel.PositionSearch effectiveSearch = withAllPermissions(positionSearch);
        return positionQueryService.lookupInternalPositions(effectiveSearch);
    }

    private static RequestModel.PositionSearch withAllPermissions(RequestModel.PositionSearch positionSearch) {
        RequestModel.PositionSearch effectiveSearch = positionSearch;
        if (positionSearch.accountId().isEmpty() && !positionSearch.portfolio().isEmpty()) {
            effectiveSearch = effectiveSearch.withAccountType(RequestModel.AccountType.NONE);
        }
        if (!positionSearch.accountId().isEmpty() && positionSearch.portfolio().isEmpty()) {
            effectiveSearch = effectiveSearch.withPortfolioType(RequestModel.PortfolioType.NONE);
        }
        if (positionSearch.accountId().isEmpty() && positionSearch.portfolio().isEmpty()) {
            effectiveSearch = effectiveSearch.withAccountType(RequestModel.AccountType.ALL).withPortfolioType(RequestModel.PortfolioType.ALL);
        }
        return effectiveSearch;
    }

    @GetMapping("/position-types")
    public List<String> positionTypes() {
        return PositionType.stringValues();
    }
}
