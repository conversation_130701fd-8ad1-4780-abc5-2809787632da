package io.wyden.booking.interfaces.rest;

import io.wyden.booking.application.rate.RateCache;
import io.wyden.booking.domain.rate.RatesModel;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RestController;

import static org.apache.commons.lang3.StringUtils.toRootUpperCase;

@RestController
public class RateController {

    private final RateCache rateCache;

    public RateController(RateCache rateCache) {
        this.rateCache = rateCache;
    }

    @GetMapping("/rates/{base}/{quote}")
    public RatesModel.Rate getPricing(@PathVariable("base") String base, @PathVariable("quote") String quote) {
        return rateCache.find(toRootUpperCase(base), toRootUpperCase(quote))
            .orElseThrow(() -> new IllegalArgumentException("Cannot find rate for %s / %s".formatted(base, quote)));
    }
}
