package io.wyden.booking.domain.position;

import io.wyden.booking.application.rate.RateCache;
import io.wyden.booking.domain.account.VenueAccountProvider;
import io.wyden.booking.domain.common.JpaRepository;
import io.wyden.booking.domain.instrument.Instrument;
import io.wyden.booking.domain.portfolio.PortfolioProvider;
import io.wyden.booking.domain.portfolio.SystemCurrencyProvider;
import io.wyden.booking.domain.rate.RateNotFoundException;
import io.wyden.booking.domain.rate.RatesModel;
import io.wyden.published.referencedata.Portfolio;
import io.wyden.published.referencedata.VenueAccount;
import io.wyden.published.referencedata.WalletType;

import jakarta.transaction.Transactional;
import org.slf4j.Logger;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.Optional;

import static org.apache.commons.lang3.StringUtils.isBlank;
import static org.slf4j.LoggerFactory.getLogger;

@Service
public class PositionReferenceService { // todo-md should not be visible outside of state manager package

    private static final Logger LOGGER = getLogger(PositionReferenceService.class);

    private final PositionReferenceRepository positionReferenceRepository;
    private final PositionReferenceStorageService positionReferenceStorageService;
    private final RateCache rateCache;
    private final PortfolioProvider portfolioProvider;
    private final VenueAccountProvider venueAccountProvider;

    public PositionReferenceService(PositionReferenceRepository positionReferenceRepository,
                                    PositionReferenceStorageService positionReferenceStorageService,
                                    RateCache rateCache,
                                    PortfolioProvider portfolioProvider,
                                    VenueAccountProvider venueAccountProvider) {
        this.positionReferenceRepository = positionReferenceRepository;
        this.positionReferenceStorageService = positionReferenceStorageService;
        this.rateCache = rateCache;
        this.portfolioProvider = portfolioProvider;
        this.venueAccountProvider = venueAccountProvider;
    }

    @Transactional(Transactional.TxType.REQUIRES_NEW) // TODO-md SM exclusive
    public PortfolioReference createPortfolio(String portfolioId) {
        if (isBlank(portfolioId)) {
            throw new IllegalArgumentException("Portfolio id cannot be blank");
        }

        Portfolio portfolio = portfolioProvider.getPortfolio(portfolioId);
        String currency = portfolio.getPortfolioCurrency();
        if (isBlank(currency)) {
            currency = SystemCurrencyProvider.getSystemCurrency();
            LOGGER.warn("Could not find portfolio currency for portfolioId {}, falling back to system currency: {}", portfolioId, currency);
        }

        PortfolioReference portfolioReference = new PortfolioReference(portfolioId, currency, portfolio.getPortfolioType().name());

        return JpaRepository.trySave(
            p -> positionReferenceRepository.findPortfolioById(p.getReferenceId()),
            positionReferenceStorageService::save,
            portfolioReference
        );
    }

    @Transactional(Transactional.TxType.REQUIRES_NEW) // todo-md SM exclusive
    public AccountReference createAccount(String accountId) {
        if (isBlank(accountId)) {
            throw new IllegalArgumentException("Account id cannot be blank");
        }

        String accountCurrency = SystemCurrencyProvider.getSystemCurrency();
        Optional<VenueAccount> venueAccount = venueAccountProvider.getVenueAccount(accountId);
        AccountReference account = new AccountReference(accountId, accountCurrency,
            venueAccount.<String>map(va -> va.getAccountType().name()).orElse(null),
            venueAccount.filter(va -> WalletType.WALLET_NOSTRO.equals(va.getWalletType()) || WalletType.WALLET_VOSTRO.equals(va.getWalletType()))
                .map(va -> va.getWalletType().name())
                .orElse(null));

        return JpaRepository.trySave(
            a -> positionReferenceRepository.findAccountById(a.getReferenceId()),
            positionReferenceStorageService::save,
            account
        );
    }

    public BigDecimal getRate(String symbol, String quoteCurrency) {
        return rateCache.find(symbol, quoteCurrency)
            .map(RatesModel.Rate::value)
            .orElseThrow(() -> new RateNotFoundException(symbol, quoteCurrency));
    }

    public BigDecimal getRate(Instrument instrument, GenericReference reference) {
        return getRate(instrument.getSymbol(), reference.getCurrency());
    }
}
