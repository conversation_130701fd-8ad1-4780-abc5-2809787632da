package io.wyden.booking.domain.settlement;

import io.micrometer.core.instrument.MeterRegistry;
import io.micrometer.core.instrument.Timer;
import io.opentelemetry.api.trace.SpanKind;
import io.wyden.booking.application.settlement.SettlementStateInput;
import io.wyden.booking.application.state.StateOutputBuilder;
import io.wyden.booking.domain.ledgerentry.LedgerEntry;
import io.wyden.booking.domain.position.Position;
import io.wyden.booking.domain.transaction.Transaction;
import io.wyden.cloudutils.telemetry.Telemetry;
import io.wyden.cloudutils.telemetry.metrics.EmptyTimer;
import io.wyden.cloudutils.telemetry.tracing.Tracing;
import org.slf4j.Logger;
import org.springframework.stereotype.Service;

import java.time.ZonedDateTime;
import java.util.Collection;
import java.util.Map;
import java.util.Optional;

import static io.wyden.booking.infrastructure.telemetry.Meters.bookingSettlementLatencyTimer;
import static io.wyden.cloudutils.telemetry.metrics.LatencyRecorder.recordLatencyIn;
import static org.slf4j.LoggerFactory.getLogger;

@Service
public class SettlementEngine {

    private static final Logger LOGGER = getLogger(SettlementEngine.class);

    private final Tracing otlTracing;
    private final MeterRegistry meterRegistry;

    public SettlementEngine(Telemetry telemetry) {
        this.otlTracing = telemetry.getTracing();
        this.meterRegistry = telemetry.getMeterRegistry();
    }

    public StateOutputBuilder settle(Collection<String> executionIds, ZonedDateTime settlementDateTime, SettlementStateInput stateInput) {
        try (var ignored = otlTracing.createSpan("booking.command.processing.settle", SpanKind.CLIENT)) {
            return recordLatencyIn(latencyTimer()).of(() -> settleInner(executionIds, settlementDateTime, stateInput));
        }
    }

    private StateOutputBuilder settleInner(Collection<String> executionIds, ZonedDateTime settlementDateTime, SettlementStateInput stateInput) {
        StateOutputBuilder resultBuilder = new StateOutputBuilder();
        ZonedDateTime finalSettlementDateTime = settlementDateTime != null ? settlementDateTime : ZonedDateTime.now();

        for (String executionId : executionIds) {
            Optional<Transaction> optionalTransaction = stateInput.findTransactionByExecutionId(executionId);
            if (optionalTransaction.isEmpty()) {
                LOGGER.info("Transaction {} could not be found in repository", executionId);
                continue;
            }

            Transaction transaction = optionalTransaction.get();
            if (transaction.isSettled()) {
                LOGGER.debug("Transaction {} is already settled, skipping", executionId);
                continue;
            }

            Collection<LedgerEntry> ledgerEntries = stateInput.findLedgerEntries(executionId)
                .stream()
                .filter(ledgerEntry -> !ledgerEntry.isSettled() && ledgerEntry.getType().isNonReservationOrRelease() && ledgerEntry.getQuantity() != null)
                .toList();

            settleLedgerEntries(ledgerEntries, stateInput, resultBuilder);

            transaction.setSettled(true);
            transaction.setSettledDateTime(finalSettlementDateTime);
            resultBuilder.addTransaction(transaction);
        }
        LOGGER.trace("affectedLedgerEntries: {}", resultBuilder);
        return resultBuilder;
    }

    private void settleLedgerEntries(Collection<LedgerEntry> ledgerEntries, SettlementStateInput stateInput, StateOutputBuilder resultBuilder) {
        Map<LedgerEntry, Position> positionsForLedgerEntries = stateInput.getPositionsForLedgerEntries(ledgerEntries);
        positionsForLedgerEntries.forEach((ledgerEntry, position) -> {
            LOGGER.info("Settling ledger entry: {} against position: {}", ledgerEntry, position);
            position.settle(ledgerEntry.getQuantity());
            resultBuilder.addPosition(position);
            ledgerEntry.setSettled(true);
            resultBuilder.addLedgerEntry(ledgerEntry);
        });
    }

    private Timer latencyTimer() {
        try {
            return bookingSettlementLatencyTimer(meterRegistry);
        } catch (Exception e) {
            LOGGER.warn("Unable to create latency timer", e);
            return EmptyTimer.create();
        }
    }
}
