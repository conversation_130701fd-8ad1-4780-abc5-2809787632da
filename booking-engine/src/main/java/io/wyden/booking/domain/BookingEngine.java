package io.wyden.booking.domain;

import io.opentelemetry.api.trace.SpanKind;
import io.wyden.booking.application.state.BookingStateInput;
import io.wyden.booking.application.state.StateOutputBuilder;
import io.wyden.booking.domain.ledgerentry.LedgerEntry;
import io.wyden.booking.domain.ledgerentry.LedgerEntryReducer;
import io.wyden.booking.domain.ledgerentry.LedgerEntryType;
import io.wyden.booking.domain.ledgerentry.RawLedgerEntry;
import io.wyden.booking.domain.ledgerentry.SimpleReference;
import io.wyden.booking.domain.position.Position;
import io.wyden.booking.domain.rate.PricingService;
import io.wyden.booking.domain.transaction.TransactionFeeEntity;
import io.wyden.cloudutils.telemetry.tracing.Tracing;
import org.slf4j.Logger;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Comparator;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Queue;

import static io.wyden.cloudutils.tools.BigDecimalUtils.isNonZero;
import static io.wyden.cloudutils.tools.BigDecimalUtils.isNullOrZero;
import static io.wyden.cloudutils.tools.BigDecimalUtils.isZero;
import static java.math.BigDecimal.ZERO;
import static org.slf4j.LoggerFactory.getLogger;

/**
 * Booking Engine
 * - Responsible for applying transaction logic (i.e., generated LedgerEntry objects) to positions.
 * - Tracks both AssetPositions and CashPositions.
 * - Stateless:
 * - Accepts current state (relevant Positions, LedgerEntries, open Reservation, etc.) and Command to mutate state
 * - Returns updated state to be saved
 */
@Service
public class BookingEngine {

    private static final Logger LOGGER = getLogger(BookingEngine.class);

    private final PricingService pricingService;

    private final Tracing otlTracing;

    public BookingEngine(PricingService pricingService,
                         Tracing otlTracing) {
        this.pricingService = pricingService;
        this.otlTracing = otlTracing;
    }

    public StateOutputBuilder book(Collection<RawLedgerEntry> rawEntriesToApply, BookingStateInput stateInput) {
        try (var ignored = otlTracing.createSpan("booking.command.processing.engine.book", SpanKind.INTERNAL)) {
            return bookInner(rawEntriesToApply, stateInput);
        }
    }

    private record Task(RawLedgerEntry rawLedgerEntry, Position position) {}

    private StateOutputBuilder bookInner(Collection<RawLedgerEntry> rawEntriesToApply, BookingStateInput stateInput) {
        StateOutputBuilder resultBuilder = new StateOutputBuilder();

        // Merge ledger entries of the same identifier (instrument, reference, type) by summing quantity
        // This simplifies processing and makes sure multiple reservations (reservation for quantity + reservation for fee) are not skipped
        List<RawLedgerEntry> aggregatedEntries = LedgerEntryReducer.reduce(rawEntriesToApply).toList();

        Map<RawLedgerEntry, Position> tasks = stateInput.getPositionsForLedgerEntries(aggregatedEntries);
        Queue<Task> todo = new LinkedList<>(tasks.entrySet().stream()
            .map(e -> new Task(e.getKey(), e.getValue()))
            .sorted(Comparator.comparing(e -> e.rawLedgerEntry.getType()))
            .toList());

        List<LedgerEntry> reservationsCreatedInThisRun = new ArrayList<>();
        while (!todo.isEmpty()) {
            // process
            var task = todo.poll();
            RawLedgerEntry rawLedgerEntry = task.rawLedgerEntry();
            Position position = task.position();

            LOGGER.debug("Processing raw ledger entry: {}", rawLedgerEntry);

            Optional<LedgerEntry> processedLedgerEntry = process(rawLedgerEntry, reservationsCreatedInThisRun, stateInput);
            processedLedgerEntry.ifPresentOrElse(ledgerEntry -> {
                    LOGGER.debug("Parsed ledger entry: {}", ledgerEntry);

                    // apply to position
                    List<RawLedgerEntry> additionalEntries = applyToPosition(ledgerEntry, position);

                    resultBuilder.addLedgerEntry(ledgerEntry);
                    if (ledgerEntry.getType().isReservationOrRelease()) {
                        // next LEs from the batch will need it for computations (aside from StateInput data)
                        reservationsCreatedInThisRun.add(ledgerEntry);
                    }

                    resultBuilder.addPosition(position);

                    // add to post-processing, if necessary
                    if (!additionalEntries.isEmpty()) {
                        StateOutputBuilder additionalEntriesResult = book(additionalEntries, stateInput);
                        resultBuilder.merge(additionalEntriesResult);
                    }
                },
                () -> LOGGER.debug("No ledger entry to apply"));
        }

        return resultBuilder;
    }

    private Optional<LedgerEntry> process(RawLedgerEntry rawLedgerEntry, List<LedgerEntry> reservationsCreatedInThisRun, BookingStateInput stateInput) {
        try (var ignored = otlTracing.createSpan("booking.command.processing.processledgerentry." + rawLedgerEntry.getType().name(), SpanKind.CLIENT)) {
            return processInternal(rawLedgerEntry, reservationsCreatedInThisRun, stateInput);
        }
    }

    private Optional<LedgerEntry> processInternal(RawLedgerEntry rawLedgerEntry, List<LedgerEntry> reservationsCreatedInThisRun, BookingStateInput stateInput) {
        SimpleReference key = asSimpleReference(rawLedgerEntry);

        // Generate all necessary (unprocessed) RawLedgerEntries by calling getLedgerEntries of the Transaction object.
        //
        // Pre-process RawLedgerEntries and create (processed) LedgerEntries
        // - in case price is null (e.g., Deposit, Withdrawal), retrieve the exchange rate from the PricingService and apply it to the LedgerEntry
        // - in case the feeCurrency does not match the currency of the Position. Convert the fee amount to the fee amount in Position currency by applying the conversion rate retrieved from the PricingService.
        //
        // Lookup the position by Position or Account and Currency (for CashPositions) or Security (for AssetPositions)
        //
        // Apply the LedgerEntry onto the Position by calling applyLedgerEntry

        if (rawLedgerEntry.getReference() == null) {
            // might be null in case of empty optional feePortfolioId / feeAccountId
            return Optional.empty();
        }

        BigDecimal quantity = rawLedgerEntry.getQuantity();
        String reservationRef = rawLedgerEntry.getReservationRef();

        if (rawLedgerEntry.getType() == LedgerEntryType.RESERVATION_RELEASE_REMAINING) {
            // this will release all remaining pending quantity,
            BigDecimal pendingAmount = stateInput.getPendingAmount(rawLedgerEntry, reservationRef);
            BigDecimal pendingAmount2 = reservationsCreatedInThisRun.stream()
                // reference.getAccountId(), reference.getPortfolioId(), instrument.getSymbol(), reservationRef
                .filter(r -> asSimpleReference(r).equals(key))
                .map(LedgerEntry::getQuantity)
                .reduce(ZERO, BigDecimal::add);
            BigDecimal total = pendingAmount.add(pendingAmount2);

            if (isNullOrZero(total)) {
                return Optional.empty();
            }

            quantity = total.negate();
        }

        if (rawLedgerEntry.getType() == LedgerEntryType.RESERVATION_RELEASE) {
            // this will release reservations up to the limit of pending quantity
            BigDecimal pendingAmount = stateInput.getPendingAmount(rawLedgerEntry, reservationRef);

            if (isZero(pendingAmount)) {
                LOGGER.debug("No need to release {} {}. There is nothing to release left.", rawLedgerEntry.getQuantity(), rawLedgerEntry.getInstrument());
                return Optional.empty();
            }

//            same as below
//            quantity = isNegative(pendingAmount) ? MathUtils.min(pendingAmount.negate(), quantity)
//                : MathUtils.max(pendingAmount.negate(), quantity);

            BigDecimal remainingReservations = pendingAmount.negate();
            if (remainingReservations.abs().compareTo(quantity.abs()) < 0) {
                LOGGER.debug("Reservation release is smaller than expected {} {} vs {} {}. No need to release more than that.",
                    remainingReservations, rawLedgerEntry.getInstrument(), rawLedgerEntry.getQuantity(), rawLedgerEntry.getInstrument());
                quantity = remainingReservations;
            }
        }

        BigDecimal fee = getFee(rawLedgerEntry);
        BigDecimal price = getPrice(rawLedgerEntry);

        LedgerEntry ledgerEntry = new LedgerEntry(
            quantity,
            price,
            fee,
            rawLedgerEntry.getType(),
            rawLedgerEntry.getReference(),
            rawLedgerEntry.getInstrument(),
            rawLedgerEntry.getReservationRef(),
            rawLedgerEntry.getTransactionId(),
            rawLedgerEntry.isSettled()
        );

        return Optional.of(ledgerEntry);
    }

    private SimpleReference asSimpleReference(LedgerEntry ledgerEntry) {
        return new SimpleReference(ledgerEntry.getReference(), ledgerEntry.getInstrument());
    }

    private SimpleReference asSimpleReference(RawLedgerEntry rawLedgerEntry) {
        return new SimpleReference(rawLedgerEntry.getReference(), rawLedgerEntry.getInstrument());
    }

    /**
     * LedgerEntry -> (apply to position) -> LedgerEntry
     */
    private List<RawLedgerEntry> applyToPosition(LedgerEntry ledgerEntry, Position position) {
        try (var ignored = otlTracing.createSpan("booking.command.processing.applyledgerentry", SpanKind.CLIENT)) {
            // Perform potential post-processing:
            // - applying AssetPosition grossNewRealizedPnL to CashPosition when reducing or closing AssetPositions.
            // - store the current “qty” of adjusted Positions in the LedgerEntry balance field

            ledgerEntry.setBalanceBefore(position.getQuantity());
            LOGGER.info("Before applying ledger entry: {} to position: {}", ledgerEntry, position);

            List<RawLedgerEntry> additionalLedgerEntries = position.applyLedgerEntry(ledgerEntry);
            ledgerEntry.setBalanceAfter(position.getQuantity());
            LOGGER.info(" After applying ledger entry: {} to position: {}", ledgerEntry, position);

            if (!additionalLedgerEntries.isEmpty()) {
                LOGGER.debug("Found additional ledger entries to apply: {}", additionalLedgerEntries);
            }

            return additionalLedgerEntries;
        }
    }

    private BigDecimal getPrice(RawLedgerEntry rawLedgerEntry) {
        BigDecimal price = rawLedgerEntry.getPrice();
        if (price != null && isNonZero(price)) {
            return price;
        }

        if (price != null && isNullOrZero(rawLedgerEntry.getQuantity())) {
            return price;
        }

        String positionCurrency = rawLedgerEntry.getInstrument().getSymbol();
        String portfolioCurrency = rawLedgerEntry.getReference().getCurrency();
        return pricingService.getMarketPrice(positionCurrency, portfolioCurrency);
    }

    private BigDecimal getFee(RawLedgerEntry rawLedgerEntry) {
        Collection<? extends TransactionFeeEntity> fees = rawLedgerEntry.getFees();

        if (fees.isEmpty()) {
            return ZERO;
        }

        return fees.stream()
            .map(fee -> toAmountInPortfolioCurrency(fee, rawLedgerEntry.getCurrency()))
            .reduce(BigDecimal.ZERO, BigDecimal::add);
    }

    private BigDecimal toAmountInPortfolioCurrency(TransactionFeeEntity fee, String bookingCurrency) {
        if (isNullOrZero(fee.getAmount())) {
            return ZERO;
        }

        String feeCurrency = fee.getCurrency().getSymbol();
        if (bookingCurrency.equals(feeCurrency)) {
            return fee.getAmount();
        }

        BigDecimal exchangeRate = pricingService.getMarketPrice(feeCurrency, bookingCurrency);
        return fee.getAmount().multiply(exchangeRate);
    }
}
