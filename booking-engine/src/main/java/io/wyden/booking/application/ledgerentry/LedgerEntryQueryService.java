package io.wyden.booking.application.ledgerentry;

import io.wyden.booking.application.security.SearchRequestAuthorizer;
import io.wyden.booking.domain.ledgerentry.EntryReference;
import io.wyden.booking.domain.ledgerentry.LedgerEntry;
import io.wyden.booking.domain.ledgerentry.LedgerEntryRepository;
import io.wyden.booking.domain.ledgerentry.LedgerEntryType;
import io.wyden.booking.domain.ledgerentry.SimpleReference;
import io.wyden.booking.interfaces.rest.RequestModel;
import io.wyden.booking.interfaces.rest.RequestModel.LedgerEntrySearch;
import io.wyden.booking.utils.DebugUtils;
import io.wyden.cloud.utils.rest.pagination.PaginationWrapper;
import io.wyden.published.common.CursorConnection;
import io.wyden.published.common.CursorNode;
import org.slf4j.Logger;
import org.springframework.stereotype.Service;

import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import static io.wyden.booking.application.ledgerentry.LedgerEntryToProtoMapper.map;
import static io.wyden.booking.interfaces.rest.PaginationModel.empty;
import static org.slf4j.LoggerFactory.getLogger;

/**
 * Performs a DB query.
 * Keep in mind that a DB query will only be eventually consistent
 * since all real-time state changes happen in-memory and state is dumped to the DB periodically.
 */
@Service
public class LedgerEntryQueryService {

    private static final Logger LOGGER = getLogger(LedgerEntryQueryService.class);

    private final LedgerEntryRepository ledgerEntryRepository;
    private final SearchRequestAuthorizer searchRequestAuthorizer;

    public LedgerEntryQueryService(LedgerEntryRepository ledgerEntryRepository,
                                   SearchRequestAuthorizer searchRequestAuthorizer) {
        this.ledgerEntryRepository = ledgerEntryRepository;
        this.searchRequestAuthorizer = searchRequestAuthorizer;
    }

    public Map<SimpleReference, List<LedgerEntry>> groupByReservationRef(String reservationRef) {
        // find all ledger entries for reservationRef
        RequestModel.LedgerEntrySearch search = RequestModel.LedgerEntrySearch.justOrderId(reservationRef);
        Collection<LedgerEntry> ledgerEntries = ledgerEntryRepository.findByProperties(search);

        // group them by portfolio (or account) + instrument
        return ledgerEntries.stream()
            .collect(Collectors.groupingBy(SimpleReference::ofReference));
    }

    public Collection<LedgerEntry> findReservationsByReferenceAndReservationRef(EntryReference reference, String reservationRef) {
        List<String> ledgerEntryTypes = LedgerEntryType.getReservationOrReleaseTypes().stream()
            .map(LedgerEntryType::name)
            .toList();

            RequestModel.LedgerEntrySearch search = LedgerEntrySearch.entryReference(reference)
                .withLedgerEntryTypes(ledgerEntryTypes)
                .withReservationRef(reservationRef);

            return ledgerEntryRepository.findByProperties(search);
    }

    public CursorConnection lookupByProperties(LedgerEntrySearch request) {
        RequestModel.SearchRequest authorizedRequest = searchRequestAuthorizer.authorize(request);
        if (authorizedRequest == null) {
            return empty();
        }

        return doLookup((LedgerEntrySearch) authorizedRequest);
    }

    private CursorConnection doLookup(LedgerEntrySearch request) {
        LOGGER.info("Lookup ledger entries by ({})", request);

        Collection<LedgerEntry> ledgerEntries = ledgerEntryRepository.findByProperties(request);
        long total = ledgerEntryRepository.countByProperties(request);
        long remaining = ledgerEntryRepository.countByProperties(request, request.after());

        LOGGER.info(DebugUtils.logFinishedLedgerEntryLookup(ledgerEntries));

        return PaginationWrapper.wrapToProto(
            ledgerEntries,
            ledgerEntry -> String.valueOf(ledgerEntry.getId()),
            ledgerEntry -> CursorNode.newBuilder().setLedgerEntry(map(ledgerEntry)).build(),
            remaining,
            total);
    }

}
