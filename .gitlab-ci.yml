include:
  - project: infrastructure/components
    ref: main
    file: components.yml
  - template: Security/SAST.gitlab-ci.yml

image: 739275467562.dkr.ecr.eu-central-1.amazonaws.com/dockerhub/library/maven:3.8.4-eclipse-temurin-17

variables:
  IMAGE_NAME: order-collider
  # Used in the deploy stage (see the components repo)
  RELEASE_NAME: order-collider

stages:
  - version
  - test
  - build
  - publish
  - scan
  - deploy
  - tag
  - qa

version-verify:
  extends: .pipeline-verification
  stage: version

changes-verify:
  extends: .verify-locked-files
  stage: version

version-check-domain:
  stage: version
  interruptible: true
  rules:
    - if: $CI_PIPELINE_SOURCE == "merge_request_event" && $CI_MERGE_REQUEST_TARGET_BRANCH_NAME == $CI_DEFAULT_BRANCH
      changes:
        - domain/version.properties
      when: never
      allow_failure: false
    - if: $CI_PIPELINE_SOURCE == "merge_request_event" && $CI_MERGE_REQUEST_TARGET_BRANCH_NAME == $CI_DEFAULT_<PERSON>ANCH
      changes:
        - domain/src/**/*
      when: always
      allow_failure: false
    - when: never
  script:
    - echo "Version not updated. Please update version.properties file"
    - exit 1

semgrep-sast:
  rules:
    - if: $CI_COMMIT_TAG
      when: never

sonarqube-check:
  stage: test
  interruptible: true
  services:
    - name: docker:20.10-dind-rootless
      command: ["--tls=false"]
  variables:
    DOCKER_HOST: tcp://docker:2375
    DOCKER_TLS_CERTDIR: ""
    DOCKER_DRIVER: overlay2
    TESTCONTAINERS_RYUK_DISABLED: "true"
    # SONAR_USER_HOME: "${CI_PROJECT_DIR}/.sonar" # Defines the location of the analysis task cache
    # GIT_DEPTH: "0" # Tells git to fetch all the branches of the project, required by the analysis task
    MAVEN_OPTS: "-Xmx2048m -XX:MaxPermSize=1024m"
  script:
    - ./gradlew sonarqube --stacktrace
  allow_failure: true
  only:
    - main
  except:
    - schedules

unit-test:
  stage: test
  extends: .unit_test
  variables:
    REPORTS_BASE_DIRECTORY: "collider/"
  except:
    - tags
    - schedules

integration-test:
  stage: test
  extends: .integration_test
  variables:
    REPORTS_BASE_DIRECTORY: "collider/"
  except:
    - tags
    - schedules

build:
  stage: build
  interruptible: true
  script:
    - ./gradlew assemble
  artifacts:
    paths:
      - collider/build/libs/
      - domain/build/libs/
    expire_in: 1 days
  except:
    - tags
    - schedules

publish-domain-lib:
  stage: publish
  interruptible: true
  only:
    - main
  script:
    - cd domain
    - ../gradlew publish
  dependencies:
    - build
  except:
    - tags
    - schedules

publish:
  extends:
    - .publish_image
  variables:
    DOCKERFILE_PATH: collider/Dockerfile
    DOCKER_BUILD_CONTEXT: collider/.
  dependencies:
    - build
  only:
    - main
  except:
    - schedules

container-scan:
  extends:
    - .docker_scan
  dependencies: []
  artifacts:
    when: always
    paths:
      - ./results-container.html
    expire_in: 1 days
  allow_failure: true
  only:
    - main

retag:
  extends:
    - .retag_image
  stage: publish
  dependencies: []
  only:
    - tags

audit:
  extends:
    - .audit_trails
  stage: publish
  only:
    - tags

deploy-dev:
  extends:
    - .deploy_helm
  only:
    - main
  except:
    - schedules

tag_rc:
  extends: .tag_repository
  stage: tag
  only:
    - main
  except:
    - schedules

tag_rc_scheduled:
  extends: .tag_repository_scheduled
  stage: tag
