include:
  - project: infrastructure/components
    ref: main
    file: components.yml

image: maven:3.8.4-eclipse-temurin-17

stages:
  - version
  - test
  - build
  - deploy
  - tag

MEC_version-verify:
  variables:
    VERSION_FILE: "build.gradle"
    LIBRARY: "matching-engine-collections"
  extends: .lib-verification
  stage: version

MEC_build:
  stage: build
  script:
    - echo "Build stage"
    - ./gradlew clean build
  artifacts:
    paths:
      - build/libs/
    expire_in: 1 week
  except:
    - tags

MEC_deploy_snapshot:
  stage: deploy
  variables:
    BUILD_VERSION: "-SNAPSHOT"
  script:
    - echo "Running task 'publishMavenJavaPublicationToNexus-snapshotsRepository'"
    - ./gradlew publishMavenJavaPublicationToNexus-snapshotsRepository
  only:
    - main
  needs:
    - job: MEC_build
      artifacts: true

MEC_tag:
  variables:
    VERSION_FILE: "build.gradle"
    LIBRARY: "matching-engine-collections"
  extends: .tag_library
  stage: tag
  only:
    - main
  except:
    - schedules
  needs:
    - job: MEC_deploy_snapshot

MEC_unit_test:
  stage: test
  interruptible: true
  script:
    - ./gradlew test