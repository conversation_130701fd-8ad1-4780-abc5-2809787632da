include:
  - project: infrastructure/components
    ref: main
    file: components.yml
  - template: Security/SAST.gitlab-ci.yml

image: 739275467562.dkr.ecr.eu-central-1.amazonaws.com/dockerhub/library/maven:3.8.4-eclipse-temurin-17

variables:
  IMAGE_NAME: exchange-simulator
  # Used in the deploy stage (see the components repo)
  RELEASE_NAME: exchange-simulator
  #For submodule
  GIT_SUBMODULE_STRATEGY: recursive
  GIT_SUBMODULE_UPDATE_FLAGS: --remote
  GIT_SUBMODULE_DEPTH: 0

stages:
  - version
  - test
  - build
  - publish
  - scan
  - deploy
  - tag
  - qa

version-verify:
  extends: .pipeline-verification
  stage: version

changes-verify:
  extends: .verify-locked-files
  stage: version

semgrep-sast:
  rules:
    - if: $CI_COMMIT_TAG
      when: never

sonarqube-check:
  stage: test
  interruptible: true
  variables:
    SONAR_USER_HOME: "${CI_PROJECT_DIR}/.sonar" # Defines the location of the analysis task cache
    GIT_DEPTH: "0" # Tells git to fetch all the branches of the project, required by the analysis task
  # cache:
  #   key: "${CI_JOB_NAME}"
  #   paths:
  #     - .sonar/cache
  script:
    - ./gradlew sonar --stacktrace
  allow_failure: true
  only:
    - main
  except:
    - schedules
  tags:
    - kubernetes-aws

unit-test:
  stage: test
  interruptible: true
  script:
    - ./gradlew test
    - awk -F"," '{ instructions += $4 + $5; covered += $5 } END { print 100*covered/instructions, "% covered" }'
      build/reports/jacoco/test/jacocoTestReport.csv
  artifacts:
    when: always
    reports:
      junit:
        - build/test-results/test/**/TEST-*.xml
      coverage_report:
        coverage_format: cobertura
        path: build/reports/jacoco/test/cobertura.xml
    expire_in: 1 days
  coverage: '/(\d+.\d+ \%) covered/'
  except:
    - tags
    - schedules
  tags:
    - kubernetes-aws

integration-test:
  stage: test
  interruptible: true
  services:
    - name: docker:20.10-dind-rootless
      command: ["--tls=false"]
  variables:
    DOCKER_HOST: tcp://docker:2375
    DOCKER_TLS_CERTDIR: ""
    DOCKER_DRIVER: overlay2
    TESTCONTAINERS_RYUK_DISABLED: "true"
  script:
    - ./gradlew integrationTest
    - awk -F"," '{ instructions += $4 + $5; covered += $5 } END { print 100*covered/instructions, "% covered" }'
      build/reports/jacoco/test/jacocoTestReport.csv
  artifacts:
    when: always
    reports:
      junit:
        - build/test-results/integrationTest/**/TEST-*.xml
      coverage_report:
        coverage_format: cobertura
        path: build/reports/jacoco/test/cobertura.xml
    expire_in: 1 days
  coverage: '/(\d+.\d+ \%) covered/'
  except:
    - tags
    - schedules
  tags:
    - kubernetes-aws

build:
  stage: build
  interruptible: true
  script:
    - ./gradlew assemble
  cache: &artifact_cache
    key: "$CI_COMMIT_SHA"
    paths:
      - build/libs/
    policy: push
  except:
    - tags
    - schedules
  tags:
    - kubernetes-aws

publish:
  extends:
    - .publish_image
  stage: publish
  dependencies:
    - build
  cache:
    <<: *artifact_cache
    policy: pull
  only:
    - main
  except:
    - schedules

container-scan:
  extends:
    - .docker_scan
  dependencies: []
  artifacts:
    when: always
    paths:
      - ./results-container.html
    expire_in: 1 days
  allow_failure: true
  tags:
    - kubernetes-aws
  only:
    - main

retag:
  extends:
    - .retag_image
  stage: publish
  dependencies: []
  rules:
    - if: $CI_COMMIT_TAG =~ /^[0-9]+\.[0-9]+\.[0-9]+\.[0-9]+$/
      when: always

release_image:
  interruptible: true
  stage: publish
  variables:
    DEV_IMAGE_PATH: "cloud/dev"
    RELEASE_IMAGE_PATH: "cloud/release"
    ARCHITECTURES: "arm64 amd64"
    DOCKERFILE_PATH: Dockerfile
    IMAGE_TAG: $CI_COMMIT_SHORT_SHA
    CONTAINERS_SHORT_NAME_ALIASING: "on"
    DOCKER_BUILD_CONTEXT: "."
  image:
    name: $CRANE_IMAGE
    entrypoint: [""]
  script:
    - crane auth login -u ${NEXUS_USER} -p ${NEXUS_PASSWORD} ${DOCKER_IMAGE_REPOSITORY}
    - crane cp ${DOCKER_IMAGE_REPOSITORY}/${DEV_IMAGE_PATH}/${IMAGE_NAME}:${CI_COMMIT_SHORT_SHA} ${DOCKER_IMAGE_REPOSITORY}/${RELEASE_IMAGE_PATH}/${IMAGE_NAME}:${CI_COMMIT_TAG}
    - crane cp ${DOCKER_IMAGE_REPOSITORY}/${DEV_IMAGE_PATH}/${IMAGE_NAME}:${CI_COMMIT_SHORT_SHA} ${DOCKER_IMAGE_REPOSITORY}/${RELEASE_IMAGE_PATH}/${IMAGE_NAME}:latest
  tags:
    - image-builder
  rules:
    - if: $CI_COMMIT_TAG =~ /^[0-9]+\.[0-9]+\.[0-9]+$/
      when: always
    - when: never


.deploy_exchange_helm:
  image: $HELM_KUBECTL_IMAGE
  stage: deploy
  variables:
    IMAGE_TAG: $CI_COMMIT_SHORT_SHA
    ENVIRONMENT: "dev"
    NAMESPACE: "wyden-${ENVIRONMENT}"
  script:
    - helm secrets upgrade --install --namespace ${NAMESPACE} ${RELEASE_NAME} ./env-values -f ./env-values/secret.${ENVIRONMENT}-aws.yaml -f ./env-values/values-${ENVIRONMENT}-aws.yaml --set image.tag=${IMAGE_TAG} --timeout 20m
  needs:
    - job: publish
      artifacts: false
  tags:
    - kubernetes-aws


deploy_qa:
  variables:
    ENVIRONMENT: "qa"
  extends:
    - .deploy_exchange_helm
  only:
    - main
  except:
    - schedules

deploy_performance:
  variables:
    ENVIRONMENT: "performance"
  extends:
    - .deploy_exchange_helm
  when: manual
  only:
    - main
  except:
    - schedules

deploy_og:
  image: $HELM_KUBECTL_IMAGE
  stage: deploy
  script:
    - helm repo add --username $NEXUS_USER --password $NEXUS_PASSWORD wyden https://repo.wyden.io/nexus/repository/wyden/
    - helm repo update
    - helm secrets upgrade --install exchange-simulator wyden/wyden-exchange-simulator -f ./env-values/secret.prod-aws.yaml -f ./env-values/values-prod-aws.yaml --set image.tag=${CI_COMMIT_TAG} --namespace wyden-og --timeout 20m
  rules:
    - if: $CI_COMMIT_TAG =~ /^[0-9]+\.[0-9]+\.[0-9]+$/
      when: on_success
    - when: never
  allow_failure: true
  tags:
    - kubernetes-aws

tag_rc:
  extends: .tag_repository
  stage: tag
  only:
    - main
  except:
    - schedules

tag_rc_scheduled:
  extends: .tag_repository_scheduled
  stage: tag
