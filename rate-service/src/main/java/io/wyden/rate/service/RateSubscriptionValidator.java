package io.wyden.rate.service;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;
import org.springframework.web.server.ResponseStatusException;

import static org.apache.commons.lang3.StringUtils.isBlank;

@Service
public class RateSubscriptionValidator {

    private static final Logger LOGGER = LoggerFactory.getLogger(RateSubscriptionValidator.class);

    private final RateSubscriptionRepository rateSubscriptionRepository;

    public RateSubscriptionValidator(RateSubscriptionRepository rateSubscriptionRepository) {
        this.rateSubscriptionRepository = rateSubscriptionRepository;
    }

    public void validateCreate(String baseCurrency, String quoteCurrency) {
        validateNotBlank(baseCurrency, quoteCurrency);

        if (rateSubscriptionRepository.exists(baseCurrency, quoteCurrency)) {
            String log = "Rate subscription for baseCurrency: %s, quoteCurrency: %s already exists".formatted(baseCurrency, quoteCurrency);
            LOGGER.info(log);
            throw new ResponseStatusException(HttpStatus.CONFLICT, log);
        }
    }

    public void validateDelete(String baseCurrency, String quoteCurrency) {
        validateNotBlank(baseCurrency, quoteCurrency);

        if (!rateSubscriptionRepository.exists(baseCurrency, quoteCurrency)) {
            String log = "Rate subscription for baseCurrency: %s, quoteCurrency: %s does not exist".formatted(baseCurrency, quoteCurrency);
            LOGGER.info(log);
            throw new ResponseStatusException(HttpStatus.NOT_FOUND, log);
        }
    }

    private static void validateNotBlank(String baseCurrency, String quoteCurrency) {
        if (isBlank(baseCurrency) || isBlank(quoteCurrency)) {
            throw new ResponseStatusException(HttpStatus.NOT_FOUND, "Input parameters are empty");
        }
    }
}
