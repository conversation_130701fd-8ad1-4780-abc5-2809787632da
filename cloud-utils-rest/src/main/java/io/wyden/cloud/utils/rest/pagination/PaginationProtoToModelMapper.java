package io.wyden.cloud.utils.rest.pagination;

import io.wyden.published.common.CursorConnection;
import io.wyden.published.common.CursorNode;
import io.wyden.published.common.PageInfo;

import java.io.Serializable;
import java.util.List;
import java.util.function.Function;

public final class PaginationProtoToModelMapper {

    private PaginationProtoToModelMapper() {
    }

    /**
     * Map proto pagination to raw pagination model
     */
    public static <T extends Serializable> PaginationModel.CursorConnection<T> map(CursorConnection connection, Function<CursorNode, T> protoToModel) {
        if (connection == null) {
            return PaginationModel.emptyCursorConnection();
        }

        List<PaginationModel.CursorEdge<T>> cursorEdge = connection.getEdgesList().stream()
            .map(securityEdge -> {
                T node = protoToModel.apply(securityEdge.getNode());
                return new PaginationModel.CursorEdge<>(node, securityEdge.getCursor());
            })
            .toList();

        PaginationModel.PageInfo pageInfo = getPageInfo(connection.getPageInfo());
        return new PaginationModel.CursorConnection<>(cursorEdge, pageInfo);
    }

    public static PaginationModel.PageInfo getPageInfo(PageInfo pageInfo) {
        String endCursor = pageInfo.getEndCursor();
        boolean hasNextPage = pageInfo.getHasNextPage();
        long pageSize = pageInfo.getPageSize();
        Long totalSize = getTotalSize(pageInfo);

        return new PaginationModel.PageInfo(hasNextPage, endCursor, pageSize, totalSize);
    }

    private static Long getTotalSize(PageInfo pageInfo) {
        if (pageInfo.getTotalSizeStatus() == PageInfo.TotalSizeStatus.SIZE_UNKNOWN) {
            return null;
        }

        return pageInfo.getTotalSize();
    }
}
