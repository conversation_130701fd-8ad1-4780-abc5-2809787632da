<mxfile host="app.diagrams.net" agent="Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36" version="26.0.16">
  <diagram id="kgpKYQtTHZ0yAKxKKP6v" name="Page-1">
    <mxGraphModel dx="4037" dy="1086" grid="1" gridSize="10" guides="1" tooltips="1" connect="1" arrows="1" fold="1" page="1" pageScale="1" pageWidth="850" pageHeight="1100" background="#FFFFFF" math="0" shadow="0">
      <root>
        <mxCell id="0" />
        <mxCell id="1" parent="0" />
        <mxCell id="6bjWbGc7v3m6aFxhvqW1-86" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=0;entryY=0.908;entryDx=0;entryDy=0;entryPerimeter=0;exitX=1;exitY=0.75;exitDx=0;exitDy=0;" parent="1" source="6bjWbGc7v3m6aFxhvqW1-22" target="6bjWbGc7v3m6aFxhvqW1-61" edge="1">
          <mxGeometry relative="1" as="geometry">
            <mxPoint x="-59" y="720" as="sourcePoint" />
            <Array as="points">
              <mxPoint x="210" y="748" />
              <mxPoint x="210" y="717" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="6bjWbGc7v3m6aFxhvqW1-92" value="stream Orders and Executions" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" parent="6bjWbGc7v3m6aFxhvqW1-86" vertex="1" connectable="0">
          <mxGeometry x="0.1093" y="-2" relative="1" as="geometry">
            <mxPoint x="100" y="11" as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="6bjWbGc7v3m6aFxhvqW1-22" value="&lt;div style=&quot;line-height: 102%;&quot;&gt;&lt;br&gt;&lt;/div&gt;" style="rounded=1;whiteSpace=wrap;html=1;dashed=1;glass=0;arcSize=3;fillColor=#f5f5f5;fontColor=#333333;strokeColor=#666666;" parent="1" vertex="1">
          <mxGeometry x="-920" y="380" width="860" height="490" as="geometry" />
        </mxCell>
        <mxCell id="6bjWbGc7v3m6aFxhvqW1-65" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=0.061;exitY=0.988;exitDx=0;exitDy=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;exitPerimeter=0;" parent="1" source="6bjWbGc7v3m6aFxhvqW1-34" target="6bjWbGc7v3m6aFxhvqW1-38" edge="1">
          <mxGeometry relative="1" as="geometry">
            <Array as="points">
              <mxPoint x="-814" y="550" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="6bjWbGc7v3m6aFxhvqW1-34" value="&lt;div style=&quot;line-height: 102%;&quot;&gt;&lt;br&gt;&lt;/div&gt;" style="rounded=1;whiteSpace=wrap;html=1;dashed=1;glass=0;" parent="1" vertex="1">
          <mxGeometry x="-880" y="400" width="910" height="80" as="geometry" />
        </mxCell>
        <mxCell id="6bjWbGc7v3m6aFxhvqW1-28" value="&lt;div style=&quot;line-height: 102%;&quot;&gt;&lt;br&gt;&lt;/div&gt;" style="rounded=1;whiteSpace=wrap;html=1;dashed=1;glass=0;" parent="1" vertex="1">
          <mxGeometry x="160" y="260" width="560" height="90" as="geometry" />
        </mxCell>
        <mxCell id="6bjWbGc7v3m6aFxhvqW1-25" value="&lt;b&gt;Reference data&lt;/b&gt;&lt;div&gt;Instruments | Exchanges | Accounts | Portfolios&lt;/div&gt;" style="rounded=1;whiteSpace=wrap;html=1;" parent="1" vertex="1">
          <mxGeometry x="170" y="275" width="280" height="60" as="geometry" />
        </mxCell>
        <mxCell id="6bjWbGc7v3m6aFxhvqW1-26" value="&lt;div style=&quot;line-height: 102%;&quot;&gt;&lt;br&gt;&lt;/div&gt;" style="rounded=1;whiteSpace=wrap;html=1;dashed=1;glass=0;" parent="1" vertex="1">
          <mxGeometry x="-700" y="990" width="1420" height="100" as="geometry" />
        </mxCell>
        <mxCell id="6bjWbGc7v3m6aFxhvqW1-20" value="&lt;div style=&quot;line-height: 102%;&quot;&gt;&lt;br&gt;&lt;/div&gt;" style="rounded=1;whiteSpace=wrap;html=1;dashed=1;glass=0;" parent="1" vertex="1">
          <mxGeometry x="-700" y="120" width="1420" height="115" as="geometry" />
        </mxCell>
        <mxCell id="6bjWbGc7v3m6aFxhvqW1-4" value="&lt;b&gt;FIX API&lt;/b&gt;&lt;div&gt;trading&lt;/div&gt;" style="rounded=1;whiteSpace=wrap;html=1;" parent="1" vertex="1">
          <mxGeometry x="190" y="160" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="6bjWbGc7v3m6aFxhvqW1-5" value="&lt;b&gt;FIX API&lt;/b&gt;&lt;div&gt;drop copy&lt;/div&gt;" style="rounded=1;whiteSpace=wrap;html=1;" parent="1" vertex="1">
          <mxGeometry x="320" y="160" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="6bjWbGc7v3m6aFxhvqW1-6" value="&lt;b&gt;FIX API&lt;/b&gt;&lt;div&gt;market data&lt;/div&gt;" style="rounded=1;whiteSpace=wrap;html=1;" parent="1" vertex="1">
          <mxGeometry x="450" y="160" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="6bjWbGc7v3m6aFxhvqW1-7" value="&lt;b&gt;FIX API&lt;/b&gt;&lt;div&gt;custom (OHLC)&lt;/div&gt;" style="rounded=1;whiteSpace=wrap;html=1;" parent="1" vertex="1">
          <mxGeometry x="580" y="160" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="6bjWbGc7v3m6aFxhvqW1-8" value="&lt;b&gt;REST API (GQL API)&lt;/b&gt;&lt;div&gt;trading | market data | booking | permissions | reference data | configs&lt;/div&gt;" style="rounded=1;whiteSpace=wrap;html=1;" parent="1" vertex="1">
          <mxGeometry x="-670" y="160" width="390" height="60" as="geometry" />
        </mxCell>
        <mxCell id="6bjWbGc7v3m6aFxhvqW1-9" value="&lt;b&gt;Inbound&lt;/b&gt;" style="text;html=1;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;" parent="1" vertex="1">
          <mxGeometry x="-30" y="120" width="60" height="30" as="geometry" />
        </mxCell>
        <mxCell id="6bjWbGc7v3m6aFxhvqW1-10" value="&lt;b&gt;Outbound&lt;/b&gt;" style="text;html=1;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;" parent="1" vertex="1">
          <mxGeometry x="-300" y="1060" width="60" height="30" as="geometry" />
        </mxCell>
        <mxCell id="6bjWbGc7v3m6aFxhvqW1-84" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=0.75;exitY=0;exitDx=0;exitDy=0;entryX=0.5;entryY=1;entryDx=0;entryDy=0;" parent="1" source="6bjWbGc7v3m6aFxhvqW1-11" target="6bjWbGc7v3m6aFxhvqW1-78" edge="1">
          <mxGeometry relative="1" as="geometry">
            <Array as="points">
              <mxPoint x="-192" y="933" />
              <mxPoint x="595" y="933" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="6bjWbGc7v3m6aFxhvqW1-11" value="&lt;b&gt;ConnectorWrapper&lt;/b&gt;&lt;div&gt;Connector implementation + runtime + translation layer&lt;/div&gt;" style="rounded=1;whiteSpace=wrap;html=1;" parent="1" vertex="1">
          <mxGeometry x="-425" y="995" width="310" height="60" as="geometry" />
        </mxCell>
        <mxCell id="6bjWbGc7v3m6aFxhvqW1-12" value="&lt;div style=&quot;line-height: 102%;&quot;&gt;&lt;br&gt;&lt;/div&gt;" style="rounded=1;whiteSpace=wrap;html=1;dashed=1;glass=0;" parent="1" vertex="1">
          <mxGeometry x="-700" y="260" width="840" height="90" as="geometry" />
        </mxCell>
        <mxCell id="6bjWbGc7v3m6aFxhvqW1-13" value="&lt;b&gt;Keycloak&lt;/b&gt;&lt;div&gt;Authentication | Federation&lt;/div&gt;" style="rounded=0;whiteSpace=wrap;html=1;fillColor=#ffe6cc;strokeColor=#d79b00;" parent="1" vertex="1">
          <mxGeometry x="-650" y="280" width="190" height="50" as="geometry" />
        </mxCell>
        <mxCell id="6bjWbGc7v3m6aFxhvqW1-14" value="&lt;b&gt;Hashicorp Vault&lt;/b&gt;&lt;div&gt;Wyden API keys | Connector&#39;s secrets&lt;/div&gt;" style="rounded=0;whiteSpace=wrap;html=1;fillColor=#ffe6cc;strokeColor=#d79b00;" parent="1" vertex="1">
          <mxGeometry x="-160" y="280" width="230" height="50" as="geometry" />
        </mxCell>
        <mxCell id="6bjWbGc7v3m6aFxhvqW1-16" value="&lt;b&gt;REST management&lt;/b&gt;&lt;div&gt;onboarding | config | booking&lt;/div&gt;" style="rounded=1;whiteSpace=wrap;html=1;" parent="1" vertex="1">
          <mxGeometry x="-270" y="160" width="220" height="60" as="geometry" />
        </mxCell>
        <mxCell id="6bjWbGc7v3m6aFxhvqW1-17" value="&lt;b&gt;WS API&lt;/b&gt;&lt;div&gt;notifications&lt;/div&gt;" style="rounded=1;whiteSpace=wrap;html=1;" parent="1" vertex="1">
          <mxGeometry x="-40" y="160" width="220" height="60" as="geometry" />
        </mxCell>
        <mxCell id="6bjWbGc7v3m6aFxhvqW1-21" value="&lt;b&gt;Access Gateway&lt;/b&gt;&lt;div&gt;Permissions&lt;/div&gt;" style="rounded=1;whiteSpace=wrap;html=1;" parent="1" vertex="1">
          <mxGeometry x="-420" y="275" width="220" height="60" as="geometry" />
        </mxCell>
        <mxCell id="6bjWbGc7v3m6aFxhvqW1-23" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=0.5;exitY=1;exitDx=0;exitDy=0;" parent="1" source="6bjWbGc7v3m6aFxhvqW1-22" target="6bjWbGc7v3m6aFxhvqW1-22" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="6bjWbGc7v3m6aFxhvqW1-76" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" parent="1" source="6bjWbGc7v3m6aFxhvqW1-24" target="6bjWbGc7v3m6aFxhvqW1-37" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="6bjWbGc7v3m6aFxhvqW1-24" value="&lt;b&gt;Order Gateway&lt;/b&gt;&lt;div&gt;Order tracking | Routing&lt;/div&gt;" style="rounded=1;whiteSpace=wrap;html=1;" parent="1" vertex="1">
          <mxGeometry x="-690" y="410" width="220" height="60" as="geometry" />
        </mxCell>
        <mxCell id="6bjWbGc7v3m6aFxhvqW1-30" value="&lt;b&gt;Target registry&lt;/b&gt;&lt;div&gt;Connector provisioning | Connector health reporting&lt;/div&gt;" style="rounded=1;whiteSpace=wrap;html=1;" parent="1" vertex="1">
          <mxGeometry x="240" y="995" width="315" height="60" as="geometry" />
        </mxCell>
        <mxCell id="6bjWbGc7v3m6aFxhvqW1-32" value="&lt;b&gt;Risk Engine&lt;/b&gt;&lt;div&gt;Pre-trade checks&lt;/div&gt;" style="rounded=1;whiteSpace=wrap;html=1;" parent="1" vertex="1">
          <mxGeometry x="-210" y="410" width="220" height="60" as="geometry" />
        </mxCell>
        <mxCell id="6bjWbGc7v3m6aFxhvqW1-33" value="&lt;b&gt;Broker Config Service&lt;/b&gt;&lt;div&gt;Client-side trading rule engine&lt;/div&gt;" style="rounded=1;whiteSpace=wrap;html=1;" parent="1" vertex="1">
          <mxGeometry x="-450" y="410" width="220" height="60" as="geometry" />
        </mxCell>
        <mxCell id="6bjWbGc7v3m6aFxhvqW1-37" value="Pre trade" style="text;html=1;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;" parent="1" vertex="1">
          <mxGeometry x="-830" y="425" width="60" height="30" as="geometry" />
        </mxCell>
        <mxCell id="6bjWbGc7v3m6aFxhvqW1-38" value="&lt;div style=&quot;line-height: 102%;&quot;&gt;&lt;br&gt;&lt;/div&gt;" style="rounded=1;whiteSpace=wrap;html=1;dashed=1;glass=0;" parent="1" vertex="1">
          <mxGeometry x="-750" y="510" width="1170" height="80" as="geometry" />
        </mxCell>
        <mxCell id="6bjWbGc7v3m6aFxhvqW1-39" value="Broker desk" style="text;html=1;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;" parent="1" vertex="1">
          <mxGeometry x="-720" y="535" width="60" height="30" as="geometry" />
        </mxCell>
        <mxCell id="6bjWbGc7v3m6aFxhvqW1-40" value="&lt;b&gt;Agency trading&lt;/b&gt;" style="rounded=1;whiteSpace=wrap;html=1;" parent="1" vertex="1">
          <mxGeometry x="-610" y="520" width="220" height="60" as="geometry" />
        </mxCell>
        <mxCell id="6bjWbGc7v3m6aFxhvqW1-41" value="&lt;b&gt;Principal trading&lt;/b&gt;&lt;div&gt;&lt;b&gt;&lt;i&gt;(planned)&lt;/i&gt;&lt;/b&gt;&lt;/div&gt;" style="rounded=1;whiteSpace=wrap;html=1;gradientColor=none;sketch=1;curveFitting=1;jiggle=2;strokeColor=#EDEDED;fontColor=#999999;" parent="1" vertex="1">
          <mxGeometry x="-350" y="520" width="220" height="60" as="geometry" />
        </mxCell>
        <mxCell id="6bjWbGc7v3m6aFxhvqW1-42" value="&lt;div style=&quot;line-height: 102%;&quot;&gt;&lt;br&gt;&lt;/div&gt;" style="rounded=1;whiteSpace=wrap;html=1;dashed=1;glass=0;" parent="1" vertex="1">
          <mxGeometry x="-750" y="710" width="660" height="80" as="geometry" />
        </mxCell>
        <mxCell id="6bjWbGc7v3m6aFxhvqW1-43" value="SOR" style="text;html=1;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;" parent="1" vertex="1">
          <mxGeometry x="-720" y="735" width="60" height="30" as="geometry" />
        </mxCell>
        <mxCell id="6bjWbGc7v3m6aFxhvqW1-106" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=0;exitY=0.5;exitDx=0;exitDy=0;entryX=1;entryY=0.5;entryDx=0;entryDy=0;" parent="1" source="6bjWbGc7v3m6aFxhvqW1-44" target="6bjWbGc7v3m6aFxhvqW1-45" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="6bjWbGc7v3m6aFxhvqW1-44" value="&lt;b&gt;Recommendation Engine&lt;/b&gt;&lt;div&gt;Best candidate recommendation streaming&lt;/div&gt;" style="rounded=1;whiteSpace=wrap;html=1;" parent="1" vertex="1">
          <mxGeometry x="-350" y="720" width="220" height="60" as="geometry" />
        </mxCell>
        <mxCell id="6bjWbGc7v3m6aFxhvqW1-45" value="&lt;b&gt;Smart Order Router&lt;/b&gt;&lt;div&gt;SOR Orders | State Machine | Retry logic&lt;/div&gt;" style="rounded=1;whiteSpace=wrap;html=1;" parent="1" vertex="1">
          <mxGeometry x="-610" y="720" width="220" height="60" as="geometry" />
        </mxCell>
        <mxCell id="6bjWbGc7v3m6aFxhvqW1-46" value="&lt;div style=&quot;line-height: 102%;&quot;&gt;&lt;br&gt;&lt;/div&gt;" style="rounded=1;whiteSpace=wrap;html=1;dashed=1;glass=0;" parent="1" vertex="1">
          <mxGeometry x="-750" y="610" width="910" height="80" as="geometry" />
        </mxCell>
        <mxCell id="6bjWbGc7v3m6aFxhvqW1-104" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=1;exitY=0.5;exitDx=0;exitDy=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;" parent="1" source="6bjWbGc7v3m6aFxhvqW1-47" target="6bjWbGc7v3m6aFxhvqW1-48" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="6bjWbGc7v3m6aFxhvqW1-47" value="&lt;b&gt;CLOB Gateway&lt;/b&gt;&lt;div&gt;CLOB Orders | State Machine | External match detection &amp;amp; hedging&lt;/div&gt;" style="rounded=1;whiteSpace=wrap;html=1;" parent="1" vertex="1">
          <mxGeometry x="-610" y="620" width="220" height="60" as="geometry" />
        </mxCell>
        <mxCell id="6bjWbGc7v3m6aFxhvqW1-48" value="&lt;b&gt;Aeron Cluster&lt;/b&gt;&lt;div&gt;Matching engine (Exchange Core)&lt;/div&gt;" style="rounded=0;whiteSpace=wrap;html=1;fillColor=#ffe6cc;strokeColor=#d79b00;" parent="1" vertex="1">
          <mxGeometry x="-350" y="625" width="220" height="50" as="geometry" />
        </mxCell>
        <mxCell id="6bjWbGc7v3m6aFxhvqW1-49" value="Internal execution" style="text;html=1;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;" parent="1" vertex="1">
          <mxGeometry x="-720" y="635" width="60" height="30" as="geometry" />
        </mxCell>
        <mxCell id="6bjWbGc7v3m6aFxhvqW1-50" value="&lt;div style=&quot;line-height: 102%;&quot;&gt;&lt;br&gt;&lt;/div&gt;" style="rounded=1;whiteSpace=wrap;html=1;dashed=1;glass=0;" parent="1" vertex="1">
          <mxGeometry x="-750" y="810" width="660" height="80" as="geometry" />
        </mxCell>
        <mxCell id="6bjWbGc7v3m6aFxhvqW1-51" value="External execution" style="text;html=1;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;" parent="1" vertex="1">
          <mxGeometry x="-720" y="835" width="60" height="30" as="geometry" />
        </mxCell>
        <mxCell id="6bjWbGc7v3m6aFxhvqW1-60" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;startArrow=classic;startFill=1;" parent="1" source="6bjWbGc7v3m6aFxhvqW1-52" target="6bjWbGc7v3m6aFxhvqW1-11" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="6bjWbGc7v3m6aFxhvqW1-93" value="Orders | Executions" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" parent="6bjWbGc7v3m6aFxhvqW1-60" vertex="1" connectable="0">
          <mxGeometry x="-0.4477" y="4" relative="1" as="geometry">
            <mxPoint x="26" y="-3" as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="6bjWbGc7v3m6aFxhvqW1-52" value="&lt;b&gt;Order Collider&lt;/b&gt;&lt;div&gt;Street Orders | State Machine&lt;/div&gt;" style="rounded=1;whiteSpace=wrap;html=1;" parent="1" vertex="1">
          <mxGeometry x="-610" y="820" width="220" height="60" as="geometry" />
        </mxCell>
        <mxCell id="6bjWbGc7v3m6aFxhvqW1-61" value="&lt;div style=&quot;line-height: 102%;&quot;&gt;&lt;br&gt;&lt;/div&gt;" style="rounded=1;whiteSpace=wrap;html=1;dashed=1;glass=0;arcSize=12;fillColor=#f5f5f5;fontColor=#333333;strokeColor=#666666;" parent="1" vertex="1">
          <mxGeometry x="470" y="390" width="250" height="360" as="geometry" />
        </mxCell>
        <mxCell id="6bjWbGc7v3m6aFxhvqW1-97" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=0.25;exitY=1;exitDx=0;exitDy=0;entryX=1;entryY=0.5;entryDx=0;entryDy=0;" parent="1" source="6bjWbGc7v3m6aFxhvqW1-62" target="6bjWbGc7v3m6aFxhvqW1-95" edge="1">
          <mxGeometry relative="1" as="geometry">
            <Array as="points">
              <mxPoint x="540" y="500" />
              <mxPoint x="400" y="500" />
              <mxPoint x="400" y="550" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="6bjWbGc7v3m6aFxhvqW1-98" value="stream position updates" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" parent="6bjWbGc7v3m6aFxhvqW1-97" vertex="1" connectable="0">
          <mxGeometry x="-0.3419" y="-1" relative="1" as="geometry">
            <mxPoint x="-41" y="-9" as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="6bjWbGc7v3m6aFxhvqW1-62" value="&lt;b&gt;Booking Engine&lt;/b&gt;&lt;div&gt;Positions | PnL metrics | Settlements&lt;/div&gt;" style="rounded=1;whiteSpace=wrap;html=1;" parent="1" vertex="1">
          <mxGeometry x="485" y="410" width="220" height="60" as="geometry" />
        </mxCell>
        <mxCell id="6bjWbGc7v3m6aFxhvqW1-63" value="&lt;b&gt;Settlement Engine&lt;/b&gt;&lt;div&gt;&lt;b&gt;(planned)&lt;/b&gt;&lt;/div&gt;" style="rounded=1;whiteSpace=wrap;html=1;gradientColor=none;sketch=1;curveFitting=1;jiggle=2;strokeColor=#EDEDED;fontColor=#999999;" parent="1" vertex="1">
          <mxGeometry x="485" y="520" width="220" height="60" as="geometry" />
        </mxCell>
        <mxCell id="6bjWbGc7v3m6aFxhvqW1-64" value="&lt;b&gt;Portfolio Metrics&lt;span style=&quot;white-space: pre;&quot;&gt; &lt;/span&gt;Engine&lt;/b&gt;&lt;div&gt;&lt;b&gt;(planned)&lt;/b&gt;&lt;/div&gt;" style="rounded=1;whiteSpace=wrap;html=1;gradientColor=none;sketch=1;curveFitting=1;jiggle=2;strokeColor=#EDEDED;fontColor=#999999;" parent="1" vertex="1">
          <mxGeometry x="485" y="610" width="220" height="60" as="geometry" />
        </mxCell>
        <mxCell id="6bjWbGc7v3m6aFxhvqW1-66" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=0;entryY=0.5;entryDx=0;entryDy=0;" parent="1" target="6bjWbGc7v3m6aFxhvqW1-46" edge="1">
          <mxGeometry relative="1" as="geometry">
            <mxPoint x="-815" y="481" as="sourcePoint" />
            <mxPoint x="-740" y="560" as="targetPoint" />
            <Array as="points">
              <mxPoint x="-815" y="650" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="6bjWbGc7v3m6aFxhvqW1-67" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=0;entryY=0.5;entryDx=0;entryDy=0;" parent="1" target="6bjWbGc7v3m6aFxhvqW1-42" edge="1">
          <mxGeometry relative="1" as="geometry">
            <mxPoint x="-815" y="480" as="sourcePoint" />
            <mxPoint x="-740" y="660" as="targetPoint" />
            <Array as="points">
              <mxPoint x="-815" y="750" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="6bjWbGc7v3m6aFxhvqW1-68" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=0;entryY=0.5;entryDx=0;entryDy=0;" parent="1" target="6bjWbGc7v3m6aFxhvqW1-50" edge="1">
          <mxGeometry relative="1" as="geometry">
            <mxPoint x="-815" y="480" as="sourcePoint" />
            <mxPoint x="-740" y="760" as="targetPoint" />
            <Array as="points">
              <mxPoint x="-815" y="850" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="6bjWbGc7v3m6aFxhvqW1-70" value="&lt;b&gt;Pricing Engine&lt;/b&gt;&lt;div&gt;Client-side market data&lt;/div&gt;" style="rounded=1;whiteSpace=wrap;html=1;" parent="1" vertex="1">
          <mxGeometry x="-80" y="520" width="220" height="60" as="geometry" />
        </mxCell>
        <mxCell id="6bjWbGc7v3m6aFxhvqW1-72" value="&lt;b&gt;OEMS&lt;/b&gt;" style="text;html=1;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;" parent="1" vertex="1">
          <mxGeometry x="-910" y="830" width="60" height="30" as="geometry" />
        </mxCell>
        <mxCell id="6bjWbGc7v3m6aFxhvqW1-77" value="&lt;b&gt;Accounting&lt;/b&gt;" style="text;html=1;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;" parent="1" vertex="1">
          <mxGeometry x="570" y="700" width="60" height="30" as="geometry" />
        </mxCell>
        <mxCell id="6bjWbGc7v3m6aFxhvqW1-79" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=0.5;exitY=0;exitDx=0;exitDy=0;entryX=0.5;entryY=1;entryDx=0;entryDy=0;" parent="1" source="6bjWbGc7v3m6aFxhvqW1-78" target="6bjWbGc7v3m6aFxhvqW1-61" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="6bjWbGc7v3m6aFxhvqW1-78" value="&lt;b&gt;Rate Service&lt;/b&gt;&lt;div&gt;Current FX rate provider&lt;/div&gt;" style="rounded=1;whiteSpace=wrap;html=1;" parent="1" vertex="1">
          <mxGeometry x="485" y="810" width="220" height="60" as="geometry" />
        </mxCell>
        <mxCell id="6bjWbGc7v3m6aFxhvqW1-85" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=1;exitY=0.5;exitDx=0;exitDy=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;" parent="1" source="6bjWbGc7v3m6aFxhvqW1-34" target="6bjWbGc7v3m6aFxhvqW1-62" edge="1">
          <mxGeometry relative="1" as="geometry">
            <Array as="points">
              <mxPoint x="310" y="440" />
              <mxPoint x="310" y="440" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="6bjWbGc7v3m6aFxhvqW1-91" value="(optional position check)" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" parent="6bjWbGc7v3m6aFxhvqW1-85" vertex="1" connectable="0">
          <mxGeometry x="-0.6786" relative="1" as="geometry">
            <mxPoint x="35" y="-10" as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="6bjWbGc7v3m6aFxhvqW1-105" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=0;exitY=0.5;exitDx=0;exitDy=0;entryX=1;entryY=0.5;entryDx=0;entryDy=0;" parent="1" source="6bjWbGc7v3m6aFxhvqW1-69" target="6bjWbGc7v3m6aFxhvqW1-48" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="6bjWbGc7v3m6aFxhvqW1-69" value="&lt;b&gt;Quoting Engine&lt;/b&gt;&lt;div&gt;External liquidity mirroring&lt;/div&gt;" style="rounded=1;whiteSpace=wrap;html=1;" parent="1" vertex="1">
          <mxGeometry x="-80" y="620" width="220" height="60" as="geometry" />
        </mxCell>
        <mxCell id="6bjWbGc7v3m6aFxhvqW1-95" value="&lt;b&gt;AutoHedger&lt;/b&gt;&lt;div&gt;Observe Position and hedge&lt;/div&gt;" style="rounded=1;whiteSpace=wrap;html=1;" parent="1" vertex="1">
          <mxGeometry x="160" y="520" width="220" height="60" as="geometry" />
        </mxCell>
        <mxCell id="6bjWbGc7v3m6aFxhvqW1-100" value="&lt;b&gt;Market Data Manager&lt;/b&gt;&lt;div&gt;Deduplicates Market data requests&lt;/div&gt;" style="rounded=1;whiteSpace=wrap;html=1;" parent="1" vertex="1">
          <mxGeometry x="-90" y="995" width="310" height="60" as="geometry" />
        </mxCell>
        <mxCell id="6bjWbGc7v3m6aFxhvqW1-101" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=0.5;exitY=1;exitDx=0;exitDy=0;" parent="1" source="6bjWbGc7v3m6aFxhvqW1-26" target="6bjWbGc7v3m6aFxhvqW1-26" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="4JK7Ufv2KNQaGYpIt138-1" value="&lt;b&gt;Order History&lt;/b&gt;&lt;div&gt;Historical Orders and Order details view&lt;/div&gt;" style="rounded=1;whiteSpace=wrap;html=1;" vertex="1" parent="1">
          <mxGeometry x="460" y="275" width="250" height="60" as="geometry" />
        </mxCell>
      </root>
    </mxGraphModel>
  </diagram>
</mxfile>
