package io.wyden.rest.management.permission;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.hazelcast.config.AdvancedNetworkConfig;
import com.hazelcast.config.Config;
import com.hazelcast.config.JoinConfig;
import com.hazelcast.core.Hazelcast;
import com.hazelcast.core.HazelcastInstance;
import com.hazelcast.map.IMap;
import io.micrometer.core.instrument.MeterRegistry;
import io.wyden.accessgateway.client.license.LicenseService;
import io.wyden.accessgateway.client.permission.PermissionChecker;
import io.wyden.accessgateway.client.permission.dto.PermissionDto;
import io.wyden.cloud.utils.rest.pagination.PaginationModel;
import io.wyden.cloud.utils.test.TracingMock;
import io.wyden.cloudutils.telemetry.Telemetry;
import io.wyden.cloudutils.tools.DateUtils;
import io.wyden.published.referencedata.AccountType;
import io.wyden.published.referencedata.VenueAccount;
import io.wyden.published.referencedata.WalletType;
import io.wyden.referencedata.client.VenueAccountCacheFacade;
import io.wyden.referencedata.domain.VenueAccountMapConfig;
import io.wyden.rest.management.DisableApiSignatureValidation;
import io.wyden.rest.management.WithMockCustomUser;
import io.wyden.rest.management.account.AccountController;
import io.wyden.rest.management.account.AccountCreateRequestEmitter;
import io.wyden.rest.management.account.AccountOnboardingEventConsumer;
import io.wyden.rest.management.account.AccountPermissionValidator;
import io.wyden.rest.management.account.AccountService;
import io.wyden.rest.management.account.VenueAccountRepository;
import io.wyden.rest.management.booking.BookingEngineService;
import io.wyden.rest.management.domain.AccountModel;
import io.wyden.rest.management.infrastructure.web.WebSecurityConfig;
import io.wyden.rest.management.onboarding.OnboardingRequestValidator;
import io.wyden.rest.management.portfolio.AccessGatewayMockClient;
import io.wyden.rest.management.portfolio.PortfolioRepository;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.boot.autoconfigure.EnableAutoConfiguration;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest;
import org.springframework.boot.test.context.TestConfiguration;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Import;
import org.springframework.context.annotation.Primary;
import org.springframework.http.MediaType;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders;
import org.springframework.web.context.WebApplicationContext;

import java.util.HashSet;
import java.util.Map;
import java.util.Set;
import java.util.UUID;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.Mockito.mock;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

@WebMvcTest(controllers = AccountController.class)
@Import({WebSecurityConfig.class, DisableApiSignatureValidation.class, AccountService.class, AccountPermissionValidator.class, VenueAccountRepository.class})
@ContextConfiguration(classes = {AccountControllerWalletPermissionsTest.TestConfig.class})
@MockBean(classes = {PermissionChecker.class, LicenseService.class, PortfolioRepository.class})
@AutoConfigureMockMvc
class AccountControllerWalletPermissionsTest {
    @Autowired
    private MockMvc mockMvc;
    @Autowired
    private WebApplicationContext context;
    @Autowired
    VenueAccountCacheFacade venueAccountCacheFacade;
    @Qualifier("accountMap")
    @Autowired
    IMap<String, VenueAccount> venueAccountMap;
    @Autowired
    AccessGatewayMockClient accessGatewayFacade;

    @MockBean
    AccountCreateRequestEmitter accountCreateRequestEmitter;
    @MockBean
    AccountOnboardingEventConsumer accountOnboardingEventConsumer;
    @MockBean
    OnboardingRequestValidator validator;
    @MockBean
    BookingEngineService bookingEngineService;

    @BeforeEach
    void mockResponses() {
        Map<String, VenueAccount> accounts = Map.of(
            "account_1", VenueAccount.newBuilder().setId("account_1").setAccountType(AccountType.EXCHANGE).setCreatedAt(DateUtils.toIsoUtcTime()).build(),
            "wallet_1", VenueAccount.newBuilder().setId("wallet_1").setAccountType(AccountType.WALLET).setCreatedAt(DateUtils.toIsoUtcTime()).build(),
            "wallet_V", VenueAccount.newBuilder().setId("wallet_V").setAccountType(AccountType.WALLET).setCreatedAt(DateUtils.toIsoUtcTime()).setWalletType(WalletType.WALLET_VOSTRO).build(),
            "wallet_N", VenueAccount.newBuilder().setId("wallet_N").setAccountType(AccountType.WALLET).setCreatedAt(DateUtils.toIsoUtcTime()).setWalletType(WalletType.WALLET_NOSTRO).build()
        );
        venueAccountMap.putAll(accounts);
    }

    @Test
    @WithMockCustomUser(authorities = {
        "wallet.nostro:read",
    })
    void getNostroWallets() throws Exception {
        accessGatewayFacade.grantPermissions(Set.of(new PermissionDto("wallet.nostro", "read")));

        String json = mockMvc.perform(MockMvcRequestBuilders.get("/api/v1/accounts")
                .contentType(MediaType.APPLICATION_JSON))
            .andExpect(status().isOk())
            .andReturn().getResponse().getContentAsString();

        PaginationModel.CursorConnection<AccountModel.Account> cursorConnection = new ObjectMapper().readValue(json, new TypeReference<PaginationModel.CursorConnection<AccountModel.Account>>() {});
        assertThat(cursorConnection).isNotNull();
        assertThat(cursorConnection.edges()).hasSize(1);
        AccountModel.Account node = cursorConnection.edges().get(0).node();
        assertThat(node.id()).isEqualTo("wallet_N");
        assertThat(node.walletType().name()).isEqualTo("NOSTRO");
    }

    @Test
    @WithMockCustomUser(authorities = {
        "wallet.vostro:read",
    })
    void getVostroWallets() throws Exception {
        accessGatewayFacade.grantPermissions(Set.of(new PermissionDto("wallet.vostro", "read")));

        String json = mockMvc.perform(MockMvcRequestBuilders.get("/api/v1/accounts")
                .contentType(MediaType.APPLICATION_JSON))
            .andExpect(status().isOk())
            .andReturn().getResponse().getContentAsString();

        PaginationModel.CursorConnection<AccountModel.Account> cursorConnection = new ObjectMapper().readValue(json, new TypeReference<PaginationModel.CursorConnection<AccountModel.Account>>() {});
        assertThat(cursorConnection).isNotNull();
        assertThat(cursorConnection.edges()).hasSize(1);
        AccountModel.Account node = cursorConnection.edges().get(0).node();
        assertThat(node.id()).isEqualTo("wallet_V");
        assertThat(node.walletType().name()).isEqualTo("VOSTRO");
    }

    @TestConfiguration
    @EnableAutoConfiguration
    public static class TestConfig {

        @Primary
        @Bean("hazelcast")
        HazelcastInstance createHazelcastInstance() {
            Config config = new Config();
            AdvancedNetworkConfig advancedNetworkConfig = config.getAdvancedNetworkConfig();
            JoinConfig join = advancedNetworkConfig.getJoin();
            join.getMulticastConfig().setEnabled(false);
            join.getTcpIpConfig().setEnabled(false);
            join.getAutoDetectionConfig().setEnabled(false);
            join.getKubernetesConfig().setEnabled(false);
            join.getDiscoveryConfig().setDiscoveryStrategyConfigs(null);
            config.setClusterName(UUID.randomUUID().toString());
            config.getJetConfig().setEnabled(true);
            config.getJetConfig().setBackupCount(0);
            return Hazelcast.newHazelcastInstance(config);
        }

        @Bean("accountMap")
        IMap<String, VenueAccount> createVenueAccountMap(HazelcastInstance hazelcastInstance) {
            return VenueAccountMapConfig.getMap(hazelcastInstance);
        }

        @Primary
        @Bean
        Telemetry telemetry() {
            return new Telemetry(TracingMock.createMock(), mock(MeterRegistry.class));
        }

        @Bean
        VenueAccountCacheFacade venueAccountCacheFacade(IMap<String, VenueAccount> createVenueAccountMap, Telemetry telemetry) {
            return new VenueAccountCacheFacade(createVenueAccountMap, telemetry.getTracing());
        }

        @Bean
        AccessGatewayMockClient accessGatewayFacade() {
            return new AccessGatewayMockClient(new HashSet<>());
        }
    }

}
