package io.wyden.rest.management.ledgerentry;

import io.wyden.cloud.utils.rest.pagination.PaginationModel;
import io.wyden.rest.management.booking.BookingEngineService;
import io.wyden.rest.management.domain.LedgerEntryModel;
import io.wyden.rest.management.security.authentication.WydenAuthenticationToken;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping(path = "${rest.management.context-path}")
public class LedgerEntryController {

    private static final Logger LOGGER = LoggerFactory.getLogger(LedgerEntryController.class);

    private final BookingEngineService bookingEngineService;

    public LedgerEntryController(BookingEngineService bookingEngineService) {
        this.bookingEngineService = bookingEngineService;
    }

    @GetMapping("/ledger-entries")
    public PaginationModel.CursorConnection<LedgerEntryModel.LedgerEntry> getLedgerEntries(LedgerEntryModel.LedgerEntrySearch search, WydenAuthenticationToken authentication) {
        LOGGER.info("Getting ledger entries for {}", search);
        return bookingEngineService.searchLedgerEntries(search, authentication.getClientId());
    }
}
