package io.wyden.rest.management.security.permission;

import io.wyden.accessgateway.client.permission.WydenRole;
import io.wyden.rest.management.security.authentication.WydenAuthenticationToken;

import java.util.Set;

public record User(String clientId, Set<String> groups, Set<WydenRole> roles) {

    public static User user(WydenAuthenticationToken token) {
        return new User(token.getClientId(), token.getGroups(), token.getRoles());
    }
}