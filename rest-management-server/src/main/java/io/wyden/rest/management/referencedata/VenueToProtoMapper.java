package io.wyden.rest.management.referencedata;

import io.wyden.cloudutils.tools.DateUtils;
import io.wyden.published.common.Metadata;
import io.wyden.published.referencedata.VenueCreateRequest;
import io.wyden.published.referencedata.VenueType;
import io.wyden.rest.management.domain.VenueModel;
import org.apache.commons.lang3.ObjectUtils;

import java.util.UUID;

import static org.apache.commons.lang3.StringUtils.EMPTY;

public final class VenueToProtoMapper {

    private VenueToProtoMapper() {
        // Empty
    }

    public static VenueCreateRequest map(VenueModel.VenueCreateRequest request, String clientId) {
        Metadata metadata = Metadata.newBuilder()
            .setRequestId(UUID.randomUUID().toString())
            .setCreatedAt(DateUtils.toIsoUtcTime())
            .setRequesterId(clientId)
            .build();

        return VenueCreateRequest.newBuilder()
            .setMetadata(metadata)
            .setName(request.name())
            .setVenueType(map(request.venueType()))
            .setTradingViewId(ObjectUtils.firstNonNull(request.tradingViewId(), EMPTY))
            .build();
    }

    public static VenueType map(VenueModel.VenueType venueType) {
        return switch (venueType) {
            case CLIENT -> VenueType.CLIENT;
            case CLOB -> VenueType.CLOB;
            case STREET -> VenueType.STREET;
        };
    }
}
