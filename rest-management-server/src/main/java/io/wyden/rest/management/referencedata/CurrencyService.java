package io.wyden.rest.management.referencedata;

import io.wyden.published.referencedata.Currency;
import io.wyden.referencedata.client.CurrencyCacheFacade;
import org.springframework.stereotype.Service;

import java.util.Optional;

@Service
public class CurrencyService {

    private final CurrencyCacheFacade currencyCacheFacade;

    public CurrencyService(CurrencyCacheFacade currencyCacheFacade) {
        this.currencyCacheFacade = currencyCacheFacade;
    }

    public Optional<Currency> find(String currency) {
        return currencyCacheFacade.find(currency);
    }
}
