package io.wyden.referencedata.validator;

import io.wyden.published.audit.EventLogStatus;
import io.wyden.published.common.Metadata;
import io.wyden.published.referencedata.Portfolio;
import io.wyden.published.referencedata.PortfolioChangeCommandType;
import io.wyden.published.referencedata.PortfolioChangeRequest;
import io.wyden.published.referencedata.PortfolioToModify;
import io.wyden.published.referencedata.PortfolioType;
import io.wyden.referencedata.exception.PortfolioChangeException;
import io.wyden.referencedata.service.EventLogEmitter;
import io.wyden.referencedata.service.portfolio.PortfolioRepository;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.Map;

import static io.wyden.published.referencedata.PortfolioChangeCommandType.PORTFOLIO_CHANGE_COMMAND_TYPE_CREATE;
import static io.wyden.published.referencedata.PortfolioChangeCommandType.PORTFOLIO_CHANGE_COMMAND_TYPE_UPDATE;
import static org.assertj.core.api.Assertions.assertThatExceptionOfType;
import static org.assertj.core.api.Assertions.assertThatThrownBy;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class PortfolioChangeRequestValidatorTest {

    private static final String ID = "123";
    private static final String BTC = "BTC";
    private static final Map<String, String> TAGS = Map.of("key", "value");
    private static final String NAME = "test";
    private static final String ARCHIVED_AT = "archivedAt";
    @Mock
    private PortfolioRepository portfolioRepository;

    @Mock
    private EventLogEmitter eventLogEmitter;

    @InjectMocks
    private PortfolioChangeRequestValidator portfolioChangeRequestValidator;

    @Test
    void validate_portfolioDoesNotExistsDuringUpdate() {
        //given
        PortfolioToModify portfolio = createPortfolioToModifyUnarchived();

        PortfolioChangeRequest portfolioChangeRequest = PortfolioChangeRequest.newBuilder()
            .setRequester("Requester")
            .setPortfolioToModify(portfolio)
            .setPortfolioChangeCommandType(PortfolioChangeCommandType.PORTFOLIO_CHANGE_COMMAND_TYPE_UPDATE)
            .setMetadata(Metadata.newBuilder()
                .setCorrelationObject("testCorrelationObject")
                .build())
            .build();

        //when + then
        String expectedMessage = "Portfolio with id: " + ID + " could not be found";
        assertThatExceptionOfType(PortfolioChangeException.class)
            .isThrownBy(() -> portfolioChangeRequestValidator.validate(portfolioChangeRequest))
            .withMessage(expectedMessage);

        verify(eventLogEmitter).emit(
            eq(portfolioChangeRequest.getRequester()),
            any(String.class),
            eq("Portfolio update failed: " + expectedMessage),
            eq(EventLogStatus.FAILURE),
            eq("portfolioUpdateRequestStatus"),
            eq("testCorrelationObject"));
    }

    @Test
    void validate_tryToUpdateInstrumentWhichIsArchived() {
        //given
        PortfolioToModify portfolio = createPortfolioToModifyArchived();

        PortfolioChangeRequest portfolioChangeRequest = PortfolioChangeRequest.newBuilder()
            .setRequester("Requester")
            .setPortfolioToModify(portfolio)
            .setPortfolioChangeCommandType(PortfolioChangeCommandType.PORTFOLIO_CHANGE_COMMAND_TYPE_UPDATE)
            .setMetadata(Metadata.newBuilder()
                .setCorrelationObject("testCorrelationObject")
                .build())
            .build();

        when(portfolioRepository.find(ID)).thenReturn(createArchivedPortfolio());

        //when + then
        String expectedMessage = "Portfolio with id: " + ID + " is archived and cannot be updated without un-archiving";
        assertThatExceptionOfType(PortfolioChangeException.class)
            .isThrownBy(() -> portfolioChangeRequestValidator.validate(portfolioChangeRequest))
            .withMessage(expectedMessage);

        verify(eventLogEmitter).emit(
            eq(portfolioChangeRequest.getRequester()),
            any(String.class),
            eq("Portfolio update failed: " + expectedMessage),
            eq(EventLogStatus.FAILURE),
            eq("portfolioUpdateRequestStatus"),
            eq("testCorrelationObject"));
    }

    @Test
    void validate_tryToUnarchivePortfolio() {
        //given
        PortfolioToModify portfolio = createPortfolioToModifyUnarchived();

        PortfolioChangeRequest portfolioChangeRequest = PortfolioChangeRequest.newBuilder()
            .setPortfolioToModify(portfolio)
            .setPortfolioChangeCommandType(PortfolioChangeCommandType.PORTFOLIO_CHANGE_COMMAND_TYPE_UPDATE)
            .build();

        when(portfolioRepository.find(ID)).thenReturn(createArchivedPortfolio());

        //when
        portfolioChangeRequestValidator.validate(portfolioChangeRequest);

        //then
        verify(portfolioRepository).find(ID);
    }

    @Test
    public void shouldThrowExceptionWhenCreatingPortfolioWithBlankCurrency() {
        // given
        PortfolioChangeRequest request = PortfolioChangeRequest.newBuilder()
            .setPortfolioChangeCommandType(PORTFOLIO_CHANGE_COMMAND_TYPE_CREATE)
            .setPortfolioToModify(PortfolioToModify.newBuilder()
                .setPortfolioCurrency("")
                .build())
            .build();

        // when + then
        assertThatThrownBy(() -> portfolioChangeRequestValidator.validate(request))
            .isInstanceOf(PortfolioChangeException.class)
            .hasMessage("Portfolio does not have portfolio currency parameter");
    }

    @Test
    public void shouldThrowExceptionWhenUpdatingPortfolioType() {
        // given portfolio exists
        when(portfolioRepository.find(ID)).thenReturn(createPortfolio(PortfolioType.NOSTRO));

        // and request incoming
        PortfolioChangeRequest request = PortfolioChangeRequest.newBuilder()
            .setPortfolioChangeCommandType(PORTFOLIO_CHANGE_COMMAND_TYPE_UPDATE)
            .setPortfolioToModify(buildPortfolioToModify(PortfolioType.VOSTRO))
            .build();

        // when + then
        assertThatThrownBy(() -> portfolioChangeRequestValidator.validate(request))
            .isInstanceOf(PortfolioChangeException.class)
            .hasMessageContaining("cannot be modified - changing portfolioType is not supported");
    }

    private static PortfolioToModify createPortfolioToModifyUnarchived() {
        return buildPortfolioToModify()
            .setArchived(false)
            .build();
    }

    private static PortfolioToModify createPortfolioToModifyArchived() {
        return buildPortfolioToModify()
            .setArchived(true)
            .build();
    }

    private static PortfolioToModify.Builder buildPortfolioToModify() {
        return buildPortfolioToModify(PortfolioType.NOSTRO);
    }

    private static PortfolioToModify.Builder buildPortfolioToModify(PortfolioType portfolioType) {
        return PortfolioToModify.newBuilder()
            .setId(ID)
            .setName(NAME)
            .setPortfolioCurrency(BTC)
            .setPortfolioType(portfolioType)
            .putAllTags(TAGS);
    }

    private static Portfolio createPortfolio(PortfolioType portfolioType) {
        return Portfolio.newBuilder()
            .setId(ID)
            .setName(NAME)
            .setPortfolioCurrency(BTC)
            .putAllTags(TAGS)
            .setPortfolioType(portfolioType)
            .build();
    }

    private static Portfolio createArchivedPortfolio() {
        return Portfolio.newBuilder()
            .setArchivedAt(ARCHIVED_AT)
            .setId(ID)
            .setName(NAME)
            .setPortfolioCurrency(BTC)
            .putAllTags(TAGS)
            .setPortfolioType(PortfolioType.NOSTRO)
            .build();
    }
}