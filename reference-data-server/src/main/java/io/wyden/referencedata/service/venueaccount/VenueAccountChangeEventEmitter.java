package io.wyden.referencedata.service.venueaccount;

import io.wyden.cloudutils.rabbitmq.RabbitExchange;
import io.wyden.cloudutils.tools.ProtobufUtils;
import io.wyden.published.referencedata.VenueAccountChangeEvent;
import io.wyden.referencedata.exception.FailureNeedsRetryException;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

@Component
public class VenueAccountChangeEventEmitter {

    private static final Logger LOGGER = LoggerFactory.getLogger(VenueAccountChangeEventEmitter.class);

    private final RabbitExchange<VenueAccountChangeEvent> venueAccountChangeEventExchange;

    public VenueAccountChangeEventEmitter(RabbitExchange<VenueAccountChangeEvent> venueAccountChangeEventExchange) {
        this.venueAccountChangeEventExchange = venueAccountChangeEventExchange;
    }

    public void emit(VenueAccountChangeEvent venueAccountChangeEvent) {
        LOGGER.info("Emitting venue account change event: {}", ProtobufUtils.shortDebugString(venueAccountChangeEvent));
        try {
            venueAccountChangeEventExchange.publish(venueAccountChangeEvent, StringUtils.EMPTY);
        } catch (Exception ex) {
            throw new FailureNeedsRetryException(ex);
        }
    }
}
