package io.wyden.quoting.engine.domain.map;

import com.hazelcast.config.Config;
import com.hazelcast.config.MapStoreConfig;
import com.hazelcast.config.SerializationConfig;
import com.hazelcast.core.HazelcastInstance;
import com.hazelcast.map.IMap;
import io.wyden.cloudutils.hazelcast.HazelcastMapConfig;
import io.wyden.published.brokerdesk.QuotingConfig;
import jakarta.annotation.Nullable;

import static io.wyden.cloudutils.hazelcast.Serializers.protobufSerializer;

public class QuotingConfigMapConfig extends HazelcastMapConfig {

    private static final String MAP_NAME = "quoting-config_v0.1";

    public static IMap<Long, QuotingConfig> getMap(HazelcastInstance hazelcastInstance) {
        return hazelcastInstance.getMap(MAP_NAME);
    }

    @Override
    public String getMapName() {
        return MAP_NAME;
    }

    @Override
    protected void addSerializersConfig(SerializationConfig serializationConfig) {
        serializationConfig.addSerializerConfig(protobufSerializer(QuotingConfig.class, QuotingConfig.parser()));
    }

    @Override
    protected void addMapConfig(Config config, @Nullable MapStoreConfig mapStoreConfig) {
        super.addMapConfig(config, mapStoreConfig);
    }
}
