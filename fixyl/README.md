# Fixyl Usage Instructions

1. Install Fixyl locally from https://github.com/yaalalabs/fixyl
2. After installation, update the shortcut settings:
    - Change the **"Run in"** (`Rozpocznij w`) directory to point to the location of the custom dictionary.  
      The default path is:  
      `oems\fix-api-server\fix-api-common\src\main\resources`.
      ![FixylWindowsConfiguration](images/fixyl-run-config-win.png)
3. This ensures that Fixyl correctly references the dictionary using our config files.
4. Run Fixyl and configure the Fixyl settings by specifying the directory of this file as the configuration location.
