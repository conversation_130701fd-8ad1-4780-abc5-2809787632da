package io.wyden.cloudutils.rabbitmq.health.indicators;

import io.wyden.cloudutils.rabbitmq.RabbitIntegrator;
import org.springframework.boot.actuate.health.AbstractHealthIndicator;
import org.springframework.boot.actuate.health.Health;

public class RabbitHealthCheckConfiguration extends AbstractHealthIndicator {

    private final RabbitIntegrator rabbitIntegrator;

    public RabbitHealthCheckConfiguration(RabbitIntegrator rabbitIntegrator) {
        this.rabbitIntegrator = rabbitIntegrator;
    }

    @Override
    protected void doHealthCheck(Health.Builder builder) {
        builder.up().withDetail("version", getVersion());
        try {
            rabbitIntegrator.checkHealth();
        } catch (Throwable e) {
            builder.withDetail("rabbit", "down");
            builder.down().withException(e);
        }
    }

    private String getVersion() {
        return rabbitIntegrator.getServerProperties().get("version").toString();
    }

}
