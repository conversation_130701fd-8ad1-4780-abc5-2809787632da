package io.wyden.apiserver.rest.brokerdesk;

import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.web.reactive.function.client.WebClient;
import org.springframework.web.reactive.function.client.WebClientResponseException;
import org.springframework.web.util.UriBuilder;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

public abstract class BaseHttpClient {

    protected final WebClient webClient;

    protected BaseHttpClient(WebClient webClient) {
        this.webClient = webClient;
    }

    public <T> Flux<T> getResource(String url, Class<T> resourceClass) {
        return webClient
            .get()
            .uri(url)
            .retrieve()
            .bodyToFlux(resourceClass);
    }

    public <T> Mono<T> getResourceEntity(String url, Class<T> resourceClass) {
        return webClient
            .get()
            .uri(url)
            .retrieve()
            .bodyToMono(resourceClass)
            .onErrorResume(WebClientResponseException.InternalServerError.class, e -> Mono.error(() -> new IllegalArgumentException("Server error: " + e.getResponseBodyAsString())));
    }

    public <T> Mono<T> getResourceEntity(String url, Class<T> resourceClass, MediaType mediaType) {
        return webClient
            .get()
            .uri(url)
            .accept(mediaType)
            .retrieve()
            .bodyToMono(resourceClass);
    }

    public <T> Mono<String> updateResource(String url, T payload) {
        return webClient
            .put()
            .uri(url)
            .bodyValue(payload)
            .retrieve()
            .bodyToMono(String.class)
            .onErrorResume(WebClientResponseException.BadRequest.class, e -> Mono.error(() -> new IllegalArgumentException("Bad request: " + e.getResponseBodyAsString())))
            .onErrorResume(WebClientResponseException.NotFound.class, e -> Mono.error(() -> new IllegalArgumentException("Not found: " + e.getResponseBodyAsString())))
            .onErrorResume(WebClientResponseException.InternalServerError.class, e -> Mono.error(() -> new IllegalArgumentException("Server error: " + e.getResponseBodyAsString())));
    }

    public <T> Mono<String> createResource(String url, T payload) {
        return webClient
            .post()
            .uri(url)
            .bodyValue(payload)
            .retrieve()
            .bodyToMono(String.class)
            .onErrorResume(WebClientResponseException.BadRequest.class, e -> Mono.error(() -> new IllegalArgumentException("Bad request: " + e.getResponseBodyAsString())))
            .onErrorResume(WebClientResponseException.InternalServerError.class, e -> Mono.error(() -> new IllegalArgumentException("Server error: " + e.getResponseBodyAsString())));
    }

    public Mono<String> deleteResource(String url, LinkedMultiValueMap<String, String> params) {
        return webClient
            .delete()
            .uri(url, uriBuilder ->
                uriBuilder
                    .queryParams(params)
                    .build())
            .retrieve()
            .bodyToMono(String.class)
            .onErrorResume(WebClientResponseException.BadRequest.class, e -> Mono.error(() -> new IllegalArgumentException("Bad request: " + e.getResponseBodyAsString())))
            .onErrorResume(WebClientResponseException.InternalServerError.class, e -> Mono.error(() -> new IllegalArgumentException("Server error: " + e.getResponseBodyAsString())));
    }

    public Mono<ResponseEntity<Void>> deleteResource(String url) {
        return webClient
            .delete()
            .uri(url, UriBuilder::build)
            .retrieve()
            .toBodilessEntity()
            .onErrorResume(WebClientResponseException.BadRequest.class, e -> Mono.error(() -> new IllegalArgumentException("Bad request: " + e.getResponseBodyAsString())))
            .onErrorResume(WebClientResponseException.InternalServerError.class, e -> Mono.error(() -> new IllegalArgumentException("Server error: " + e.getResponseBodyAsString())));
    }

    public Mono<ResponseEntity<Void>> deleteResource(String url, String resourceId) {
        return webClient
            .delete()
            .uri(url, resourceId)
            .retrieve()
            .toBodilessEntity()
            .onErrorResume(WebClientResponseException.BadRequest.class, e -> Mono.error(() -> new IllegalArgumentException("Bad request: " + e.getResponseBodyAsString())))
            .onErrorResume(WebClientResponseException.NotFound.class, e -> Mono.error(() -> new IllegalArgumentException("Not found: " + e.getResponseBodyAsString())))
            .onErrorResume(WebClientResponseException.InternalServerError.class, e -> Mono.error(() -> new IllegalArgumentException("Server error: " + e.getResponseBodyAsString())));
    }
}
