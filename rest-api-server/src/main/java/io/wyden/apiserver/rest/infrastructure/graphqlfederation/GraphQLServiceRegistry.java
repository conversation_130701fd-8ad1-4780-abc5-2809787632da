package io.wyden.apiserver.rest.infrastructure.graphqlfederation;

import io.wyden.apiserver.rest.infrastructure.webclient.WebClientInstance;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.graphql.client.HttpGraphQlClient;
import org.springframework.graphql.client.WebSocketGraphQlClient;
import org.springframework.stereotype.Component;
import org.springframework.web.reactive.function.client.WebClient;
import org.springframework.web.reactive.socket.client.ReactorNettyWebSocketClient;
import org.springframework.web.reactive.socket.client.WebSocketClient;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

import static io.wyden.apiserver.rest.infrastructure.graphqlfederation.FederatedService.SETTLEMENT;

@Component
public class GraphQLServiceRegistry {

    private static final Logger LOGGER = LoggerFactory.getLogger(GraphQLServiceRegistry.class);

    private final String settlementHost;
    private final Map<FederatedService, ServiceConfig> services = new ConcurrentHashMap<>();
    private final WebClient webClient;

    public GraphQLServiceRegistry(@Value("${settlement.host}") String settlementHost,
                                  WebClientInstance webClientInstance) {
        this.settlementHost = settlementHost;
        this.webClient = webClientInstance.getWebClient();
        registerSettlementService();
    }

    private void registerSettlementService() {
        String httpEndpoint = this.settlementHost + "/graphql";
        String wsEndpoint = httpEndpoint.replace("http://", "ws://").replace("https://", "wss://") + "/ws";

        registerService(SETTLEMENT,
            "remote",
            httpEndpoint,
            wsEndpoint
        );
    }

    public ServiceConfig getService(FederatedService serviceName) {
        ServiceConfig service = services.get(serviceName);
        if (service == null) {
            throw new IllegalArgumentException("Unknown service: " + serviceName);
        }
        return service;
    }

    private void registerService(FederatedService service, String type, String httpEndpoint, String wsEndpoint) {
        HttpGraphQlClient httpClient = null;
        WebSocketGraphQlClient wsClient = null;
        if ("remote".equals(type) && httpEndpoint != null) {
            // HTTP client for queries and mutations
            httpClient = HttpGraphQlClient.builder(webClient)
                .url(httpEndpoint)
                .build();

            // WebSocket client for subscriptions if endpoint is provided
            if (wsEndpoint != null) {
                WebSocketClient webSocketClient = new ReactorNettyWebSocketClient();
                wsClient = WebSocketGraphQlClient.builder(wsEndpoint, webSocketClient)
                    .build();
                LOGGER.info("Configured WebSocket client for endpoint: {}", wsEndpoint);
            }
        }

        services.put(service, new ServiceConfig( httpEndpoint, wsEndpoint, httpClient, wsClient));
    }

    public class ServiceConfig {
        private final String httpEndpoint;
        private final String wsEndpoint;
        private final HttpGraphQlClient httpClient;
        private final WebSocketGraphQlClient wsClient;

        public ServiceConfig(String httpEndpoint, String wsEndpoint,
                             HttpGraphQlClient httpClient, WebSocketGraphQlClient wsClient) {
            this.httpEndpoint = httpEndpoint;
            this.wsEndpoint = wsEndpoint;
            this.httpClient = httpClient;
            this.wsClient = wsClient;
        }

        public String getHttpEndpoint() {
            return httpEndpoint;
        }

        public String getWsEndpoint() {
            return wsEndpoint;
        }

        public HttpGraphQlClient getHttpClient() {
            return httpClient;
        }

        public WebSocketGraphQlClient getWsClient() {
            return wsClient;
        }
    }
}