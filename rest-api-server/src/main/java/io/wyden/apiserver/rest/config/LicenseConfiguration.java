package io.wyden.apiserver.rest.config;

import com.hazelcast.map.IMap;
import io.wyden.accessgateway.client.license.LicenseService;
import io.wyden.accessgateway.domain.license.LicenseState;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
public class LicenseConfiguration {

    @Bean
    LicenseService licenseService(IMap<String, LicenseState> licenseMap) {
        return new LicenseService(licenseMap);
    }
}
