package io.wyden.apiserver.rest.infrastructure.rabbit;

import com.rabbitmq.client.AMQP;
import io.micrometer.core.instrument.MeterRegistry;
import io.wyden.cloudutils.rabbitmq.ConsumptionResult;
import io.wyden.cloudutils.rabbitmq.queue.MessageConsumer;
import io.wyden.cloudutils.telemetry.Telemetry;
import io.wyden.published.client.ClientRequest;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

@Component
public class ClientRequestDLHandler implements MessageConsumer<ClientRequest> {

    private static final Logger LOGGER = LoggerFactory.getLogger(ClientRequestDLHandler.class);

    private final MeterRegistry meterRegistry;
    private final TradingMessageReturnListener tradingMessageReturnListener;

    public ClientRequestDLHandler(Telemetry telemetry, TradingMessageReturnListener tradingMessageReturnListener) {
        this.meterRegistry = telemetry.getMeterRegistry();
        this.tradingMessageReturnListener = tradingMessageReturnListener;
    }

    @Override
    public ConsumptionResult consume(ClientRequest request, AMQP.BasicProperties basicProperties) {
        LOGGER.error("ClientRequest dead-lettered: {}", request);
        updateMetrics(request, "dlq");

        tradingMessageReturnListener.handle(request);

        return ConsumptionResult.consumed();
    }

    private void updateMetrics(ClientRequest request, String handlerType) {
        try {
            String instrumentId = request.getInstrumentId();
            String messageType = request.getRequestType().name();
            String orderType = request.getOrderType().name();
            this.meterRegistry.counter("wyden.trading.response.incoming.count",
                "instrumentId", instrumentId,
                "messageType", messageType,
                "orderType", orderType,
                "handlerType", handlerType).increment();
        } catch (Exception ex) {
            LOGGER.warn("Metrics update failed", ex);
        }
    }
}
