package io.wyden.apiserver.rest.trading;

import com.hazelcast.map.IMap;
import io.micrometer.core.instrument.MeterRegistry;
import io.micrometer.core.instrument.Timer;
import io.opentelemetry.api.trace.SpanKind;
import io.wyden.apiserver.rest.infrastructure.telemetry.Meters;
import io.wyden.cloudutils.telemetry.Telemetry;
import io.wyden.cloudutils.telemetry.metrics.EmptyTimer;
import io.wyden.cloudutils.telemetry.tracing.Tracing;
import io.wyden.cloudutils.telemetry.tracing.otl.TracingConv;
import io.wyden.published.client.ClientRequest;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Repository;

import java.util.Map;
import java.util.Optional;
import java.util.concurrent.TimeUnit;

import static io.wyden.apiserver.rest.infrastructure.telemetry.Meters.storageQueryLatencyTimer;
import static io.wyden.cloudutils.telemetry.metrics.LatencyRecorder.recordLatencyIn;


@Repository
public class ClientRequestRegistry {

    private static final Logger LOGGER = LoggerFactory.getLogger(ClientRequestRegistry.class);

    private final IMap<String, ClientRequest> clientRequestMap;
    private final Tracing otlTracing;
    private final MeterRegistry meterRegistry;

    public ClientRequestRegistry(IMap<String, ClientRequest> clientRequestMap, Telemetry telemetry) {
        this.clientRequestMap = clientRequestMap;
        this.otlTracing = telemetry.getTracing();
        this.meterRegistry = telemetry.getMeterRegistry();
    }

    public void register(String orderId, ClientRequest clientRequest) {
        recordLatencyIn(latencyTimer(Meters.QueryType.CLIENT_REQUEST_REGISTER))
            .of(() -> registerInner(orderId, clientRequest));
    }

    private void registerInner(String orderId, ClientRequest clientRequest) {
        Map<String, String> baggage = Map.of(
            TracingConv.CLIENT_ID, ObjectUtils.firstNonNull(clientRequest.getClientId(), StringUtils.EMPTY),
            TracingConv.CL_ORDER_ID, ObjectUtils.firstNonNull(clientRequest.getClOrderId(), StringUtils.EMPTY),
            TracingConv.ORDER_ID, ObjectUtils.firstNonNull(orderId, StringUtils.EMPTY)
        );
        try (var ignored = otlTracing.createBaggage(baggage)) {
            try (var ignored2 = otlTracing.createSpan("registerClientRequest", SpanKind.CLIENT)) {
                clientRequestMap.set(orderId, clientRequest);
            }
        }
    }
    public Optional<ClientRequest> find(String orderId) {
        return recordLatencyIn(latencyTimer(Meters.QueryType.CLIENT_REQUEST_FIND))
            .of(() -> findInner(orderId));
    }

    private Optional<ClientRequest> findInner(String orderId) {
        return Optional.ofNullable(clientRequestMap.get(orderId));
    }

    /**
     * @param ttl time-to-live, in seconds, after which entry will be evicted from cache. 0 means no eviction.
     */
    public void evict(String orderId, int ttl) {
        recordLatencyIn(latencyTimer(Meters.QueryType.CLIENT_REQUEST_EVICT))
            .of(() -> evictInner(orderId, ttl));
    }

    private void evictInner(String orderId, int ttl) {
        Optional.ofNullable(clientRequestMap.get(orderId))
            .ifPresentOrElse(clientRequest -> clientRequestMap.set(orderId, clientRequest, ttl, TimeUnit.SECONDS),
                () -> LOGGER.warn("ClientRequest not found for orderId={}", orderId));
    }

    private Timer latencyTimer(Meters.QueryType queryType) {
        try {
            return storageQueryLatencyTimer(this.meterRegistry, queryType);
        } catch (Exception e) {
            LOGGER.warn("Unable to create latency timer", e);
            return EmptyTimer.create();
        }
    }
}
