package io.wyden.apiserver.rest.risk.mapper;

import io.wyden.apiserver.rest.referencedata.portfolio.mapper.PortfolioEntityMapper;
import io.wyden.apiserver.rest.referencedata.portfolio.model.PortfolioResponseDto;
import io.wyden.apiserver.rest.referencedata.portfolio.model.TagDto;
import io.wyden.apiserver.rest.referencedata.portfolio.service.PortfolioRepository;
import io.wyden.apiserver.rest.risk.model.PreTradeCheckModel;
import io.wyden.apiserver.rest.risk.model.PreTradeCheckModel.PreTradeCheckResponseDto;
import io.wyden.apiserver.rest.security.model.ScopeExtractor;
import io.wyden.published.risk.PreTradeCheck;
import io.wyden.published.risk.PreTradeCheckLevel;
import io.wyden.published.risk.PreTradeCheckPropertyValue;
import io.wyden.published.risk.RequestChannel;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;
import javax.annotation.Nullable;

import static io.wyden.apiserver.rest.risk.model.PreTradeCheckModel.PreTradeCheckChannel;
import static io.wyden.apiserver.rest.risk.model.PreTradeCheckModel.PreTradeCheckInputDto;
import static io.wyden.apiserver.rest.risk.model.PreTradeCheckModel.PreTradeCheckPropertyDto;
import static io.wyden.apiserver.rest.risk.model.PreTradeCheckModel.PreTradeCheckPropertyType;

@Component
public class PreTradeCheckDtoMapper {

    private final PortfolioRepository portfolioRepository;

    public PreTradeCheckDtoMapper(PortfolioRepository portfolioRepository) {
        this.portfolioRepository = portfolioRepository;
    }

    private static PreTradeCheckModel.PreTradeCheckLevel mapLevel(PreTradeCheckLevel level) {
        return switch (level) {
            case WARN -> PreTradeCheckModel.PreTradeCheckLevel.WARN;
            case BLOCK -> PreTradeCheckModel.PreTradeCheckLevel.BLOCK;
            case PRE_TRADE_CHECK_LEVEL_UNSPECIFIED, UNRECOGNIZED -> null;
        };
    }

    private static List<PreTradeCheckChannel> mapChannels(List<RequestChannel> channels) {
        return channels.stream()
            .map(PreTradeCheckDtoMapper::mapChannel)
            .filter(Objects::nonNull)
            .collect(Collectors.toList());
    }

    private static List<TagDto> mapTags(Map<String, String> tagsMap) {
        return tagsMap.entrySet().stream()
            .map(entry -> new TagDto(entry.getKey(), entry.getValue()))
            .collect(Collectors.toList());
    }

    @Nullable
    private static PreTradeCheckChannel mapChannel(RequestChannel requestChannel) {
        return switch (requestChannel) {
            case API -> PreTradeCheckChannel.API;
            case UI -> PreTradeCheckChannel.UI;
            case UNRECOGNIZED, REQUEST_CHANNEL_UNSPECIFIED -> null;
        };
    }

    public static PreTradeCheck map(PreTradeCheckInputDto dto) {
        return PreTradeCheck.newBuilder()
            .setId(dto.id())
            .setType(dto.type())
            .setLevel(mapLevel(dto.level()))
            .setEnabled(true)
            .addAllPortfolios(dto.portfolios())
            .putAllPortfolioTags(dto.portfolioTags().stream().collect(Collectors.toMap(TagDto::key, TagDto::value)))
            .addAllRequestChannels(mapChannelsToProto(dto.channels()))
            .putAllProperties(mapProperties(dto.configuration()))
            .build();
    }

    private static List<PreTradeCheckPropertyDto> mapProperties(Map<String, PreTradeCheckPropertyValue> propertiesMap) {
        return propertiesMap.entrySet().stream()
            .map(entry -> mapProperty(entry.getKey(), entry.getValue()))
            .filter(Objects::nonNull)
            .collect(Collectors.toList());
    }

    @Nullable
    private static PreTradeCheckPropertyDto mapProperty(String key, PreTradeCheckPropertyValue value) {
        final PreTradeCheckPropertyType type;
        final List<String> values;

        if (value.hasStringValue()) {
            type = PreTradeCheckPropertyType.STRING;
            values = List.of(value.getStringValue());
        } else if (value.hasDecimalValue()) {
            type = PreTradeCheckPropertyType.NUMBER;
            values = List.of(value.getDecimalValue());
        } else if (value.hasStringArray()) {
            type = PreTradeCheckPropertyType.STRING_LIST;
            values = value.getStringArray().getStringValueList();
        } else {
            return null;
        }

        return new PreTradeCheckPropertyDto(
            type,
            key,
            values
        );
    }

    private static PreTradeCheckLevel mapLevel(PreTradeCheckModel.PreTradeCheckLevel level) {
        return switch (level) {
            case WARN -> PreTradeCheckLevel.WARN;
            case BLOCK -> PreTradeCheckLevel.BLOCK;
        };
    }

    private static List<RequestChannel> mapChannelsToProto(List<PreTradeCheckChannel> channels) {
        return channels.stream().map(channel -> switch (channel) {
            case UI -> RequestChannel.UI;
            case API -> RequestChannel.API;
        }).toList();
    }

    public PreTradeCheckResponseDto map(PreTradeCheck preTradeCheck, ScopeExtractor scopeExtractor, ScopeExtractor dynamicScopeExtractor) {
        List<PortfolioResponseDto> portfolioList = preTradeCheck.getPortfoliosList().stream()
            .flatMap(portfolioId -> portfolioRepository.find(portfolioId).stream())
            .map(p -> PortfolioEntityMapper.map(p, scopeExtractor.apply(p.getId()), dynamicScopeExtractor.apply(p.getId())))
            .toList();

        return new PreTradeCheckResponseDto(
            preTradeCheck.getId(),
            preTradeCheck.getType(),
            mapLevel(preTradeCheck.getLevel()),
            portfolioList,
            mapTags(preTradeCheck.getPortfolioTagsMap()),
            mapChannels(preTradeCheck.getRequestChannelsList()),
            mapProperties(preTradeCheck.getPropertiesMap())
        );
    }

    private static Map<String, PreTradeCheckPropertyValue> mapProperties(List<PreTradeCheckPropertyDto> configuration) {
        Map<String, PreTradeCheckPropertyValue> map = new HashMap<>();
        for (PreTradeCheckPropertyDto property : configuration) {
            PreTradeCheckPropertyValue value = switch (property.type()) {
                case STRING -> PreTradeCheckPropertyValue.newBuilder().setStringValue(property.values().get(0)).build();
                case NUMBER -> PreTradeCheckPropertyValue.newBuilder().setDecimalValue(property.values().get(0)).build();
                case STRING_LIST ->
                    PreTradeCheckPropertyValue.newBuilder().setStringArray(PreTradeCheckPropertyValue.StringArray.newBuilder().addAllStringValue(property.values())).build();
            };
            map.put(property.name(), value);
        }
        return map;
    }
}
