package io.wyden.apiserver.rest.apiui;

import com.google.protobuf.InvalidProtocolBufferException;
import com.google.protobuf.Message;
import io.wyden.apiserver.rest.MockWebServerListener;
import io.wyden.apiserver.rest.WithMockCustomUser;
import io.wyden.apiserver.rest.apiui.permissions.PermissionsIntegrationTestNoDb;
import io.wyden.apiserver.rest.clob.QuotingModel;
import io.wyden.apiserver.rest.referencedata.instruments.model.dto.AssetClassDto;
import io.wyden.apiserver.rest.referencedata.instruments.model.dto.instrumentsquery.BaseInstrumentResponse;
import io.wyden.apiserver.rest.referencedata.instruments.model.dto.instrumentsquery.ForexSpotPropertiesResponse;
import io.wyden.apiserver.rest.referencedata.instruments.model.dto.instrumentsquery.InstrumentIdentifiersResponse;
import io.wyden.apiserver.rest.referencedata.instruments.model.dto.instrumentsquery.InstrumentResponseDto;
import io.wyden.apiserver.rest.referencedata.instruments.model.dto.instrumentsquery.TradingConstraintsResponse;
import io.wyden.apiserver.rest.referencedata.instruments.service.InstrumentsRepository;
import io.wyden.apiserver.rest.venueaccount.model.VenueAccountDesc;
import io.wyden.cloudutils.tools.DateUtils;
import io.wyden.published.brokerdesk.CalendarEntry;
import io.wyden.published.brokerdesk.ErrorType;
import io.wyden.published.brokerdesk.InstrumentQuotingConfig;
import io.wyden.published.brokerdesk.QuotingConfig;
import io.wyden.published.brokerdesk.QuotingConfigValidation;
import io.wyden.published.brokerdesk.QuotingSource;
import io.wyden.published.brokerdesk.QuotingSourceAccountConfig;
import io.wyden.published.brokerdesk.ValidationError;
import io.wyden.published.brokerdesk.ValidationResult;
import io.wyden.published.brokerdesk.Validations;
import io.wyden.published.common.DayOfTheWeek;
import io.wyden.published.common.TimeInAWeek;
import io.wyden.published.referencedata.AccountType;
import io.wyden.published.referencedata.AssetClass;
import io.wyden.published.referencedata.BaseInstrument;
import io.wyden.published.referencedata.ForexSpotProperties;
import io.wyden.published.referencedata.Instrument;
import io.wyden.published.referencedata.InstrumentIdentifiers;
import io.wyden.published.referencedata.TradingConstraints;
import io.wyden.published.referencedata.VenueAccount;
import io.wyden.referencedata.client.VenueAccountCacheFacade;
import okhttp3.mockwebserver.MockResponse;
import okhttp3.mockwebserver.MockWebServer;
import okhttp3.mockwebserver.RecordedRequest;
import okio.Buffer;
import org.jetbrains.annotations.NotNull;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.graphql.test.tester.GraphQlTester;
import org.springframework.test.context.DynamicPropertyRegistry;
import org.springframework.test.context.DynamicPropertySource;
import org.springframework.test.context.TestExecutionListeners;
import org.springframework.test.context.support.DependencyInjectionTestExecutionListener;
import org.testcontainers.shaded.org.bouncycastle.util.Strings;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.math.BigDecimal;
import java.time.ZonedDateTime;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Random;
import java.util.concurrent.TimeUnit;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.when;

@TestExecutionListeners(listeners = {MockWebServerListener.class, DependencyInjectionTestExecutionListener.class}, mergeMode = TestExecutionListeners.MergeMode.MERGE_WITH_DEFAULTS)
class QuotingConfigGraphQLControllerTest extends PermissionsIntegrationTestNoDb {

    public static final String CLOB_INSTRUMENT_1 = "clob-instrument-1";
    public static final String CLOB_INSTRUMENT_2 = "clob-instrument-2";
    @MockBean
    InstrumentsRepository instrumentsRepository;
    @MockBean
    VenueAccountCacheFacade venueAccountCacheFacade;

    private static MockWebServer quotingEngine;

    private final Instrument sourceInstrument1 = instrument("source1");
    private final Instrument sourceInstrument2 = instrument("source2");
    private final Instrument conversionSourceInstrument1 = instrument("conversionSource1");
    private final Instrument conversionSourceInstrument2 = instrument("conversionSource2");

    private final InstrumentResponseDto sourceInstrumentDto2 = instrumentDto("source2");
    private final InstrumentResponseDto conversionsSourceInstrumentDto2 = instrumentDto("conversionSource2");
    private String clobUid;

    @DynamicPropertySource
    static void registerMockServerProperties(DynamicPropertyRegistry registry) {
        quotingEngine = MockWebServerListener.getMockWebServer();
        registry.add("quoting.engine.host", () -> quotingEngine.url("/").toString());
    }

    @BeforeEach
    void setUp() throws InterruptedException {
        drainTheRequestQueue();

        clobUid = String.valueOf(new Random().nextLong());

        when(instrumentsRepository.find("source1")).thenReturn(sourceInstrument1);
        when(instrumentsRepository.find("source2")).thenReturn(sourceInstrument2);
        when(instrumentsRepository.find("conversionSource1")).thenReturn(conversionSourceInstrument1);
        when(instrumentsRepository.find("conversionSource2")).thenReturn(conversionSourceInstrument2);

        when(venueAccountCacheFacade.find(anyString())).thenAnswer(invocation -> Optional.of(VenueAccount.newBuilder()
            .setId(invocation.getArgument(0))
            .setVenueAccountName(Strings.toUpperCase(invocation.getArgument(0)))
            .setAccountType(AccountType.EXCHANGE)
            .build()));
    }

    @Test
    @WithMockCustomUser(name = "m") // TODO-md authorities checks to be set and tested after spec update
    void createTest() throws InterruptedException, InvalidProtocolBufferException {
        // prepare QE reply
        QuotingConfigValidation replyFromQuoting = getValidationReplyMessageWithErrors(clobUid);
        quotingEngine.enqueue(asHttpResponse(replyFromQuoting));

        // when - config create requested
        GraphQlTester.Response createResponse = executeFile("quoting-config-create", Map.of(
            "req", new QuotingModel.QuotingConfigurationInput(
                "test-config-1",
                null,
                null,
                "nostro",
                List.of("bitmex1"),
                List.of(sampleQuotingSourceAccountConfigInput()),
                new BigDecimal("0.1"),
                new BigDecimal("0.8"),
                new BigDecimal("50"),
                2000,
                15,
                true,
                List.of("hedging-acc-1"),
                new BigDecimal("0.01")
            )
        ));

        // then - config should be forwarded to QE
        RecordedRequest recordedRequest = quotingEngine.takeRequest();
        assertThat(recordedRequest.getMethod()).isEqualTo("POST");
        byte[] bytesSent = recordedRequest.getBody().readByteArray();
        assertThat(QuotingConfig.parseFrom(bytesSent)).isEqualTo(QuotingConfig.newBuilder()
            .setQuotingEnabled(false)
            .setDisplayName("test-config-1")
            .setNostroPortfolio("nostro")
            .addSourceVenueAccounts("bitmex1")
            .addAllSources(sampleProtoQuotingSourceAccountConfig())
            .setMinQuantityFactor("0.1")
            .setMaxQuantityFactor("0.8")
            .setMaximumDepth("50")
            .setThrottlingPeriod(2000)
            .setQuoteTtl(15)
            .addHedgingAccounts("hedging-acc-1")
            .setHedgingSafetyMargin("0.01")
            .build()
        );

        // then - OK returns to GQL client, with clobUid assigned
        createResponse.path("$.errors").pathDoesNotExist();
        createResponse.path("createQuotingConfiguration.clobUid")
            .entity(String.class)
            .isEqualTo(clobUid);
        createResponse.path("createQuotingConfiguration.resultsPerInstrument[0].instrumentId")
            .entity(String.class)
            .isEqualTo(CLOB_INSTRUMENT_1);
        createResponse.path("createQuotingConfiguration.resultsPerInstrument[0].isValid")
            .entity(Boolean.class)
            .isEqualTo(false);
        createResponse.path("createQuotingConfiguration.resultsPerInstrument[0].errors[0].fieldName")
            .entity(String.class)
            .isEqualTo("quotingSource");
        createResponse.path("createQuotingConfiguration.resultsPerInstrument[0].errors[0].errorType")
            .entity(String.class)
            .isEqualTo("MISSING");
        createResponse.path("createQuotingConfiguration.resultsPerInstrument[1].instrumentId")
            .entity(String.class)
            .isEqualTo(CLOB_INSTRUMENT_2);
        createResponse.path("createQuotingConfiguration.resultsPerInstrument[1].isValid")
            .entity(Boolean.class)
            .isEqualTo(false);
        createResponse.path("createQuotingConfiguration.resultsPerInstrument[1].errors[0].fieldName")
            .entity(String.class)
            .isEqualTo("quotingSource");
        createResponse.path("createQuotingConfiguration.resultsPerInstrument[1].errors[0].errorType")
            .entity(String.class)
            .isEqualTo("MISSING");
        createResponse.path("createQuotingConfiguration.resultsPerInstrument[1].errors[0].errorMessage")
            .entity(String.class)
            .isEqualTo("Quoting source cannot be missing!");
    }

    private Iterable<? extends QuotingSourceAccountConfig> sampleProtoQuotingSourceAccountConfig() {
        return List.of(QuotingSourceAccountConfig.newBuilder()
                .setVenueAccountId("bitmex1")
                .setIsDroppingQuotesOnDisconnection(true)
                .setDefaultQuoteTtl(5)
                .setDefaultQuoteTtlUnit(io.wyden.published.common.TimeUnit.MINUTES)
                .addCalendarEntries(sampleProtoCalendarEntry())
            .build());
    }

    private CalendarEntry sampleProtoCalendarEntry() {
        return CalendarEntry.newBuilder()
            .setFrom(TimeInAWeek.newBuilder()
                .setDay(DayOfTheWeek.FRIDAY)
                .setTime("19:00")
                .build())
            .setTo(TimeInAWeek.newBuilder()
                .setDay(DayOfTheWeek.MONDAY)
                .setTime("07:00")
                .build())
            .setQuoteTtl(72)
            .setQuoteTtlUnit(io.wyden.published.common.TimeUnit.HOURS)
            .setAdditionalMarkup("0.123")
            .build();
    }

    private QuotingModel.QuotingSourceAccountConfigInput sampleQuotingSourceAccountConfigInput() {
        return new QuotingModel.QuotingSourceAccountConfigInput(
            "bitmex1",
            true,
            5,
            QuotingModel.QuoteTTLUnit.MINUTES,
            List.of(sampleCalendarEntryInput())
        );
    }

    private QuotingModel.QuotingSourceAccountConfig sampleQuotingSourceAccountConfigState() {
        return new QuotingModel.QuotingSourceAccountConfig(
            "bitmex1",
            "BITMEX1",
            true,
            5,
            QuotingModel.QuoteTTLUnit.MINUTES,
            List.of(sampleCalendarEntry())
        );
    }

    private QuotingModel.QuotingCalendarInput sampleCalendarEntryInput() {
        return new QuotingModel.QuotingCalendarInput(
            QuotingModel.DayOfTheWeek.FRIDAY,
            QuotingModel.DayOfTheWeek.MONDAY,
            "19:00",
            "07:00",
            72,
            QuotingModel.QuoteTTLUnit.HOURS,
            new BigDecimal("0.123")
        );
    }

    private QuotingModel.QuotingCalendar sampleCalendarEntry() {
        return new QuotingModel.QuotingCalendar(
            QuotingModel.DayOfTheWeek.FRIDAY,
            QuotingModel.DayOfTheWeek.MONDAY,
            "19:00",
            "07:00",
            72,
            QuotingModel.QuoteTTLUnit.HOURS,
            new BigDecimal("0.123")
        );
    }

    @Test
    @WithMockCustomUser(name = "m") // TODO-md authorities checks to be set and tested after spec update
    void updateTest() throws InterruptedException, IOException {
        // when
        QuotingConfigValidation replyFromQuoting = getValidationReplyMessageWithErrors(clobUid);
        quotingEngine.enqueue(asHttpResponse(replyFromQuoting));

        GraphQlTester.Response updateResponse = executeFile("quoting-config-update", Map.of(
            "clobUid", clobUid,
            "req", new QuotingModel.QuotingConfigurationInput(
                "test-config-1-updated",
                null,
                null,
                "nostro2",
                List.of("bitmex1"),
                List.of(sampleQuotingSourceAccountConfigInput()),
                new BigDecimal("0.2"),
                new BigDecimal("0.9"),
                new BigDecimal("51"),
                3000,
                15,
                false,
                List.of("hedging-acc-1", "hedging-acc-2"),
                new BigDecimal("0.02")
            )
        ));

        // then - config should be forwarded to QE
        QuotingConfig expectedUpdateForward = QuotingConfig.newBuilder()
            .setQuotingEnabled(true)
            .setClobUid(Long.parseLong(clobUid))
            .setDisplayName("test-config-1-updated")
            .setNostroPortfolio("nostro2")
            .addSourceVenueAccounts("bitmex1")
            .addAllSources(sampleProtoQuotingSourceAccountConfig())
            .setMinQuantityFactor("0.2")
            .setMaxQuantityFactor("0.9")
            .setMaximumDepth("51")
            .setThrottlingPeriod(3000)
            .setQuoteTtl(15)
            .addHedgingAccounts("hedging-acc-1")
            .addHedgingAccounts("hedging-acc-2")
            .setHedgingSafetyMargin("0.02")
            .build();

        RecordedRequest recordedRequest2 = quotingEngine.takeRequest();
        assertThat(recordedRequest2.getMethod()).isEqualTo("PUT");
        assertThat(recordedRequest2.getPath()).contains(clobUid);
        assertThat(QuotingConfig.parseFrom(recordedRequest2.getBody().readByteArray())).isEqualTo(expectedUpdateForward);

        // then - ok returns to GQL client
        updateResponse.path("$.errors").pathDoesNotExist();
        updateResponse.path("updateQuotingConfiguration.clobUid")
            .entity(String.class)
            .isEqualTo(clobUid);
        updateResponse.path("updateQuotingConfiguration.resultsPerInstrument[0].instrumentId")
            .entity(String.class)
            .isEqualTo(CLOB_INSTRUMENT_1);
        updateResponse.path("updateQuotingConfiguration.resultsPerInstrument[0].isValid")
            .entity(Boolean.class)
            .isEqualTo(false);
        updateResponse.path("updateQuotingConfiguration.resultsPerInstrument[0].errors[0].fieldName")
            .entity(String.class)
            .isEqualTo("quotingSource");
        updateResponse.path("updateQuotingConfiguration.resultsPerInstrument[0].errors[0].errorType")
            .entity(String.class)
            .isEqualTo("MISSING");
        updateResponse.path("updateQuotingConfiguration.resultsPerInstrument[1].instrumentId")
            .entity(String.class)
            .isEqualTo(CLOB_INSTRUMENT_2);
        updateResponse.path("updateQuotingConfiguration.resultsPerInstrument[1].isValid")
            .entity(Boolean.class)
            .isEqualTo(false);
        updateResponse.path("updateQuotingConfiguration.resultsPerInstrument[1].errors[0].fieldName")
            .entity(String.class)
            .isEqualTo("quotingSource");
        updateResponse.path("updateQuotingConfiguration.resultsPerInstrument[1].errors[0].errorType")
            .entity(String.class)
            .isEqualTo("MISSING");
    }


    @Test
    @WithMockCustomUser(name = "m") // TODO-md authorities checks to be set and tested after spec update
    void instrumentLevelUpdateTest() throws InterruptedException, IOException {
        // when
        QuotingConfigValidation replyFromQuoting = getValidationReplyMessageWithoutErrors(clobUid);
        quotingEngine.enqueue(asHttpResponse(replyFromQuoting));

        GraphQlTester.Response updateResponse = executeFile("quoting-config-update-instrument", Map.of(
            "clobUid", clobUid,
            "req", new QuotingModel.InstrumentQuotingConfigurationInput(
                CLOB_INSTRUMENT_1,
                true,
                List.of(new QuotingModel.SourceConfigurationInput(
                    "source1",
                    "conversionSource1",
                    true
                ), new QuotingModel.SourceConfigurationInput(
                    "source2",
                    null,
                    false
                )),
                new BigDecimal("1000"),
                new BigDecimal("100"),
                null,
                null,
                null,
                new BigDecimal("80"),
                new BigDecimal("82"),
                15,
                new BigDecimal("0.0001")
            )
        ));

        // then - config should be forwarded to QE
        InstrumentQuotingConfig expectedUpdateForward = InstrumentQuotingConfig.newBuilder()
            .setInstrumentId(CLOB_INSTRUMENT_1)
            .setQuotingEnabled(false)
            .addQuotingSources(QuotingSource.newBuilder()
                .setSourceInstrumentId("source1")
                .setConversionSourceInstrumentId("conversionSource1")
                .setInverse(true)
                .build())
            .addQuotingSources(QuotingSource.newBuilder()
                .setSourceInstrumentId("source2")
                .setConversionSourceInstrumentId("")
                .setInverse(false)
                .build())
            .setPriceIncrement("1000")
            .setQuantityIncrement("100")
            .setQuoteTtl(15)
            .setMinQuantityFactor("80")
            .setMaxQuantityFactor("82")
            .setHedgingSafetyMargin("0.0001")
            .build();

        RecordedRequest recordedRequest = quotingEngine.takeRequest();
        assertThat(recordedRequest.getMethod()).isEqualTo("PUT");
        assertThat(recordedRequest.getPath()).contains(clobUid);
        assertThat(InstrumentQuotingConfig.parseFrom(recordedRequest.getBody().readByteArray())).isEqualTo(expectedUpdateForward);

        // then - ok returns to GQL client
        updateResponse.path("$.errors").pathDoesNotExist();
        updateResponse.path("updateInstrumentQuotingConfiguration.clobUid")
            .entity(String.class)
            .isEqualTo(clobUid);
        updateResponse.path("updateInstrumentQuotingConfiguration.resultsPerInstrument[0].instrumentId")
            .entity(String.class)
            .isEqualTo(CLOB_INSTRUMENT_1);
        updateResponse.path("updateInstrumentQuotingConfiguration.resultsPerInstrument[0].isValid")
            .entity(Boolean.class)
            .isEqualTo(true);
        updateResponse.path("updateInstrumentQuotingConfiguration.resultsPerInstrument[1].instrumentId")
            .entity(String.class)
            .isEqualTo(CLOB_INSTRUMENT_2);
        updateResponse.path("updateInstrumentQuotingConfiguration.resultsPerInstrument[1].isValid")
            .entity(Boolean.class)
            .isEqualTo(true);
    }

    @Test
    @WithMockCustomUser(name = "m") // TODO-md authorities checks to be set and tested after spec update
    void queryTest() throws IOException {
        // given two configs to return from QE
        String isoUtcTime = DateUtils.toIsoUtcTime(ZonedDateTime.now());
        QuotingConfig qc1 = QuotingConfig.newBuilder()
            .setQuotingEnabled(true)
            .setUpdatedAt(isoUtcTime)
            .setClobUid(12)
            .setNostroPortfolio("nostro21")
            .addSourceVenueAccounts("bitmex31")
            .setMinQuantityFactor("0.12")
            .setMaxQuantityFactor("0.19")
            .setMaximumDepth("511")
            .setThrottlingPeriod(3010)
            .addHedgingAccounts("jedynka")
            .addHedgingAccounts("dwojka")
            .setHedgingSafetyMargin("0.01")
            .addInstrumentQuotingConfigs(InstrumentQuotingConfig.newBuilder()
                .setInstrumentId(CLOB_INSTRUMENT_1)
                .setQuotingEnabled(true)
                .setMaximumDepth("0.1")
                .setQuantityIncrement("0.1")
                .setPriceIncrement("0.1")
                .addQuotingSources(QuotingSource.newBuilder()
                    .setSourceInstrumentId("source1")
                    .setConversionSourceInstrumentId("conversionSource1")
                    .setInverse(true)
                    .build())
                .build())
            .build();

        QuotingConfig qc2 = QuotingConfig.newBuilder()
            .setQuotingEnabled(false)
            .setClobUid(Long.parseLong(clobUid))
            .setDisplayName("test-config-1-updated")
            .setNostroPortfolio("nostro2")
            .addSourceVenueAccounts("bitmex3")
            .addAllSources(sampleProtoQuotingSourceAccountConfig())
            .setMinQuantityFactor("0.2")
            .setMaxQuantityFactor("0.9")
            .setMaximumDepth("51")
            .setThrottlingPeriod(3000)
            .setUpdatedAt(isoUtcTime)
            .setQuoteTtl(15)
            .addHedgingAccounts("trojka")
            .setHedgingSafetyMargin("0.03")
            .addAllInstrumentQuotingConfigs(List.of(
                InstrumentQuotingConfig.newBuilder()
                    .setQuotingEnabled(false)
                    .setInstrumentId(CLOB_INSTRUMENT_2)
                    .setPriceIncrement("0.2")
                    .setQuantityIncrement("0.2")
                    .setMinQuantityFactor("0.2222")
                    .setMaxQuantityFactor("0.9999")
                    .addQuotingSources(QuotingSource.newBuilder()
                        .setSourceInstrumentId("source2")
                        .setConversionSourceInstrumentId("conversionSource2")
                        .setInverse(false)
                        .build())
                    .setMaximumDepth("0.1")
                    .setQuoteTtl(15)
                    .setHedgingSafetyMargin("0.0002")
                    .build()
            ))
            .build();

        quotingEngine.enqueue(asHttpResponse(qc1, qc2));

        // when
        GraphQlTester.Response queried = executeFile("quoting-config-query", Map.of());

        // then - config from QE returns to GQL client
        queried.path("$.errors").pathDoesNotExist();
        queried.path("quotingConfiguration").entityList(QuotingModel.QuotingConfiguration.class)
            .hasSize(2)
            .contains(new QuotingModel.QuotingConfiguration(
                clobUid,
                true,
                DateUtils.isoUtcTimeToEpochMillis(isoUtcTime),
                "test-config-1-updated",
                null,
                null,
                "nostro2",
                "",
                List.of("bitmex3"),
                List.of(sampleQuotingSourceAccountConfigState()),
                List.of(new VenueAccountDesc("bitmex3", "BITMEX3")),
                new BigDecimal("0.2"),
                new BigDecimal("0.9"),
                new BigDecimal("51.0"),
                3000,
                15,
                List.of("trojka"),
                List.of(new VenueAccountDesc("trojka", "TROJKA")),
                new BigDecimal("0.03"),
                List.of(
                    new QuotingModel.InstrumentQuotingConfiguration(
                        CLOB_INSTRUMENT_2,
                        true,
                        List.of(new QuotingModel.SourceConfiguration(
                            sourceInstrumentDto2,
                            conversionsSourceInstrumentDto2,
                            false
                        )),
                        new BigDecimal("0.2"),
                        new BigDecimal("0.2"),
                        new BigDecimal("0.1"),
                        15,
                        null,
                        null,
                        new BigDecimal("0.2222"),
                        new BigDecimal("0.9999"),
                        new BigDecimal("0.00020")
                    )
                )
            ));
    }

    @Test
    @WithMockCustomUser(name = "m") // TODO-md authorities checks to be set and tested after spec update
    void quoting500isForwardedAsErrorToGqlClient() throws InterruptedException {
        // when + then
        quotingEngine.enqueue(new MockResponse().setResponseCode(500));
        executeFile("quoting-config-query", Map.of())
            .errors()
            .expect(e -> e.getMessage().contains("INTERNAL_ERROR"))
            .verify();

        RecordedRequest recordedRequest = quotingEngine.takeRequest();
        assertThat(recordedRequest.getMethod()).isEqualTo("GET");
    }

    private static @NotNull MockResponse asHttpResponse(QuotingConfig quotingConfig, QuotingConfig quotingConfig2) throws IOException {
        return new MockResponse()
            .setResponseCode(200)
            .addHeader("Content-Type", "application/x-protobuf")
            .setBody(new Buffer().write(serializeList(List.of(quotingConfig, quotingConfig2))));
    }

    private static @NotNull MockResponse asHttpResponse(QuotingConfigValidation validationReply) {
        return new MockResponse()
            .setResponseCode(200)
            .addHeader("Content-Type", "application/x-protobuf")
            .setBody(new Buffer().write(validationReply.toByteArray()));
    }

    private static @NotNull QuotingConfigValidation getValidationReplyMessageWithErrors(String clobUid) {
        return QuotingConfigValidation.newBuilder()
            .setClobUid(clobUid)
            .setValidations(Validations.newBuilder()
                .putResultPerInstrument(CLOB_INSTRUMENT_1, ValidationResult.newBuilder()
                    .addErrors(ValidationError.newBuilder()
                        .setErrorType(ErrorType.MISSING)
                        .setFieldName("quotingSource")
                        .build())
                    .build())
                .putResultPerInstrument(CLOB_INSTRUMENT_2, ValidationResult.newBuilder()
                    .addErrors(ValidationError.newBuilder()
                        .setErrorType(ErrorType.MISSING)
                        .setFieldName("quotingSource")
                        .setDescription("Quoting source cannot be missing!")
                        .build())
                    .build())
                .build())
            .build();
    }

    private static @NotNull QuotingConfigValidation getValidationReplyMessageWithoutErrors(String clobUid) {
        return QuotingConfigValidation.newBuilder()
            .setClobUid(clobUid)
            .setValidations(Validations.newBuilder()
                .putResultPerInstrument(CLOB_INSTRUMENT_1, ValidationResult.newBuilder().build())
                .putResultPerInstrument(CLOB_INSTRUMENT_2, ValidationResult.newBuilder().build())
                .build())
            .build();
    }

    public static <T extends Message> byte[] serializeList(List<T> list) throws IOException {
        ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
        for (T message : list) {
            message.writeDelimitedTo(outputStream);
        }
        return outputStream.toByteArray();
    }


    private Instrument instrument(String id) {
        return Instrument.newBuilder()
            .setBaseInstrument(BaseInstrument.newBuilder()
                .setVenueName("Test")
                .setAssetClass(AssetClass.FOREX)
                .setSymbol(id)
                .build())
            .setInstrumentIdentifiers(InstrumentIdentifiers.newBuilder()
                .setInstrumentId(id)
                .build())
            .setTradingConstraints(TradingConstraints.newBuilder()
                .setMaxQty("10")
                .setMinQuoteQty("1")
                .setMaxQuoteQty("2")
                .setQuoteQtyIncr("1")
                .build())
            .setForexSpotProperties(ForexSpotProperties.newBuilder()
                .setBaseCurrency("X")
                .build())
            .build();
    }

    private InstrumentResponseDto instrumentDto(String id) {
        return new InstrumentResponseDto(
            new BaseInstrumentResponse("Test", AssetClassDto.FOREX, null, null, null, null, id),
            new InstrumentIdentifiersResponse(null, id, null, null, null),
            new TradingConstraintsResponse(null, "10", "1", "2", null, "1", null, null, null, null, null, null),
            new ForexSpotPropertiesResponse("X"),
            null, null ,null
        );
    }

    private static void drainTheRequestQueue() throws InterruptedException {
        while (quotingEngine.takeRequest(1, TimeUnit.SECONDS) != null) {}
    }
}
