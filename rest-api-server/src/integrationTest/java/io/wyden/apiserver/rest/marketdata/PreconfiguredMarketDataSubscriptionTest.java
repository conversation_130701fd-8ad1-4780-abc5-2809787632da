package io.wyden.apiserver.rest.marketdata;

import io.wyden.apiserver.rest.marketdata.model.L1Event;
import io.wyden.apiserver.rest.marketdata.model.MdClientRequest;
import io.wyden.cloud.utils.test.ExchangeObserver;
import io.wyden.cloudutils.rabbitmq.RabbitExchange;
import io.wyden.cloudutils.rabbitmq.destination.OemsHeader;
import io.wyden.cloudutils.tools.DateUtils;
import io.wyden.published.audit.EventLogEvent;
import io.wyden.published.audit.EventLogStatus;
import io.wyden.published.common.Metadata;
import io.wyden.published.common.MetadataUtil;
import io.wyden.published.marketdata.Bid;
import io.wyden.published.marketdata.MarketDataEvent;
import io.wyden.published.marketdata.MarketDataIdentifier;
import io.wyden.published.marketdata.MarketDataRequest;
import io.wyden.published.referencedata.BaseInstrument;
import io.wyden.published.referencedata.ForexSpotProperties;
import io.wyden.published.referencedata.Instrument;
import io.wyden.published.referencedata.VenueType;
import org.assertj.core.groups.Tuple;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import reactor.core.Disposable;

import java.time.ZonedDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.UUID;
import java.util.concurrent.CopyOnWriteArrayList;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledFuture;
import java.util.concurrent.TimeUnit;

import static io.wyden.published.audit.EventLogEvent.BrokerConfigType.PRICING;
import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.when;
import static org.testcontainers.shaded.org.apache.commons.lang3.StringUtils.EMPTY;
import static org.testcontainers.shaded.org.awaitility.Awaitility.await;

public class PreconfiguredMarketDataSubscriptionTest extends MarketDataSubscriptionIntegrationTestBase {

    private static final String NULL_VENUE_ACCOUNT = null;
    private static final String PORTFOLIO_ID_1 = "p1";
    private static final String PORTFOLIO_ID_2 = "p2";
    private static final String CLIENT_ID_1 = "client1";
    private static final String CLIENT_ID_2 = "client2";
    private static final String CLIENT_REQUEST_ID_1 = "r1";
    private static final String CLIENT_REQUEST_ID_2 = "r2";
    private static final String STREAM_ID_1 = "1234";
    private static final String STREAM_ID_2 = "5678";
    private static final String STREAM_ID_1_1 = "xyz_1234";
    private static final String STREAM_ID_2_1 = "abc_5678";

    private static String instrumentId;
    private static String instrumentId2;

    private static final List<Disposable> DISPOSABLES = new ArrayList<>();

    @Autowired
    MarketDataService marketDataService;

    @Autowired
    RabbitExchange<MarketDataRequest> marketDataRequestExchange;

    @Autowired
    RabbitExchange<EventLogEvent> eventLogExchange;

    @Autowired
    RabbitExchange<MarketDataEvent> mdEvents;

    private ExchangeObserver<MarketDataRequest> configRequestObserver;
    private ScheduledFuture<?> scheduledFuture;
    private List<L1Event> receivedAndForwardedToClient1;
    private List<L1Event> receivedAndForwardedToClient2;

    @BeforeEach
    void setUp() {
        instrumentId = UUID.randomUUID().toString();
        instrumentId2 = UUID.randomUUID().toString();

        receivedAndForwardedToClient1 = new CopyOnWriteArrayList<>();
        receivedAndForwardedToClient2 = new CopyOnWriteArrayList<>();

        configRequestObserver = ExchangeObserver
            .newBuilder(rabbitIntegrator, marketDataRequestExchange, (data, p) -> MarketDataRequest.parseFrom(data), "it-req-config")
            .withRoutingKey("broker-config-service")
            .build();
        configRequestObserver.attach();

        when(instrumentsRepository.find(anyString())).thenReturn(clientSideInstrument());
    }

    @AfterEach
    void tearDown() {
        if (scheduledFuture != null) {
            scheduledFuture.cancel(true);
        }

        configRequestObserver.detach();

        DISPOSABLES.forEach(Disposable::dispose);
        DISPOSABLES.clear();
    }

    @Test
    void subscriptionRequestShouldTriggerConfigServiceRequest() {
        // when
        Disposable sub = marketDataService.subscribeL1(NULL_VENUE_ACCOUNT, instrumentId, PORTFOLIO_ID_1, new MdClientRequest(CLIENT_ID_1, CLIENT_REQUEST_ID_1)).subscribe();
        DISPOSABLES.add(sub);

        // then
        MarketDataRequest marketDataRequest = configRequestObserver.awaitMessage(mdr -> mdr.getInstrumentKey().getInstrumentId().equals(instrumentId));
        assertThat(marketDataRequest.getInstrumentKey().getInstrumentId()).isEqualTo(instrumentId);
        assertThat(marketDataRequest.getPortfolioId()).isEqualTo(PORTFOLIO_ID_1);
    }

    @Test
    void subscriptionNackShouldDisposeClientSubscription() throws InterruptedException {
        // given
        Disposable sub = marketDataService.subscribeL1(NULL_VENUE_ACCOUNT, instrumentId, PORTFOLIO_ID_1, new MdClientRequest(CLIENT_ID_1, CLIENT_REQUEST_ID_1))
            .subscribe();
        DISPOSABLES.add(sub);

        // and -- delivered to config service
        MarketDataRequest configServiceReceivedRequest = configRequestObserver.awaitMessage(mdr -> mdr.getInstrumentKey().getInstrumentId().equals(instrumentId));

        // when -- downstream service rejects with error
        nacked(configServiceReceivedRequest, "internal service error");

        // then
        Thread.sleep(2000); // refresh_interval is 1s (in tests), hard dispose takes effect after 2 * refresh_interval

        configRequestObserver.clearMessages(); // clear out the first request
        configRequestObserver.ensureNoMoreMessages(); // but verify no more

        assertThat(sub.isDisposed()).isTrue();
    }

    @Test
    void subscriptionAckShouldTriggerProperQueueBind() {
        // given
        Disposable sub = marketDataService.subscribeL1(NULL_VENUE_ACCOUNT, instrumentId, PORTFOLIO_ID_1, new MdClientRequest(CLIENT_ID_1, CLIENT_REQUEST_ID_1))
            .doOnNext(receivedAndForwardedToClient1::add)
            .subscribe();
        DISPOSABLES.add(sub);

        // and -- delivered to config, to be forwarded to pricing
        MarketDataRequest configServiceReceivedRequest = configRequestObserver.awaitMessage(mdr -> mdr.getInstrumentKey().getInstrumentId().equals(instrumentId));
        // and -- assume config service forwarded to pricing service
        pricingServiceIsProducing(STREAM_ID_1, instrumentId);

        // when
        ackedByPricingService(configServiceReceivedRequest, STREAM_ID_1);

        // then
        await().until(() -> !receivedAndForwardedToClient1.isEmpty());
    }

    @Test
    void ackedSubscriptionShouldBeRenewedWithStreamId() {
        // given -- requested by client
        Disposable sub = marketDataService.subscribeL1(NULL_VENUE_ACCOUNT, instrumentId, PORTFOLIO_ID_1, new MdClientRequest(CLIENT_ID_1, CLIENT_REQUEST_ID_1)).subscribe();
        DISPOSABLES.add(sub);

        // and -- delivered to config, to be forwarded to pricing
        MarketDataRequest configServiceReceivedRequest = configRequestObserver.awaitMessage(mdr -> mdr.getInstrumentKey().getInstrumentId().equals(instrumentId));

        // when -- acked by pricing service, with streamId
        ackedByPricingService(configServiceReceivedRequest, STREAM_ID_1);

        // then -- config service should start receiving heartbeats with streamId
        configRequestObserver.clearMessages();
        configRequestObserver.awaitMessage(mdr -> mdr.getStreamId().equals(STREAM_ID_1));
        configRequestObserver.awaitMessage(mdr -> mdr.getStreamId().equals(STREAM_ID_1));
        configRequestObserver.awaitMessage(mdr -> mdr.getStreamId().equals(STREAM_ID_1));
    }

    @Test
    void additionalSubscriberShouldReuseSubscription() {
        // given
        Disposable sub = marketDataService.subscribeL1(NULL_VENUE_ACCOUNT, instrumentId, PORTFOLIO_ID_1, new MdClientRequest(CLIENT_ID_1, CLIENT_REQUEST_ID_1))
            .doOnNext(receivedAndForwardedToClient1::add)
            .subscribe();
        DISPOSABLES.add(sub);

        // and -- delivered to config, to be forwarded to pricing
        MarketDataRequest configServiceReceivedRequest = configRequestObserver.awaitMessage(mdr -> mdr.getInstrumentKey().getInstrumentId().equals(instrumentId));
        // and -- assume config service forwarded to pricing service
        pricingServiceIsProducing(STREAM_ID_1, instrumentId);
        ackedByPricingService(configServiceReceivedRequest, STREAM_ID_1);

        // and -- first client is receiving data
        await().until(() -> !receivedAndForwardedToClient1.isEmpty());

        // when -- second client subscribes for the same thing
        Disposable sub2 = marketDataService.subscribeL1(NULL_VENUE_ACCOUNT, instrumentId, PORTFOLIO_ID_1, new MdClientRequest(CLIENT_ID_2, CLIENT_REQUEST_ID_2))
            .doOnNext(receivedAndForwardedToClient2::add)
            .subscribe();
        DISPOSABLES.add(sub2);

        // then -- second client should also receive data
        await().until(() -> !receivedAndForwardedToClient2.isEmpty());

        // and -- data for both consumers should come from the same stream
        assertThat(receivedAndForwardedToClient1)
            .extracting("identifier.instrumentId", "identifier.streamId")
            .allMatch(t -> t.equals(Tuple.tuple(instrumentId, STREAM_ID_1)));

        assertThat(receivedAndForwardedToClient2)
            .extracting("identifier.instrumentId", "identifier.streamId")
            .allMatch(t -> t.equals(Tuple.tuple(instrumentId, STREAM_ID_1)));
    }

    @Disabled
    @Test
    void subscriptionsOnTwoPortfoliosAreIsolated() {
        // given
        Disposable sub = marketDataService.subscribeL1(NULL_VENUE_ACCOUNT, instrumentId, PORTFOLIO_ID_1, new MdClientRequest(CLIENT_ID_1, CLIENT_REQUEST_ID_1))
            .doOnNext(receivedAndForwardedToClient1::add)
            .subscribe();
        DISPOSABLES.add(sub);

        // and -- delivered to config, to be forwarded to pricing
        MarketDataRequest configServiceReceivedRequest = configRequestObserver.awaitMessage(mdr -> mdr.getInstrumentKey().getInstrumentId().equals(instrumentId));
        // and -- assume config service forwarded to pricing service
        pricingServiceIsProducing(STREAM_ID_1, instrumentId);
        ackedByPricingService(configServiceReceivedRequest, STREAM_ID_1);

        // and -- first client is receiving data
        await().until(() -> !receivedAndForwardedToClient1.isEmpty());

        // when -- second client subscribes for the same thing
        Disposable sub2 = marketDataService.subscribeL1(NULL_VENUE_ACCOUNT, instrumentId, PORTFOLIO_ID_2, new MdClientRequest(CLIENT_ID_2, CLIENT_REQUEST_ID_2))
            .doOnNext(receivedAndForwardedToClient2::add)
            .subscribe();
        DISPOSABLES.add(sub2);

        // then - should be treated as distinct sub and forwarded to config
        MarketDataRequest configServiceReceivedRequest2 = configRequestObserver.awaitMessage(mdr -> mdr.getInstrumentKey().getInstrumentId().equals(instrumentId));
        // and -- delivered to pricing service eventually and streamed with different streamId
        pricingServiceIsProducing(STREAM_ID_2, instrumentId);
        ackedByPricingService(configServiceReceivedRequest2, STREAM_ID_2);

        // then -- second client should also receive data
        await().until(() -> !receivedAndForwardedToClient2.isEmpty());

        // and -- data for both consumers should come from different streams
        assertThat(receivedAndForwardedToClient1)
            .extracting("identifier.instrumentId", "identifier.streamId")
            .allMatch(t -> t.equals(Tuple.tuple(instrumentId, STREAM_ID_1)));

        assertThat(receivedAndForwardedToClient2)
            .extracting("identifier.instrumentId", "identifier.streamId")
            .allMatch(t -> t.equals(Tuple.tuple(instrumentId, STREAM_ID_2)));
    }

    @Test
    void leavingSubscriberShouldNotDisposeSharedSubscription() {
        // given
        Disposable sub1 = marketDataService.subscribeL1(NULL_VENUE_ACCOUNT, instrumentId, PORTFOLIO_ID_1, new MdClientRequest(CLIENT_ID_1, CLIENT_REQUEST_ID_1))
            .doOnNext(receivedAndForwardedToClient1::add)
            .subscribe();
        DISPOSABLES.add(sub1);

        // and -- delivered to config, to be forwarded to pricing
        MarketDataRequest configServiceReceivedRequest = configRequestObserver.awaitMessage(mdr -> mdr.getInstrumentKey().getInstrumentId().equals(instrumentId));
        // and -- assume config service forwarded to pricing service
        pricingServiceIsProducing(STREAM_ID_1, instrumentId);
        ackedByPricingService(configServiceReceivedRequest, STREAM_ID_1);

        // and -- second client subscribes for the same thing
        Disposable sub2 = marketDataService.subscribeL1(NULL_VENUE_ACCOUNT, instrumentId, PORTFOLIO_ID_1, new MdClientRequest(CLIENT_ID_2, CLIENT_REQUEST_ID_2))
            .doOnNext(receivedAndForwardedToClient2::add)
            .subscribe();
        DISPOSABLES.add(sub2);

        // and -- both clients received data
        await().until(() -> !receivedAndForwardedToClient1.isEmpty());
        await().until(() -> !receivedAndForwardedToClient2.isEmpty());

        // when -- client1 leaves
        sub1.dispose();

        // then -- client2 should keep receiving data
        receivedAndForwardedToClient2.clear();
        await().until(() -> !receivedAndForwardedToClient2.isEmpty());

        // and -- heart-beating should continue
        configRequestObserver.clearMessages();
        configRequestObserver.awaitMessage(mdr -> mdr.getStreamId().equals(STREAM_ID_1));
    }

    @Test
    void leavingSubscriberShouldNotDisposeSharedSubscription_sameStream_differentPortfolio() throws InterruptedException {
        // given
        Disposable sub1 = marketDataService.subscribeL1(NULL_VENUE_ACCOUNT, instrumentId, PORTFOLIO_ID_1, new MdClientRequest(CLIENT_ID_1, CLIENT_REQUEST_ID_1))
            .doOnNext(receivedAndForwardedToClient1::add)
            .subscribe();
        DISPOSABLES.add(sub1);

        // and -- delivered to config, to be forwarded to pricing
        MarketDataRequest configServiceReceivedRequest = configRequestObserver.awaitMessage(mdr -> mdr.getInstrumentKey().getInstrumentId().equals(instrumentId));
        // and -- assume config service forwarded to pricing service
        pricingServiceIsProducing(STREAM_ID_1, instrumentId);
        ackedByPricingService(configServiceReceivedRequest, STREAM_ID_1);

        // and -- second client subscribes - same Instrument, different Portfolio, but the underlying stream is the same - pricing returns the same StreamId
        configRequestObserver.clearMessages();
        Disposable sub2 = marketDataService.subscribeL1(NULL_VENUE_ACCOUNT, instrumentId, PORTFOLIO_ID_2, new MdClientRequest(CLIENT_ID_2, CLIENT_REQUEST_ID_2))
            .doOnNext(receivedAndForwardedToClient2::add)
            .subscribe();
        DISPOSABLES.add(sub2);

        MarketDataRequest configServiceReceivedRequest2 = configRequestObserver.awaitMessage(mdr -> mdr.getInstrumentKey().getInstrumentId().equals(instrumentId));
        // second req acked with same streamId
        ackedByPricingService(configServiceReceivedRequest2, STREAM_ID_1);

        // and -- both clients received data
        await().until(() -> !receivedAndForwardedToClient1.isEmpty());
        await().until(() -> !receivedAndForwardedToClient2.isEmpty());

        // when -- client1 leaves
        sub1.dispose();
        Thread.sleep(1000);

        // then -- client2 should keep receiving data
        receivedAndForwardedToClient2.clear();
        await().until(() -> !receivedAndForwardedToClient2.isEmpty());

        // and -- heart-beating should continue
        configRequestObserver.clearMessages();
        configRequestObserver.awaitMessage(mdr -> mdr.getStreamId().equals(STREAM_ID_1));
    }

    @Test
    void lastLeavingSubscriberShouldDisposeSubscription() {
        // given
        Disposable sub1 = marketDataService.subscribeL1(NULL_VENUE_ACCOUNT, instrumentId, PORTFOLIO_ID_1, new MdClientRequest(CLIENT_ID_1, CLIENT_REQUEST_ID_1))
            .doOnNext(receivedAndForwardedToClient1::add)
            .subscribe();
        DISPOSABLES.add(sub1);

        // and -- delivered to config, to be forwarded to pricing
        MarketDataRequest configServiceReceivedRequest = configRequestObserver.awaitMessage(mdr -> mdr.getInstrumentKey().getInstrumentId().equals(instrumentId));
        // and -- assume config service forwarded to pricing service
        pricingServiceIsProducing(STREAM_ID_1, instrumentId);
        ackedByPricingService(configServiceReceivedRequest, STREAM_ID_1);

        // and -- second client subscribes for the same thing
        Disposable sub2 = marketDataService.subscribeL1(NULL_VENUE_ACCOUNT, instrumentId, PORTFOLIO_ID_1, new MdClientRequest(CLIENT_ID_2, CLIENT_REQUEST_ID_2))
            .doOnNext(receivedAndForwardedToClient2::add)
            .subscribe();
        DISPOSABLES.add(sub2);

        // when -- both subscribers are leaving
        sub1.dispose();
        sub2.dispose();

        // then -- heart-beating should stop
        configRequestObserver.clearMessages();
        configRequestObserver.ensureNoMoreMessages();
    }

    @Test
    void configChangeForPortfolioIdShouldTriggerNewRequestsForAllSubs() {
        // given -- 2 requests made by client
        Disposable sub = marketDataService.subscribeL1(NULL_VENUE_ACCOUNT, instrumentId, PORTFOLIO_ID_1, new MdClientRequest(CLIENT_ID_1, CLIENT_REQUEST_ID_1))
            .doOnNext(receivedAndForwardedToClient1::add)
            .subscribe();
        Disposable sub2 = marketDataService.subscribeL1(NULL_VENUE_ACCOUNT, instrumentId2, PORTFOLIO_ID_1, new MdClientRequest(CLIENT_ID_1, CLIENT_REQUEST_ID_2))
            .doOnNext(receivedAndForwardedToClient2::add)
            .subscribe();
        DISPOSABLES.add(sub);
        DISPOSABLES.add(sub2);

        // and -- both requests delivered to config, to be forwarded to pricing
        await().until(() -> {
            boolean id1Received = configRequestObserver.getMessages().stream()
                .anyMatch(mdr -> mdr.getInstrumentKey().getInstrumentId().equals(instrumentId));
            boolean id2Received = configRequestObserver.getMessages().stream()
                .anyMatch(mdr -> mdr.getInstrumentKey().getInstrumentId().equals(instrumentId));
            return id1Received && id2Received;
        });
        String restApiRequest1 = configRequestObserver.getMessages().stream()
            .filter(mdr -> mdr.getInstrumentKey().getInstrumentId().equals(instrumentId))
            .map(mdr -> mdr.getMetadata().getRequestId()).findFirst().get();
        String restApiRequest2 = configRequestObserver.getMessages().stream()
            .filter(mdr -> mdr.getInstrumentKey().getInstrumentId().equals(instrumentId2))
            .map(mdr -> mdr.getMetadata().getRequestId()).findFirst().get();

        // and -- both acked by pricing service, each with streamId
        ackedByPricingService(restApiRequest1, STREAM_ID_1);
        ackedByPricingService(restApiRequest2, STREAM_ID_2);

        // and -- config service is receiving heartbeats with both streamIds
        configRequestObserver.clearMessages();
        await().until(() -> {
            boolean stream1Renewed = configRequestObserver.getMessages().stream()
                .anyMatch(mdr -> mdr.getStreamId().equals(STREAM_ID_1));
            boolean stream2Renewed = configRequestObserver.getMessages().stream()
                .anyMatch(mdr -> mdr.getStreamId().equals(STREAM_ID_2));
            return stream1Renewed && stream2Renewed;
        });

        // when -- config has changed for portfolioId
        configChanged(PORTFOLIO_ID_1, EMPTY, EMPTY);

        // then -- heart-beating with old streamId should stop
        configRequestObserver.clearMessages();
        configRequestObserver.ensureNoMoreMessages(mdr -> !mdr.getStreamId().equals(STREAM_ID_1));
        configRequestObserver.ensureNoMoreMessages(mdr -> !mdr.getStreamId().equals(STREAM_ID_2));

        // and then -- config service should receive new requests for both instruments
        // and -- both requests delivered to config, to be forwarded to pricing
        await().until(() -> {
            boolean id1Received = configRequestObserver.getMessages().stream()
                .anyMatch(mdr -> mdr.getInstrumentKey().getInstrumentId().equals(instrumentId) && !mdr.getMetadata().getRequestId().equals(restApiRequest1));
            boolean id2Received = configRequestObserver.getMessages().stream()
                .anyMatch(mdr -> mdr.getInstrumentKey().getInstrumentId().equals(instrumentId) && !mdr.getMetadata().getRequestId().equals(restApiRequest2));
            return id1Received && id2Received;
        });
        String restApiRequest1_1 = configRequestObserver.getMessages().stream()
            .filter(mdr -> mdr.getInstrumentKey().getInstrumentId().equals(instrumentId) && !mdr.getMetadata().getRequestId().equals(restApiRequest1))
            .map(mdr -> mdr.getMetadata().getRequestId()).findFirst().get();
        String restApiRequest2_1 = configRequestObserver.getMessages().stream()
            .filter(mdr -> mdr.getInstrumentKey().getInstrumentId().equals(instrumentId2) && !mdr.getMetadata().getRequestId().equals(restApiRequest2))
            .map(mdr -> mdr.getMetadata().getRequestId()).findFirst().get();

        // and -- both acked by pricing service, each with new streamId
        ackedByPricingService(restApiRequest1_1, STREAM_ID_1_1);
        ackedByPricingService(restApiRequest2_1, STREAM_ID_2_1);

        // and -- pricing service is receiving heartbeats with new streamIds
        configRequestObserver.clearMessages();
        await().until(() -> {
            boolean stream1_1Renewed = configRequestObserver.getMessages().stream()
                .anyMatch(mdr -> mdr.getStreamId().equals(STREAM_ID_1_1));
            boolean stream2_2Renewed = configRequestObserver.getMessages().stream()
                .anyMatch(mdr -> mdr.getStreamId().equals(STREAM_ID_2_1));
            return stream1_1Renewed && stream2_2Renewed;
        });

        // and -- server is propagating market data events with new stream ids to client
        pricingServiceIsProducing(STREAM_ID_1_1, instrumentId);
        pricingServiceIsProducing(STREAM_ID_2_1, instrumentId2);
        receivedAndForwardedToClient1.clear();
        receivedAndForwardedToClient2.clear();

        await().until(() -> !receivedAndForwardedToClient1.isEmpty());
        await().until(() -> !receivedAndForwardedToClient2.isEmpty());
    }

    private Instrument clientSideInstrument() {
        return Instrument.newBuilder()
            .setBaseInstrument(BaseInstrument.newBuilder()
                .setVenueType(VenueType.CLIENT)
                .setSymbol("BTCUSD@Bank")
                .setVenueName("Bank")
                .build())
            .setForexSpotProperties(ForexSpotProperties.newBuilder()
                .setBaseCurrency("BTC")
                .build())
            .build();
    }

    private void pricingServiceIsProducing(String streamId, String instrumentId) {
        scheduledFuture = Executors.newSingleThreadScheduledExecutor().scheduleAtFixedRate(() -> mdEvents.publishWithHeaders(MarketDataEvent.newBuilder()
            .setIdentifier(MarketDataIdentifier.newBuilder()
                .setInstrumentId(instrumentId)
                .setStreamId(streamId)
                .setDateTime(DateUtils.toIsoUtcTime(ZonedDateTime.now()))
                .build())
            .setBid(Bid.newBuilder()
                .setPrice("1")
                .setSize("2")
                .build())
            .build(), Map.of(OemsHeader.MD_STREAM_ID.getHeaderName(), streamId)), 0, 50, TimeUnit.MILLISECONDS);
    }

    private void ackedByPricingService(MarketDataRequest configServiceReceivedRequest, String streamId) {
        eventLogExchange.publishWithHeaders(EventLogEvent.newBuilder()
            .setMetadata(MetadataUtil.asResponseTo(configServiceReceivedRequest.getMetadata()))
            .setClientId("rest-api-server")
            .setEventType("marketDataRequest")
            .setStatus(EventLogStatus.SUCCESS)
            .setMarketDataSubscriptionCreated(EventLogEvent.MarketDataSubscriptionCreated.newBuilder()
                .setStreamId(streamId)
            )
            .build(), Map.of(OemsHeader.CLIENT_ID.getHeaderName(), "rest-api-server"));
    }

    private void ackedByPricingService(String requestId, String streamId) {
        eventLogExchange.publishWithHeaders(EventLogEvent.newBuilder()
            .setMetadata(Metadata.newBuilder()
                .setInResponseToRequestId(requestId)
                .setInResponseToRequesterId("rest-api-server")
                .build())
            .setClientId("rest-api-server")
            .setEventType("marketDataRequest")
            .setStatus(EventLogStatus.SUCCESS)
            .setMarketDataSubscriptionCreated(EventLogEvent.MarketDataSubscriptionCreated.newBuilder()
                .setStreamId(streamId)
            )
            .build(), Map.of(OemsHeader.CLIENT_ID.getHeaderName(), "rest-api-server"));
    }

    private void nacked(MarketDataRequest configServiceReceivedRequest, String description) {
        eventLogExchange.publishWithHeaders(EventLogEvent.newBuilder()
            .setMetadata(MetadataUtil.asResponseTo(configServiceReceivedRequest.getMetadata()))
            .setMarketDataSubscriptionRejected(EventLogEvent.MarketDataSubscriptionRejected.newBuilder()
                .setReason(description)
                .build())
            .setClientId("rest-api-server")
            .setDescription(description)
            .setEventType("marketDataRequest")
                .setStatus(EventLogStatus.FAILURE)
            .build(), Map.of(OemsHeader.CLIENT_ID.getHeaderName(), "rest-api-server"));
    }

    private void configChanged(String portfolioId, String portfolioGroup, String instrumentId) {
        eventLogExchange.publishWithHeaders(EventLogEvent.newBuilder()
            .setStatus(EventLogStatus.SUCCESS)
            .setBrokerConfigChanged(EventLogEvent.BrokerConfigChanged.newBuilder()
                .setPortfolioId(portfolioId)
                .setPortfolioGroup(portfolioGroup)
                .setInstrumentId(instrumentId)
                .addAffectedAreas(PRICING)
                .build())
            .build(), Map.of(OemsHeader.CLIENT_ID.getHeaderName(), "rest-api-server"));
    }
}
