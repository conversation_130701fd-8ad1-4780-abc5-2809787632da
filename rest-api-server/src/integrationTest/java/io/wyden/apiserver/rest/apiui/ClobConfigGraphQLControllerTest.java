package io.wyden.apiserver.rest.apiui;

import com.google.protobuf.InvalidProtocolBufferException;
import io.wyden.apiserver.rest.MockWebServerListener;
import io.wyden.apiserver.rest.WithMockCustomUser;
import io.wyden.apiserver.rest.apiui.permissions.PermissionsIntegrationTestNoDb;
import io.wyden.published.brokerdesk.ClobConfig;
import io.wyden.published.brokerdesk.OrderType;
import io.wyden.published.brokerdesk.TIF;
import okhttp3.mockwebserver.MockResponse;
import okhttp3.mockwebserver.MockWebServer;
import okhttp3.mockwebserver.RecordedRequest;
import okio.Buffer;
import org.junit.jupiter.api.Test;
import org.springframework.graphql.test.tester.GraphQlTester;
import org.springframework.test.context.DynamicPropertyRegistry;
import org.springframework.test.context.DynamicPropertySource;
import org.springframework.test.context.TestExecutionListeners;
import org.springframework.test.context.support.DependencyInjectionTestExecutionListener;

import java.util.List;
import java.util.Map;

import static io.wyden.apiserver.rest.apiui.SharedModel.OrderType.LIMIT;
import static io.wyden.apiserver.rest.apiui.SharedModel.OrderType.MARKET;
import static io.wyden.apiserver.rest.apiui.SharedModel.TIF.GTC;
import static io.wyden.apiserver.rest.apiui.SharedModel.TIF.IOC;
import static org.assertj.core.api.Assertions.assertThat;

@TestExecutionListeners(listeners = {MockWebServerListener.class, DependencyInjectionTestExecutionListener.class}, mergeMode = TestExecutionListeners.MergeMode.MERGE_WITH_DEFAULTS)
class ClobConfigGraphQLControllerTest extends PermissionsIntegrationTestNoDb {

    private static MockWebServer mockClobGatewayBackend;

    @DynamicPropertySource
    static void registerMockServerProperties(DynamicPropertyRegistry registry) {
        mockClobGatewayBackend = MockWebServerListener.getMockWebServer();
        registry.add("clob.gateway.host", () -> mockClobGatewayBackend.url("/").toString());
    }

    @Test
    @WithMockCustomUser(name = "m") // TODO-md authorities checks to be set and tested after spec update
    void createUpdateQueryTest() throws InterruptedException, InvalidProtocolBufferException {
        // when
        mockClobGatewayBackend.enqueue(new MockResponse().setResponseCode(200));
        GraphQlTester.Response created = executeFile("clob-config-create", Map.of(
            "account", "clobAccount",
            "orderTypes", List.of(MARKET, LIMIT),
            "tifs", List.of(GTC)
        ));

        // then - config should be forwarded to clob gateway
        RecordedRequest recordedRequest = mockClobGatewayBackend.takeRequest();
        assertThat(recordedRequest.getMethod()).isEqualTo("POST");
        byte[] bytesSent = recordedRequest.getBody().readByteArray();
        assertThat(ClobConfig.parseFrom(bytesSent)).isEqualTo(ClobConfig.newBuilder()
            .setAccount("clobAccount")
            .addAllSupportedOrderType(List.of(OrderType.MARKET, OrderType.LIMIT))
            .addAllSupportedTif(List.of(TIF.GTC))
            .build()
        );

        // then - ok returns to GQL client
        created.path("$.errors").pathDoesNotExist();
        created.path("createInternalExchangeConfiguration.status").entity(String.class).isEqualTo("created");

        // when
        mockClobGatewayBackend.enqueue(new MockResponse().setResponseCode(200));
        GraphQlTester.Response updated = executeFile("clob-config-update", Map.of(
            "account", "clobAccount",
            "orderTypes", List.of(MARKET),
            "tifs", List.of(GTC, IOC)
        ));

        // then - config should be forwarded to clob gateway
        ClobConfig expectedClobConfigAfterUpdate = ClobConfig.newBuilder()
            .setAccount("clobAccount")
            .addAllSupportedOrderType(List.of(OrderType.MARKET))
            .addAllSupportedTif(List.of(TIF.GTC, TIF.IOC))
            .build();

        RecordedRequest recordedRequest2 = mockClobGatewayBackend.takeRequest();
        assertThat(recordedRequest2.getMethod()).isEqualTo("POST");
        assertThat(ClobConfig.parseFrom(recordedRequest2.getBody().readByteArray())).isEqualTo(expectedClobConfigAfterUpdate);

        // then - ok returns to GQL client
        updated.path("$.errors").pathDoesNotExist();
        updated.path("updateInternalExchangeConfiguration.status").entity(String.class).isEqualTo("updated");

        // when
        mockClobGatewayBackend.enqueue(new MockResponse().setResponseCode(200)
            .addHeader("Content-Type", "application/x-protobuf")
            .setBody(new Buffer().write(expectedClobConfigAfterUpdate.toByteArray())));
        GraphQlTester.Response queried = executeFile("clob-config-query", Map.of("account", "clobAccount"));

        // then - config from clobGateway returns to GQL client
        queried.path("$.errors").pathDoesNotExist();
        queried.path("internalExchangeConfiguration.account").entity(String.class).isEqualTo("clobAccount");
        queried.path("internalExchangeConfiguration.availableOrderTypes").entityList(SharedModel.OrderType.class).containsExactly(MARKET);
        queried.path("internalExchangeConfiguration.availableTifs").entityList(SharedModel.TIF.class).containsExactly(GTC, IOC);
    }

    @Test
    @WithMockCustomUser(name = "m") // TODO-md authorities checks to be set and tested after spec update
    void clob404isForwardedAsEmptyConfigToGqlClient() throws InterruptedException {
        // when
        mockClobGatewayBackend.enqueue(new MockResponse().setResponseCode(404));
        GraphQlTester.Response queried = executeFile("clob-config-query", Map.of("account", "clobAccount"));

        // then - null config from clobGateway returns to GQL client
        queried.path("$.errors").pathDoesNotExist();
        queried.path("internalExchangeConfiguration").valueIsNull();

        RecordedRequest recordedRequest = mockClobGatewayBackend.takeRequest();
        assertThat(recordedRequest.getMethod()).isEqualTo("GET");
    }

    @Test
    @WithMockCustomUser(name = "m") // TODO-md authorities checks to be set and tested after spec update
    void clob500isForwardedAsErrorToGqlClient() throws InterruptedException {
        // when + then
        mockClobGatewayBackend.enqueue(new MockResponse().setResponseCode(500));
        executeFile("clob-config-query", Map.of("account", "clobAccount"))
            .errors()
            .expect(e -> e.getMessage().contains("INTERNAL_ERROR"))
            .verify();

        RecordedRequest recordedRequest = mockClobGatewayBackend.takeRequest();
        assertThat(recordedRequest.getMethod()).isEqualTo("GET");
    }
}
