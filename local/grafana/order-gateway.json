{"annotations": {"list": [{"builtIn": 1, "datasource": {"type": "grafana", "uid": "-- <PERSON><PERSON> --"}, "enable": true, "hide": true, "iconColor": "rgba(0, 211, 255, 1)", "name": "Annotations & Alerts", "target": {"limit": 100, "matchAny": false, "tags": [], "type": "dashboard"}, "type": "dashboard"}]}, "editable": true, "fiscalYearStartMonth": 0, "graphTooltip": 1, "id": 13, "links": [], "panels": [{"datasource": {"type": "Prometheus", "uid": "Prometheus"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "requests / s", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "normal"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 0}, "id": 1, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": false}, "tooltip": {"mode": "multi", "sort": "desc"}}, "pluginVersion": "11.4.0", "targets": [{"datasource": {"type": "Prometheus", "uid": "Prometheus"}, "editorMode": "builder", "exemplar": false, "expr": "sum by(handlerType) (rate(wyden_trading_request_incoming_count_total{wyden_service=\"order-gateway\", namespace=\"$namespace\"}[$__rate_interval]))", "format": "time_series", "instant": false, "interval": "", "legendFormat": "{{orderType}}", "range": true, "refId": "A"}], "title": "Incoming requests", "type": "timeseries"}, {"datasource": {"type": "Prometheus", "uid": "Prometheus"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "requests / s", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "normal"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 0}, "id": 3, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": false}, "tooltip": {"mode": "multi", "sort": "desc"}}, "pluginVersion": "11.4.0", "targets": [{"datasource": {"type": "Prometheus", "uid": "Prometheus"}, "editorMode": "builder", "exemplar": false, "expr": "sum by(messageType) (rate(wyden_trading_request_outgoing_count_total{wyden_service=\"order-gateway\", namespace=\"$namespace\"}[$__rate_interval]))", "format": "time_series", "instant": false, "interval": "", "legendFormat": "{{label}}", "range": true, "refId": "A"}], "title": "Outgoing requests", "type": "timeseries"}, {"datasource": {"type": "Prometheus", "uid": "Prometheus"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "responses / s", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "normal"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 8}, "id": 2, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": false}, "tooltip": {"mode": "multi", "sort": "desc"}}, "pluginVersion": "11.4.0", "targets": [{"datasource": {"type": "Prometheus", "uid": "Prometheus"}, "editorMode": "builder", "exemplar": false, "expr": "sum by(execType) (rate(wyden_trading_response_outgoing_count_total{wyden_service=\"order-gateway\", namespace=\"$namespace\"}[$__rate_interval]))", "format": "time_series", "instant": false, "interval": "", "legendFormat": "{{orderType}}", "range": true, "refId": "A"}], "title": "Outgoing responses", "type": "timeseries"}, {"datasource": {"type": "Prometheus", "uid": "Prometheus"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "responses / s", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "normal"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 8}, "id": 4, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": false}, "tooltip": {"mode": "multi", "sort": "desc"}}, "pluginVersion": "11.4.0", "targets": [{"datasource": {"type": "Prometheus", "uid": "Prometheus"}, "editorMode": "builder", "exemplar": false, "expr": "sum by(handlerType) (rate(wyden_trading_response_incoming_count_total{wyden_service=\"order-gateway\", namespace=\"$namespace\"}[$__rate_interval]))", "format": "time_series", "instant": false, "interval": "", "legendFormat": "{{execType}}", "range": true, "refId": "A"}], "title": "Incoming responses", "type": "timeseries"}, {"datasource": {"type": "Prometheus", "uid": "Prometheus"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "milliseconds", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 16}, "id": 5, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": false}, "tooltip": {"mode": "multi", "sort": "desc"}}, "pluginVersion": "11.4.0", "targets": [{"datasource": {"type": "Prometheus", "uid": "Prometheus"}, "disableTextWrap": false, "editorMode": "code", "expr": "(sum by(messageType) (rate(wyden_trading_request_incoming_latency_seconds_sum{namespace=\"$namespace\", wyden_service=\"order-gateway\"}[$__rate_interval])) * 1000) / on(messageType) sum by(messageType) (rate(wyden_trading_request_incoming_latency_seconds_count{namespace=\"$namespace\", wyden_service=\"order-gateway\"}[$__rate_interval]))", "fullMetaSearch": false, "includeNullMetadata": true, "legendFormat": "__auto", "range": true, "refId": "A", "useBackend": false}], "title": "Average request processing time", "type": "timeseries"}, {"datasource": {"type": "Prometheus", "uid": "Prometheus"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "milliseconds", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 16}, "id": 6, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": false}, "tooltip": {"mode": "multi", "sort": "desc"}}, "pluginVersion": "11.4.0", "targets": [{"datasource": {"type": "Prometheus", "uid": "Prometheus"}, "editorMode": "code", "expr": "(sum by(messageType) (rate(wyden_trading_response_incoming_latency_seconds_sum{namespace=\"$namespace\", wyden_service=\"order-gateway\"}[$__rate_interval])) * 1000) / on(messageType) sum by(messageType) (rate(wyden_trading_response_incoming_latency_seconds_count{namespace=\"$namespace\", wyden_service=\"order-gateway\"}[$__rate_interval]))", "legendFormat": "__auto", "range": true, "refId": "A"}], "title": "Average response processing time", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "PBFA97CFB590B2093"}, "description": "", "fieldConfig": {"defaults": {"custom": {"hideFrom": {"legend": false, "tooltip": false, "viz": false}, "scaleDistribution": {"type": "linear"}}}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 24}, "id": 9, "options": {"calculate": false, "calculation": {"xBuckets": {"mode": "size"}, "yBuckets": {"mode": "size", "scale": {"type": "linear"}, "value": ""}}, "cellGap": 0, "cellValues": {"decimals": 2}, "color": {"exponent": 0.5, "fill": "dark-orange", "mode": "scheme", "reverse": false, "scale": "exponential", "scheme": "RdBu", "steps": 32}, "exemplars": {"color": "rgba(255,0,255,0.7)"}, "filterValues": {"le": 1e-09}, "legend": {"show": true, "showLegend": true}, "rowsFrame": {"layout": "auto", "value": "orders/s"}, "tooltip": {"mode": "single", "showColorScale": false, "yHistogram": false}, "yAxis": {"axisPlacement": "left", "reverse": false, "unit": "s"}}, "pluginVersion": "11.4.0", "targets": [{"datasource": {"type": "Prometheus", "uid": "Prometheus"}, "disableTextWrap": false, "editorMode": "code", "exemplar": true, "expr": "sum by(le) (rate(wyden_trading_request_incoming_latency_seconds_bucket{namespace=\"$namespace\", wyden_service=\"order-gateway\"}[$__rate_interval]))", "format": "heatmap", "fullMetaSearch": false, "includeNullMetadata": false, "instant": false, "legendFormat": "{{le}}", "range": true, "refId": "A", "useBackend": false}], "title": "Request processing latency", "type": "heatmap"}, {"datasource": {"type": "prometheus", "uid": "PBFA97CFB590B2093"}, "description": "", "fieldConfig": {"defaults": {"custom": {"hideFrom": {"legend": false, "tooltip": false, "viz": false}, "scaleDistribution": {"type": "linear"}}}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 24}, "id": 10, "options": {"calculate": false, "calculation": {"xBuckets": {"mode": "size"}, "yBuckets": {"mode": "size", "scale": {"type": "linear"}, "value": ""}}, "cellGap": 0, "cellValues": {"decimals": 2}, "color": {"exponent": 0.5, "fill": "dark-orange", "mode": "scheme", "reverse": false, "scale": "exponential", "scheme": "RdBu", "steps": 32}, "exemplars": {"color": "rgba(255,0,255,0.7)"}, "filterValues": {"le": 1e-09}, "legend": {"show": true, "showLegend": true}, "rowsFrame": {"layout": "auto", "value": "orders/s"}, "tooltip": {"mode": "single", "showColorScale": false, "yHistogram": false}, "yAxis": {"axisPlacement": "left", "reverse": false, "unit": "s"}}, "pluginVersion": "11.4.0", "targets": [{"datasource": {"type": "Prometheus", "uid": "Prometheus"}, "disableTextWrap": false, "editorMode": "code", "exemplar": true, "expr": "sum by(le) (rate(wyden_trading_response_incoming_latency_seconds_bucket{namespace=\"$namespace\", wyden_service=\"order-gateway\"}[$__rate_interval]))", "format": "heatmap", "fullMetaSearch": false, "includeNullMetadata": false, "instant": false, "legendFormat": "{{le}}", "range": true, "refId": "A", "useBackend": false}], "title": "Response processing latency", "type": "heatmap"}, {"datasource": {"type": "prometheus", "uid": "PBFA97CFB590B2093"}, "description": "", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "milliseconds", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 32}, "id": 11, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": false}, "tooltip": {"mode": "multi", "sort": "desc"}}, "pluginVersion": "11.4.0", "targets": [{"datasource": {"type": "Prometheus", "uid": "Prometheus"}, "disableTextWrap": false, "editorMode": "code", "expr": "(sum by(accessType) (rate(wyden_security_check_latency_seconds_sum{namespace=\"$namespace\", wyden_service=\"order-gateway\"}[$__rate_interval])) * 1000) / on(accessType) sum by(accessType) (rate(wyden_security_check_latency_seconds_count{namespace=\"$namespace\", wyden_service=\"order-gateway\"}[$__rate_interval]))", "fullMetaSearch": false, "includeNullMetadata": true, "legendFormat": "__auto", "range": true, "refId": "A", "useBackend": false}], "title": "Average permission check latency", "type": "timeseries"}, {"datasource": {"type": "Prometheus", "uid": "Prometheus"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "milliseconds", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 32}, "id": 7, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": false}, "tooltip": {"mode": "multi", "sort": "desc"}}, "pluginVersion": "11.4.0", "targets": [{"datasource": {"type": "Prometheus", "uid": "Prometheus"}, "editorMode": "code", "expr": "(sum by(queryType) (rate(wyden_storage_query_latency_seconds_sum{namespace=\"$namespace\", wyden_service=\"order-gateway\"}[$__rate_interval])) * 1000) / on(queryType) sum by(queryType) (rate(wyden_storage_query_latency_seconds_count{namespace=\"$namespace\", wyden_service=\"order-gateway\"}[$__rate_interval]))", "legendFormat": "__auto", "range": true, "refId": "A"}], "title": "Average hazelcast latency", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "PBFA97CFB590B2093"}, "description": "", "fieldConfig": {"defaults": {"custom": {"hideFrom": {"legend": false, "tooltip": false, "viz": false}, "scaleDistribution": {"type": "linear"}}}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 40}, "id": 12, "options": {"calculate": false, "calculation": {"xBuckets": {"mode": "size"}, "yBuckets": {"mode": "size", "scale": {"type": "linear"}, "value": ""}}, "cellGap": 0, "cellValues": {"decimals": 2}, "color": {"exponent": 0.5, "fill": "dark-orange", "mode": "scheme", "reverse": false, "scale": "exponential", "scheme": "RdBu", "steps": 32}, "exemplars": {"color": "rgba(255,0,255,0.7)"}, "filterValues": {"le": 1e-09}, "legend": {"show": true, "showLegend": true}, "rowsFrame": {"layout": "auto", "value": "orders/s"}, "tooltip": {"mode": "single", "showColorScale": false, "yHistogram": false}, "yAxis": {"axisPlacement": "left", "reverse": false, "unit": "s"}}, "pluginVersion": "11.4.0", "targets": [{"datasource": {"type": "Prometheus", "uid": "Prometheus"}, "disableTextWrap": false, "editorMode": "builder", "exemplar": true, "expr": "sum by(le) (rate(wyden_security_check_latency_seconds_bucket{namespace=\"$namespace\", wyden_service=\"order-gateway\"}[$__rate_interval]))", "format": "heatmap", "fullMetaSearch": false, "includeNullMetadata": false, "instant": false, "legendFormat": "{{le}}", "range": true, "refId": "A", "useBackend": false}], "title": "Permission check latency", "type": "heatmap"}, {"datasource": {"type": "prometheus", "uid": "PBFA97CFB590B2093"}, "description": "", "fieldConfig": {"defaults": {"custom": {"hideFrom": {"legend": false, "tooltip": false, "viz": false}, "scaleDistribution": {"type": "linear"}}}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 40}, "id": 13, "options": {"calculate": false, "calculation": {"xBuckets": {"mode": "size"}, "yBuckets": {"mode": "size", "scale": {"type": "linear"}, "value": ""}}, "cellGap": 0, "cellValues": {"decimals": 2}, "color": {"exponent": 0.5, "fill": "dark-orange", "mode": "scheme", "reverse": false, "scale": "exponential", "scheme": "RdBu", "steps": 32}, "exemplars": {"color": "rgba(255,0,255,0.7)"}, "filterValues": {"le": 1e-09}, "legend": {"show": true, "showLegend": true}, "rowsFrame": {"layout": "auto", "value": "orders/s"}, "tooltip": {"mode": "single", "showColorScale": false, "yHistogram": false}, "yAxis": {"axisPlacement": "left", "reverse": false, "unit": "s"}}, "pluginVersion": "11.4.0", "targets": [{"datasource": {"type": "Prometheus", "uid": "Prometheus"}, "disableTextWrap": false, "editorMode": "builder", "exemplar": true, "expr": "sum by(le) (rate(wyden_storage_query_latency_seconds_bucket{namespace=\"$namespace\", wyden_service=\"order-gateway\"}[$__rate_interval]))", "format": "heatmap", "fullMetaSearch": false, "includeNullMetadata": false, "instant": false, "legendFormat": "{{le}}", "range": true, "refId": "A", "useBackend": false}], "title": "Hazelcast latency", "type": "heatmap"}, {"datasource": {"type": "Prometheus", "uid": "Prometheus"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 48}, "id": 8, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": false}, "tooltip": {"mode": "multi", "sort": "desc"}}, "pluginVersion": "11.4.0", "targets": [{"datasource": {"type": "Prometheus", "uid": "Prometheus"}, "disableTextWrap": false, "editorMode": "code", "expr": "sum(rabbitmq_queue_messages{namespace=\"$namespace\", queue=~\"order-gateway-queue.*\"})", "fullMetaSearch": false, "includeNullMetadata": true, "legendFormat": "Messages", "range": true, "refId": "A", "useBackend": false}], "title": "Queue length", "type": "timeseries"}], "preload": false, "refresh": "1m", "schemaVersion": 40, "tags": [], "templating": {"list": [{"current": {"text": "local", "value": "local"}, "datasource": {"type": "Prometheus", "uid": "Prometheus"}, "definition": "label_values(namespace)", "includeAll": false, "label": "Namespace", "name": "namespace", "options": [], "query": {"query": "label_values(namespace)", "refId": "StandardVariableQuery"}, "refresh": 1, "regex": "", "type": "query"}]}, "time": {"from": "now-1h", "to": "now"}, "timepicker": {}, "timezone": "", "title": "Order Gateway", "uid": "3b32ffe4-715e-49d6-abbd-47afbdef7208", "version": 9, "weekStart": ""}