# Wyden UI

## Overview

**Description:**
The User Interface for Wyden Platform, with the intent to be used by internally by Wyden to test the app and by clients of Wyden to play with the apps as well as on production by middleoffice teams to monitor the business cases covered by Wyden Infinity.

**Owner/Team:**
- Team/Name: `UI Team`

## Architecture
### High-Level Architecture
The Wyden UI is a React-based single page application that serves as the main interface for the Wyden Platform. It follows a modular architecture pattern with core components and feature-specific modules.
```mermaid
graph TB
    subgraph Frontend["Frontend Application"]
        Features[Feature Modules]
        State[State Management]
        Testing[Testing]
        UI[UI Layer]
    end

    subgraph Backend["Backend Services"]
        GraphQL[GraphQL API]
        REST[REST API]
        Keycloak[Keycloak Auth]
    end

    subgraph Features["Feature Modules"]
        direction TB
        Trading[Trading Features]
        Portfolio[Portfolio Management]
        Settings[Settings & Configuration]
        Analytics[Analytics & Monitoring]
    end

    subgraph Trading["Trading Features"]
        Orders[Orders]
        Transactions[Transactions]
        Positions[Positions]
        Watchlist[Watchlist]
    end

    subgraph UI["UI Layer"]
        direction TB
        Components[Custom MUI Components]
        Theme[Theme & Design System]
        Layout[Layout Components]
    end

    subgraph State["State Management"]
        Zustand[Zustand Store]
        Apollo[Apollo Client]
    end

    subgraph Testing["Testing"]
        direction TB
        Component[Cypress Component Tests]
        Unit[Vitest Unit Tests]
    end

    subgraph Backend["Backend Integration"]
        Apollo --> |Queries/Mutations/Subscriptions| GraphQL
        REST --> |API Calls| Axios
        Keycloak --> |Authentication| AuthProvider
    end

    %% Connections
    Features --> State
    Features --> UI
    Testing --> Features
    State --> Backend

    %% Legend
    classDef default fill:#1d252f,stroke:#02070d,stroke-width:2px;
    classDef frontend fill:#11161c,stroke:#ffffff,stroke-width:2px;
    classDef backend fill:#171d25,stroke:#b2b6bb,stroke-width:2px;
    classDef features fill:#1d252f,stroke:#e23e52,stroke-width:2px;
    classDef ui fill:#1d252f,stroke:#27a6e3,stroke-width:2px;
    classDef state fill:#1d252f,stroke:#4bbf28,stroke-width:2px;
    classDef testing fill:#1d252f,stroke:#db7b06,stroke-width:2px;
    classDef trading fill:#1d252f,stroke:#ef3b4d,stroke-width:2px;

    class Frontend,Features,UI,State,Testing frontend;
    class Backend backend;
    class Features features;
    class UI ui;
    class State state;
    class Testing testing;
    class Trading trading;
```



### Data Flow
The data flow to user screens is facilitated through a combination of GraphQL queries and real-time updates via GraphQL subscriptions. When a user interacts with the application, GraphQL queries are executed to fetch the necessary data from the backend, ensuring that the user interface is populated with the most current information. 

In addition to these queries, GraphQL subscriptions play a crucial role in maintaining an up-to-date view of the data. By establishing a persistent connection to the server, subscriptions allow the application to receive live updates whenever relevant data changes occur. This means that users can see real-time changes reflected on their screens without needing to refresh or manually request new data.

Together, these mechanisms create a seamless and dynamic user experience, where data flows efficiently from the backend to the frontend, ensuring that users always have access to the latest information and can interact with the application in a responsive manner.


### Domain and API
The domain for this project is defined in the `graphql-schema` project, which serves as the foundation for the data structure and types used throughout the application. The `rest-api-server` module utilizes this schema to ensure that both the GraphQL and RESTful interfaces conform to the same domain model. This alignment allows the UI to interact seamlessly with the backend services, ensuring consistent data handling and operations across different parts of the application. The primary purpose of the `rest-api-server` is to act as a gateway and translator of the models, facilitating communication and data exchange between various components and ensuring that the UI has access to all necessary functionalities.




## Functional Description
The UI provides comprehensive control over various aspects of the system, enabling users to configure business rules, instruments, flows, quoting engines, broker desks, and more. Essentially, it allows for the configuration of any business feature within the application. Additionally, the UI facilitates the monitoring of live data, including trades, positions, transactions, and market data. Users can also query archived (processed) orders and related data, such as transactions and ledger entries, as well as access audit data for thorough oversight and analysis.


## Technical Description

The Wyden UI follows a modern feature-based architecture pattern, focusing on business capabilities and clear separation of concerns:

### Core Architecture
- **Feature Modules**: Primary business capabilities organized by domain (Trading, Portfolio, etc.)
- **UI Layer**: Customized MUI components, theme, and design system
- **State Management**: Global and feature-specific state handling
- **Testing**: Component testing with Cypress and unit testing with Vitest

### Frontend Stack
- **React 18**: Core UI library with functional components and hooks
- **TypeScript**: For enhanced type safety and developer experience
- **Vite**: Fast bundling and development server
- **Material UI**: Component library providing consistent design system
- **Emotion**: CSS-in-JS solution for component styling

### State Management
- **Zustand**: Primary state management solution with Redux DevTools integration
- **Apollo Client**: GraphQL state management, caching, and real-time subscriptions

### API Integration
- **GraphQL**: Primary data exchange protocol using Apollo Client
  - Queries and Mutations for data fetching and upd ates
  - Subscriptions for real-time data updates
- **REST API**: Secondary interface for specific backend services
- **GraphQL Code Generator**: Automatic type generation from GraphQL schema

### Authentication & Authorization
- **Keycloak**: Identity and access management
- **Role-based access control**: Feature availability based on user permissions and entitlements, feature switches per license

### UI/UX Features
- **i18next**: Internationalization support
- **React Hook Form**: Form handling with Zod schema validation
- **AG Grid**: Advanced data grid for complex data visualization
- **Intro.js**: Interactive user onboarding and feature discovery
- **Notistack**: Toast notification system
- **Session recording**: For debugging and support via rrweb

### Testing
- **Cypress**: Primary testing framework for component and E2E testing
- **Testing Library**: Component testing utilities
- **MSW**: API mocking for tests and development

### Developer Experience

The application uses a feature-based folder structure, organizing code by business domain rather than technical role, enabling more efficient feature development and maintenance. GraphQL code generation provides type-safe API interactions, minimizing runtime errors.

## Service Dashboard

No Service Dashboard


## Deployment

The Wyden UI follows a streamlined deployment process leveraging GitLab CI/CD pipelines. The deployment workflow consists of the following steps:

1. **Build Process**: The GitLab CI pipeline triggers automated builds that compile the React application into optimized static assets.

2. **Docker Containerization**: These static assets are packaged into a Docker image that includes Nginx as the web server. Nginx is configured to efficiently serve the static files and handle routing for the single-page application.

3. **Versioning and Release**: The deployment utilizes Helm charts that specify the version of the UI module to be deployed, enabling consistent and controlled releases across environments.

UI_SETTINGS_VERSION in layout-configruations/ui-settings-version.ts needs to be incremented for every change in layouts or widgets (when we want to force cleaning user data)

4. **Scaling Characteristics**: As the UI is essentially a stateless application that serves static content to browsers, it can be deployed as a single instance in most scenarios. This simplifies the infrastructure requirements while maintaining reliability.

5. **Environment Configuration**: Different environment configurations (development, UAT, production) are managed through environment variables and build-time configurations, allowing the same Docker image to be deployed to various environments with appropriate settings.

The stateless nature of the frontend deployment means that scaling, if needed, can be achieved horizontally without complex state synchronization concerns, and instances can be replaced or updated with minimal disruption to users.

## Building

This project requires node >= 18.12.1

You can install the appropriate version with Node Version Manager (https://github.com/nvm-sh/nvm).

1. `nvm install v18.12.1`
2. `nvm use v18.12.1`

You can check your node version by `node --version`

### ⚙️ Setup

1. `npm install` // install dependencies
2. `npm run dev` // start wyden (remote development)
3. `npm run compose` // start wyden (local development with backend services in docker)

### 🧪 Running the tests

#### unit/integration

- run all tests with linters -> `npm run validate`

#### cypress

In order to run cypress tests locally you need to build project `npm run build-development`
first and serve it with `npm run preview` this is because we use cypress on prod
build but with development configuration (keycloak would redirect us to production
after login).

- open cypress panel -> `npm run test:open-cypress`
- run tests in console -> `npm run test:cypress`

to run locally report-portal tests please create .env file with key `REPORT_PORTAL_API_KEY=`ng frontend API key from psono

### 🚀 Releasing app to production

- TBD

## Monitoring

For internally managed environments, we utilize both Sentry and PostHog for monitoring and analytics, with credentials securely stored in Psono. 

However, for production environments, these third-party cloud applications are not implemented as they are not well-suited for banking environments, which typically have strict security and compliance requirements. Production environments rely on the bank's own monitoring and analytics infrastructure.

## Troubleshooting

### Development and Internal Environments
For environments you have direct access to, standard Chrome debugging tools provide comprehensive diagnostic capabilities for identifying and resolving issues. Additionally, Sentry reports are available for these environments, providing detailed error tracking and performance monitoring.

### Production and Client Environments
For environments where direct access is limited or not available, the application includes a session recording feature that can be activated when needed. Users experiencing issues can be guided to enable this functionality, which records their actions leading up to an error along with relevant application metrics. This recording can then be shared with the development team for analysis, providing crucial context for troubleshooting issues without requiring direct environment access or debugging capabilities.



