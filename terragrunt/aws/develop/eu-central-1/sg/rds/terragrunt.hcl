terraform {
  source = "git::https://github.com/terraform-aws-modules/terraform-aws-security-group//?ref=${include.root.locals.terraform-aws-sg}"
}

# Include all settings from the root terragrunt.hcl file
include "root" {
  path   = find_in_parent_folders("root.hcl")
  expose = true
}

dependency "vpc-default" {
  config_path = "${get_parent_terragrunt_dir()}/${include.root.locals.environment}/${include.root.locals.aws_region}/vpc"
}

dependency "eks" {
  config_path = "${get_parent_terragrunt_dir()}/${include.root.locals.environment}/${include.root.locals.aws_region}/eks-cluster/eks"
}

dependency "sg-bastion" {
  config_path = "${get_parent_terragrunt_dir()}/${include.root.locals.environment}/${include.root.locals.aws_region}/sg/bastion"
}

inputs = {
  name        = "RDS PostgreSQL"
  description = "RDS PostgreSQL"
  vpc_id      = dependency.vpc-default.outputs.vpc_id

  ingress_with_self = [
    {
      rule = "postgresql-tcp"
    }
  ]

  computed_ingress_with_source_security_group_id = [
    {
      rule                     = "postgresql-tcp"
      source_security_group_id = dependency.eks.outputs.eks.node_security_group_id
    },
    {
      rule = "postgresql-tcp"
      source_security_group_id = dependency.sg-bastion.outputs.security_group_id
    }
  ]
  number_of_computed_ingress_with_source_security_group_id = 2

  egress_with_cidr_blocks = [
    {
      from_port   = 0
      to_port     = 0
      protocol    = "-1"
      cidr_blocks = dependency.vpc-default.outputs.vpc_cidr_block
    },
  ]
}
