locals {
  env = include.root.locals.environment
}

dependency "vpc" {
  config_path = "${get_parent_terragrunt_dir()}/develop/${include.root.locals.aws_region}/vpc"
}

dependency "sg-rds" {
  config_path = "${get_parent_terragrunt_dir()}/develop/${include.root.locals.aws_region}/sg/rds"
  mock_outputs = {
    security_group_id = "sg-1234567890abcdef0"
  }
}

dependency "kms" {
  config_path = "../kms/rds"
  mock_outputs = {
    key_arn = "arn:aws:kms:eu-central-1:111122223333:key/1234abcd-12ab-34cd-56ef-1234567890ab"
  }
}

terraform {
  source = "${get_parent_terragrunt_dir()}/modules/rds-postgresql-aurora"
}

# Include all settings from the root terragrunt.hcl file
include "root" {
  path   = find_in_parent_folders("root.hcl")
  expose = true
}

inputs = {
  instance_class = include.root.locals.merged.rds.instance_class

  database_name = "${include.root.locals.merged.default_tags.client}${local.env}"
  identifier    = "${include.root.locals.merged.default_tags.client}-${local.env}"
  secret_name   = "${local.env}/${include.root.locals.merged.default_tags.client}/postgresql"

  vpc_id                 = dependency.vpc.outputs.vpc_id
  subnets                = dependency.vpc.outputs.private_subnets
  vpc_security_group_ids = [dependency.sg-rds.outputs.security_group_id]

  kms_key_id = dependency.kms.outputs.key_arn

  performance_insights_enabled = include.root.locals.merged.rds.performance_insights_enabled

  backup_retention_period = include.root.locals.merged.rds.backup_retention_period

  tags = merge(include.root.locals.default_tags)
}
