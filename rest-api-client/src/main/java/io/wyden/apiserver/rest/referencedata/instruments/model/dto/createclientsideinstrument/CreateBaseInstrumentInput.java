package io.wyden.apiserver.rest.referencedata.instruments.model.dto.createclientsideinstrument;

import io.wyden.apiserver.rest.referencedata.instruments.model.dto.AssetClassDto;

@Deprecated // use classes from io.wyden.apiserver.rest.referencedata.instruments.model.dto.create package
public record CreateBaseInstrumentInput(String venueName,
                                        AssetClassDto assetClass,
                                        String description,
                                        String quoteCurrency,
                                        String feeCurrency,
                                        String settlementCurrency,
                                        Boolean inverseContract,
                                        String symbol) {
}
