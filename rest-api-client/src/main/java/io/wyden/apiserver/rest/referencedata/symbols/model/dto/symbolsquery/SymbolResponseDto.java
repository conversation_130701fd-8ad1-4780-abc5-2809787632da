package io.wyden.apiserver.rest.referencedata.symbols.model.dto.symbolsquery;

import io.wyden.apiserver.rest.referencedata.instruments.model.dto.AssetClassDto;
import io.wyden.apiserver.rest.referencedata.symbols.model.VenueAccountNamesPerVenue;

import java.io.Serializable;
import java.util.List;
import java.util.Set;

public record SymbolResponseDto(
    String symbol,
    AssetClassDto assetClass,
    Set<VenueAccountNamesPerVenue> venues
) implements Serializable {
}
