package io.wyden.apiserver.fix.common.health;

import io.wyden.cloud.utils.spring.health.HealthConfigurer;
import org.springframework.boot.actuate.health.HealthContributor;
import org.springframework.boot.actuate.health.HealthContributorRegistry;
import org.springframework.boot.actuate.health.HealthEndpointGroups;
import org.springframework.boot.actuate.health.ReactiveHealthContributor;
import org.springframework.boot.actuate.health.ReactiveHealthContributorRegistry;
import org.springframework.context.ApplicationContext;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.Map;


@Configuration(proxyBeanMethods = false)
public class HealthConfiguration {

    @Bean
    public HealthContributorRegistry healthContributorRegistry(ApplicationContext applicationContext,
                                                               Map<String, HealthContributor> contributors,
                                                               Map<String, ReactiveHealthContributor> reactiveHealthContributors,
                                                               HealthEndpointGroups groups) {
        return HealthConfigurer.healthContributorRegistry(applicationContext, contributors, reactiveHealthContributors, groups);
    }

    @Bean
    public ReactiveHealthContributorRegistry reactiveHealthContributorRegistry(Map<String, HealthContributor> contributors,
                                                                               Map<String, ReactiveHealthContributor> reactiveHealthContributors,
                                                                               HealthEndpointGroups groups) {
        return HealthConfigurer.reactiveHealthContributorRegistry(contributors, reactiveHealthContributors, groups);
    }
}
