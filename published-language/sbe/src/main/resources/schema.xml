<?xml version="1.0" encoding="UTF-8" standalone="yes"?>
<!--
  ~ Copyright 2019-2023 Adaptive Financial Consulting Ltd.
  ~
  ~ Licensed under the Apache License, Version 2.0 (the "License");
  ~ you may not use this file except in compliance with the License.
  ~ You may obtain a copy of the License at
  ~
  ~ https://www.apache.org/licenses/LICENSE-2.0
  ~
  ~ Unless required by applicable law or agreed to in writing, software
  ~ distributed under the License is distributed on an "AS IS" BASIS,
  ~ WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
  ~ See the License for the specific language governing permissions and
  ~ limitations under the License.
  -->

<sbe:messageSchema xmlns:sbe="http://fixprotocol.io/2016/sbe"
                   package="io.wyden.sbe"
                   id="100"
                   version="1"
                   semanticVersion="0.1"
                   description="Aeron Exchange Core"
                   byteOrder="littleEndian">
    <types>
        <composite name="messageHeader" description="Message identifiers and length of message root">
            <type name="blockLength" primitiveType="uint16"/>
            <type name="templateId" primitiveType="uint16"/>
            <type name="schemaId" primitiveType="uint16"/>
            <type name="version" primitiveType="uint16"/>
        </composite>
	    <composite name="varDataEncoding">
	        <type name="length" primitiveType="uint32" maxValue="**********"/>
	        <type name="varData" primitiveType="uint8" length="0"/>
	    </composite>
        <composite name="varStringEncoding">
	        <type name="length" primitiveType="uint32" maxValue="**********"/>
	        <type name="varData" primitiveType="uint8" length="0" characterEncoding="UTF-8"/>
        </composite>
	    <composite name="varAsciiEncoding">
	        <type name="length" primitiveType="uint32" maxValue="**********"/>
	        <type name="varData" primitiveType="uint8" length="0" characterEncoding="ASCII"/>
	    </composite>        
        <composite name="groupSizeEncoding" description="Repeating group dimensions.">
            <type name="blockLength" primitiveType="uint16"/>
            <type name="numInGroup" primitiveType="uint16"/>
        </composite>
        <composite name="uuid" description="128-bit UUID">
            <type name="mostSigBits" primitiveType="uint64"/>
            <type name="leastSigBits" primitiveType="uint64"/>
        </composite>
		<enum name="OrderAction" encodingType="uint8">
		    <validValue name="ASK">0</validValue>
		    <validValue name="BID">1</validValue>	    
		</enum> 
		<enum name="OrderType" encodingType="uint8">
		    <validValue name="GTC">0</validValue>
		    <validValue name="IOC">1</validValue>	 
		    <validValue name="FOK">3</validValue>	 
		    <validValue name="GTD">5</validValue>	 
		</enum>
		<enum name="BooleanType" encodingType="uint8">
	        <validValue name="FALSE">0</validValue>
	        <validValue name="TRUE">1</validValue>
	    </enum>		
		<enum name="CommandResultCode" encodingType="int16">
	        <validValue name="NEW">0</validValue>
	        <validValue name="VALID_FOR_MATCHING_ENGINE">1</validValue>
	        <validValue name="SUCCESS">100</validValue>
	        <validValue name="ACCEPTED">110</validValue>
	        <validValue name="INVALID_SYMBOL">-1201</validValue>
	        <validValue name="INVALID_PRICE_STEP">-1202</validValue>
	        <validValue name="UNSUPPORTED_SYMBOL_TYPE">-1203</validValue>
	        <validValue name="SELF_MATCH">-1204</validValue>
	        <validValue name="MATCHING_UNKNOWN_ORDER_ID">-3002</validValue>
	        <validValue name="MATCHING_UNSUPPORTED_COMMAND">-3004</validValue>
	        <validValue name="MATCHING_INVALID_ORDER_BOOK_ID">-3005</validValue>
	        <validValue name="MATCHING_MOVE_FAILED_PRICE_OVER_RISK_LIMIT">-3041</validValue>
	        <validValue name="MATCHING_REDUCE_FAILED_WRONG_SIZE">-3051</validValue>
	        <validValue name="USER_MGMT_USER_ALREADY_EXISTS">-4001</validValue>
	        <validValue name="USER_MGMT_ACCOUNT_BALANCE_ADJUSTMENT_ALREADY_APPLIED_SAME">-4101</validValue>
	        <validValue name="USER_MGMT_ACCOUNT_BALANCE_ADJUSTMENT_ALREADY_APPLIED_MANY">-4102</validValue>
	        <validValue name="USER_MGMT_ACCOUNT_BALANCE_ADJUSTMENT_NSF">-4103</validValue>
	        <validValue name="USER_MGMT_NON_ZERO_ACCOUNT_BALANCE">-4104</validValue>
	        <validValue name="USER_MGMT_USER_NOT_SUSPENDABLE_HAS_POSITIONS">-4130</validValue>
	        <validValue name="USER_MGMT_USER_NOT_SUSPENDABLE_NON_EMPTY_ACCOUNTS">-4131</validValue>
	        <validValue name="USER_MGMT_USER_NOT_SUSPENDED">-4132</validValue>
	        <validValue name="USER_MGMT_USER_ALREADY_SUSPENDED">-4133</validValue>
	        <validValue name="USER_MGMT_USER_NOT_FOUND">-4201</validValue>
	        <validValue name="SYMBOL_MGMT_SYMBOL_ALREADY_EXISTS">-5001</validValue>
	        <validValue name="BINARY_COMMAND_FAILED">-8001</validValue>
	        <validValue name="REPORT_QUERY_UNKNOWN_TYPE">-8003</validValue>
	        <validValue name="STATE_PERSIST_RISK_ENGINE_FAILED">-8010</validValue>
	        <validValue name="STATE_PERSIST_MATCHING_ENGINE_FAILED">-8020</validValue>
	    </enum>	
		<enum name="SymbolType" encodingType="uint8">
	        <validValue name="CURRENCY_EXCHANGE_PAIR">0</validValue>
	        <validValue name="FUTURES_CONTRACT">1</validValue>
	        <validValue name="OPTION">2</validValue>
	    </enum>
        <enum name="BulkOrderType" encodingType="uint8">
            <validValue name="NEW">0</validValue>
            <validValue name="REPLACE">1</validValue>
        </enum>
		<enum name="TimerTaskType" encodingType="int32">
	        <validValue name="CANCEL_ORDER">100</validValue>
	        <validValue name="ORDER_BOOK_FULL_REFRESH">101</validValue>
	        <validValue name="ORDER_BOOK_INCREMETAL_UPDATE">102</validValue>
	    </enum>
		<enum name="SnapshotDataType" encodingType="int32">
	        <validValue name="MATCHING_ENGINE">0</validValue>
	        <validValue name="TIMER_MANAGER">1</validValue>
	    </enum>
	    
    </types>
    
    <sbe:message name="SymbolSpecificationRequest" id="1" description="Exchange Core Symbol Specification Request">
        <field name="correlation" id="1" type="int64"/>
    </sbe:message>       

    <sbe:message name="AddSymbolSpecificationRequest" id="2" description="Exchange Core Add Symbol Specification Request">
        <field name="correlation" id="1" type="int64"/>
	    <group name="symbols" id="2" dimensionType="groupSizeEncoding">	        
	        <field name="symbolId" id="1" type="int32"/>
	        <field name="symbolType" id="2" type="SymbolType"/>
	        <field name="baseCurrency" id="3" type="int32"/>
	        <field name="quoteCurrency" id="4" type="int32"/>
	        <field name="baseScaleK" id="5" type="int64"/>
	        <field name="quoteScaleK" id="6" type="int64"/>
	    </group>
    </sbe:message>

    <sbe:message name="ApiPlaceOrder" id="3" description="Exchange Core Place Order Command">
        <field name="correlation" id="1" type="int64"/>
        <field name="timestamp" id="2" type="int64"/>
        <field name="orderId" id="3" type="int64"/>
        <field name="symbol" id="4" type="int32"/>
        <field name="price" id="5" type="int64"/>
        <field name="size" id="6" type="int64"/>
        <field name="amount" id="7" type="int64"/>
        <field name="expiry" id="8" type="int64"/>
        <field name="action" id="9" type="OrderAction" />
        <field name="orderType" id="10" type="OrderType"/>
        <field name="uid" id="11" type="int64"/>
        <field name="result" id="12" type="CommandResultCode"/>
        <field name="orderUuid" id="13" type="uuid"/>
    </sbe:message>

    <sbe:message name="ApiPlaceBulkOrder" id="4" description="Exchange Core Place Bulk Order Command">
        <field name="correlation" id="1" type="int64" />
        <field name="timestamp" id="2" type="int64" />
        <field name="symbol" id="3" type="int32" />
        <field name="bulkOrderType" id="4" type="BulkOrderType" />
        <field name="orderType" id="5" type="OrderType" />
        <field name="uid" id="6" type="int64" />
        <field name="maxQtyDeviation" id="7" type="float" />
        <field name="result" id="8" type="CommandResultCode" />
        <group name="bids" id="9" dimensionType="groupSizeEncoding">
            <field name="price" id="1" type="int64" />
            <field name="size" id="2" type="int64" />
        </group>
        <group name="asks" id="10" dimensionType="groupSizeEncoding">
            <field name="price" id="1" type="int64" />
            <field name="size" id="2" type="int64" />
        </group>
    </sbe:message>

    <sbe:message name="ApiMoveOrder" id="5" description="Exchange Core Move Order Command">
        <field name="correlation" id="1" type="int64"/>
        <field name="timestamp" id="2" type="int64"/>
        <field name="orderId" id="3" type="int64"/>
        <field name="symbol" id="4" type="int32"/>
        <field name="newPrice" id="5" type="int64"/>
        <field name="uid" id="6" type="int64"/>
        <field name="result" id="7" type="CommandResultCode"/>
        <field name="orderUuid" id="8" type="uuid"/>
    </sbe:message>

    <sbe:message name="ApiReduceOrder" id="6" description="Exchange Core Reduce Order Command">
        <field name="correlation" id="1" type="int64"/>
        <field name="timestamp" id="2" type="int64"/>
        <field name="orderId" id="3" type="int64"/>
        <field name="symbol" id="4" type="int32"/>
        <field name="reduceSize" id="5" type="int64"/>
        <field name="uid" id="6" type="int64"/>
        <field name="result" id="7" type="CommandResultCode"/>
        <field name="orderUuid" id="8" type="uuid"/>
    </sbe:message>

    <sbe:message name="ApiCancelOrder" id="7" description="Exchange Core Cancel Order Command">
        <field name="correlation" id="1" type="int64"/>
        <field name="timestamp" id="2" type="int64"/>
        <field name="orderId" id="3" type="int64"/>
        <field name="symbol" id="4" type="int32"/>
        <field name="uid" id="5" type="int64"/>
        <field name="result" id="6" type="CommandResultCode"/>
        <field name="orderExpired" id="7" type="BooleanType"/>
        <field name="orderUuid" id="8" type="uuid"/>
    </sbe:message>

    <sbe:message name="ApiOrderBookRequest" id="8" description="Exchange Core Orderbook Request Command">
        <field name="correlation" id="1" type="int64"/>
        <field name="timestamp" id="2" type="int64"/>
        <field name="symbol" id="3" type="int32"/>
        <field name="depth" id="4" type="int32"/>
        <field name="result" id="5" type="CommandResultCode"/>
    </sbe:message>

    <sbe:message name="SingleUserOrdersReportQuery" id="10" description="Exchange Core Single User Orders Report Query">
        <field name="correlation" id="1" type="int64"/>
        <field name="timestamp" id="2" type="int64"/>
        <field name="uid" id="3" type="int32"/>
    </sbe:message>

    <sbe:message name="AllOrdersReportQuery" id="11" description="Exchange Core All Orders Report Query">
        <field name="correlation" id="1" type="int64"/>
        <field name="timestamp" id="2" type="int64"/>
    </sbe:message>
    
    <sbe:message name="ScheduleOrderBookTimer" id="12" description="Schedule Order Book Timer">
        <field name="correlation" id="1" type="int64"/>
        <field name="timestamp" id="2" type="int64"/>
        <field name="taskType" id="3" type="TimerTaskType"/>
        <data name="cronExpression" id="4" type="varStringEncoding"/>
    </sbe:message>
    
	<sbe:message name="CancelTimer" id="13" description="Cancel Timer">
        <field name="correlation" id="1" type="int64"/>
        <field name="timestamp" id="2" type="int64"/>
        <field name="taskType" id="3" type="TimerTaskType"/>
        <field name="id" id="4" type="int64"/>
    </sbe:message>    
    
    <sbe:message name="AddSymbolSpecificationResult" id="100" description="Exchange Core Add Symbol Specification Result">
        <field name="correlation" id="1" type="int64"/>
    </sbe:message>   
        
    <sbe:message name="SymbolSpecificationResult" id="101" description="Exchange Core Symbol Specification Result">
        <field name="correlation" id="1" type="int64"/>
	    <group name="symbols" id="2" dimensionType="groupSizeEncoding">	        
	        <field name="symbolId" id="1" type="int32"/>
	        <field name="symbolType" id="2" type="SymbolType"/>
	        <field name="baseCurrency" id="3" type="int32"/>
	        <field name="quoteCurrency" id="4" type="int32"/>
	        <field name="baseScaleK" id="5" type="int64"/>
	        <field name="quoteScaleK" id="6" type="int64"/>
	    </group>
    </sbe:message>
    
    <sbe:message name="TradeEvent" id="102" description="Exchange Core Trade Event">
        <field name="symbol" id="1" type="int32"/>
        <field name="price" id="2" type="int64"/>
        <field name="totalVolume" id="3" type="int64"/>
        <field name="takerOrderId" id="4" type="int64"/>
        <field name="takerUid" id="5" type="int64"/>
        <field name="takerTradeId" id="6" type="int64"/>
        <field name="matchId" id="7" type="int64"/>
        <field name="takerAction" id="8" type="OrderAction"/>
        <field name="takerOrderCompleted" id="9" type="BooleanType"/>
        <field name="timestamp" id="10" type="int64"/>
        <field name="takerOrderUuid" id="12" type="uuid"/>
        <group name="trades" id="11" dimensionType="groupSizeEncoding">
            <field name="makerOrderId" id="1" type="int64"/>
            <field name="makerUid" id="2" type="int64"/>
            <field name="makerTradeId" id="3" type="int64"/>
            <field name="makerOrderCompleted" id="4" type="BooleanType"/>
            <field name="price" id="5" type="int64"/>
            <field name="volume" id="6" type="int64"/>
            <field name="makerOrderUuid" id="7" type="uuid"/>
        </group>
    </sbe:message>
    
    <sbe:message name="ReduceEvent" id="103" description="Exchange Core Reduce Event">
        <field name="symbol" id="1" type="int32"/>
        <field name="reducedVolume" id="2" type="int64"/>
        <field name="orderCompleted" id="3" type="BooleanType"/>
        <field name="price" id="4" type="int64"/>
        <field name="orderId" id="5" type="int64"/>
        <field name="uid" id="6" type="int64"/>
        <field name="timestamp" id="7" type="int64"/>
        <field name="orderUuid" id="8" type="uuid"/>
    </sbe:message>     
    
    <sbe:message name="RejectEvent" id="104" description="Exchange Core Reduce Event">
        <field name="symbol" id="1" type="int32"/>
        <field name="rejectedVolume" id="2" type="int64"/>
        <field name="price" id="3" type="int64"/>
        <field name="orderId" id="4" type="int64"/>
        <field name="uid" id="5" type="int64"/>
        <field name="timestamp" id="6" type="int64"/>
        <field name="orderUuid" id="7" type="uuid"/>
    </sbe:message>  
    
    <sbe:message name="OrderBook" id="105" description="Exchange Core OrderBook Event">
        <field name="correlation" id="1" type="int64"/>
        <field name="symbol" id="2" type="int32"/>
        <field name="timestamp" id="3" type="int64"/>
	    <group name="asks" id="4" dimensionType="groupSizeEncoding">
        	<field name="price" id="1" type="int64"/>
        	<field name="volume" id="2" type="int64"/>	        
        	<field name="orders" id="3" type="int32"/>	        
	    </group>  
	    <group name="bids" id="5" dimensionType="groupSizeEncoding">
        	<field name="price" id="1" type="int64"/>
        	<field name="volume" id="2" type="int64"/>	        
        	<field name="orders" id="3" type="int32"/>	        
	    </group>
    </sbe:message>   

    <sbe:message name="SingleUserOrdersReportResult" id="106" description="Exchange Core Single User Orders Report Result">
        <field name="correlation" id="1" type="int64"/>
	    <field name="uid" id="2" type="int64"/>
	    <group name="orders" id="3" dimensionType="groupSizeEncoding">
	        <field name="symbol" id="1" type="int32"/>
	        <field name="orderId" id="2" type="int64"/>
	        <field name="price" id="3" type="int64"/>
	        <field name="size" id="4" type="int64"/>
	        <field name="filled" id="5" type="int64"/>
	        <field name="action" id="6" type="OrderAction"/>
	        <field name="uid" id="7" type="int64"/>
	        <field name="timestamp" id="8" type="int64"/>
            <field name="orderUuid" id="9" type="uuid"/>
	    </group>
    </sbe:message>   

    <sbe:message name="AllOrdersReportResult" id="107" description="Exchange Core All Orders Report Result">
        <field name="correlation" id="1" type="int64"/>
	    <group name="orders" id="2" dimensionType="groupSizeEncoding">
	        <field name="symbol" id="1" type="int32"/>
	        <field name="orderId" id="2" type="int64"/>
	        <field name="price" id="3" type="int64"/>
	        <field name="size" id="4" type="int64"/>
	        <field name="filled" id="5" type="int64"/>
	        <field name="action" id="6" type="OrderAction"/>
	        <field name="uid" id="7" type="int64"/>
	        <field name="timestamp" id="8" type="int64"/>
            <field name="orderUuid" id="9" type="uuid"/>
	    </group>
    </sbe:message>   

    <sbe:message name="SnapshotHeader" id="201" description="SnapshotHeader">
        <field name="orderId" id="2" type="int64"/>
        <field name="tradeId" id="3" type="int64"/>
        <field name="matchId" id="4" type="int64"/>
        <group name="elements" id="5" dimensionType="groupSizeEncoding">
            <field name="type" id="1" type="SnapshotDataType"/>
            <field name="length" id="2" type="int64"/>
        </group>        
    </sbe:message>

    <sbe:message name="SnapshotData" id="202" description="SnapshotData">
        <field name="type" id="1" type="SnapshotDataType"/>
        <field name="chunkOffset" id="2" type="int32"/>
        <field name="chunkLength" id="3" type="int32"/>
        <data name="chunkPayload" id="4" type="varDataEncoding"/>
    </sbe:message>

</sbe:messageSchema>
