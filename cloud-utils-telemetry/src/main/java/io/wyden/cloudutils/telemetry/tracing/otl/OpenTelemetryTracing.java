package io.wyden.cloudutils.telemetry.tracing.otl;

import io.opentelemetry.api.baggage.Baggage;
import io.opentelemetry.api.baggage.BaggageBuilder;
import io.opentelemetry.api.baggage.propagation.W3CBaggagePropagator;
import io.opentelemetry.api.common.Attributes;
import io.opentelemetry.api.trace.SpanBuilder;
import io.opentelemetry.api.trace.SpanKind;
import io.opentelemetry.api.trace.Tracer;
import io.opentelemetry.api.trace.propagation.W3CTraceContextPropagator;
import io.opentelemetry.context.Context;
import io.opentelemetry.context.propagation.ContextPropagators;
import io.opentelemetry.context.propagation.TextMapGetter;
import io.opentelemetry.context.propagation.TextMapPropagator;
import io.opentelemetry.context.propagation.TextMapSetter;
import io.opentelemetry.exporter.otlp.trace.OtlpGrpcSpanExporter;
import io.opentelemetry.sdk.OpenTelemetrySdk;
import io.opentelemetry.sdk.OpenTelemetrySdkBuilder;
import io.opentelemetry.sdk.resources.Resource;
import io.opentelemetry.sdk.trace.SdkTracerProvider;
import io.opentelemetry.sdk.trace.SdkTracerProviderBuilder;
import io.opentelemetry.sdk.trace.SpanProcessor;
import io.opentelemetry.sdk.trace.export.BatchSpanProcessor;
import io.opentelemetry.sdk.trace.export.SpanExporter;
import io.opentelemetry.semconv.resource.attributes.ResourceAttributes;
import io.wyden.cloudutils.telemetry.tracing.NoopCloseable;
import io.wyden.cloudutils.telemetry.tracing.SafeCloseable;
import io.wyden.cloudutils.telemetry.tracing.Tracing;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.Closeable;
import java.io.IOException;
import java.net.MalformedURLException;
import java.net.URL;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import javax.annotation.Nullable;
import javax.annotation.ParametersAreNonnullByDefault;


@ParametersAreNonnullByDefault
public class OpenTelemetryTracing implements Tracing {

    private static final Logger LOGGER = LoggerFactory.getLogger(OpenTelemetryTracing.class);

    private final String endpoint;
    private final String serviceName;
    private final String defaultScope;
    private final String defaultVersion;
    private final Map<String, String> defaultBaggage;

    private final SdkTracerProvider tracerProvider;
    private final OpenTelemetrySdk openTelemetry;

    private OpenTelemetryTracing(String endpoint, String serviceName, String defaultScope, String defaultVersion) {
        this.endpoint = endpoint;
        this.serviceName = serviceName;
        this.defaultScope = defaultScope;
        this.defaultVersion = defaultVersion;
        this.defaultBaggage = fillDefaultBaggage();
        this.tracerProvider = createTracerProvider(endpoint, serviceName);
        this.openTelemetry = createOpenTelemetry(tracerProvider);
    }

    public static OpenTelemetryTracing create(String endpoint, String serviceName, String defaultScope, String defaultVersion) {
        LOGGER.info("Starting OpenTelemetryTracing('{}', '{}')", endpoint, serviceName);
        return new OpenTelemetryTracing(endpoint, serviceName, defaultScope, defaultVersion);
    }

    @Override
    @SuppressWarnings("EmptyTryBlock")
    public void close() throws IOException {
        LOGGER.info("Stopping OpenTelemetryTracing('{}', '{}')", endpoint, serviceName);
        try (Closeable ignored = tracerProvider;
             Closeable ignored2 = openTelemetry) {
            // Empty
        }
    }

    public SafeCloseable createSpan(String spanName) {
        return createSpan(defaultScope, defaultVersion, spanName, SpanKind.SERVER, null);
    }

    public SafeCloseable createSpan(String spanName, SpanKind spanKind) {
        return createSpan(defaultScope, defaultVersion, spanName, spanKind, null);
    }

    public SafeCloseable createSpan(String spanName, Context parent) {
        return createSpan(defaultScope, defaultVersion, spanName, SpanKind.SERVER, parent);
    }

    public SafeCloseable createSpan(String spanName, SpanKind spanKind, Context parent) {
        return createSpan(defaultScope, defaultVersion, spanName, spanKind, parent);
    }

    public SafeCloseable createSpan(String scopeName, String version, String spanName) {
        return createSpan(scopeName, version, spanName, SpanKind.SERVER, null);
    }

    public SafeCloseable createSpan(String scopeName, String version, SpanKind spanKind, String spanName) {
        return createSpan(scopeName, version, spanName, spanKind, null);
    }

    public SafeCloseable createSpan(String scopeName, String version, String spanName, @Nullable Context parent) {
        return createSpan(scopeName, version, spanName, SpanKind.SERVER, parent);
    }

    public SafeCloseable createSpan(String scopeName, String version, String spanName, SpanKind spanKind, @Nullable Context parent) {
        try {
            Tracer tracer = openTelemetry.getTracer(scopeName, version);
            SpanBuilder spanBuilder = tracer.spanBuilder(spanName)
                .setSpanKind(spanKind);
            if (parent != null) {
                spanBuilder.setParent(parent);
            }
            return new SpanWrapper(spanBuilder.startSpan());
        } catch (Exception ex) {
            LOGGER.error("Exception in OpenTelemetryTracing.createSpan()", ex);
            return new NoopCloseable();
        }
    }

    public SafeCloseable createBaggage(Context context) {
        try {
            BaggageBuilder baggageBuilder = Baggage.fromContext(context).toBuilder();
            defaultBaggage.forEach(baggageBuilder::put);
            return new BaggageWrapper(baggageBuilder.build());
        } catch (Exception ex) {
            LOGGER.error("Exception in OpenTelemetryTracing.createBaggage(Context)", ex);
            return new NoopCloseable();
        }
    }

    public SafeCloseable createBaggage(Map<String, String> content) {
        try {
            BaggageBuilder baggageBuilder = Baggage.current().toBuilder();
            defaultBaggage.forEach(baggageBuilder::put);
            content.forEach(baggageBuilder::put);
            return new BaggageWrapper(baggageBuilder.build());
        } catch (Exception ex) {
            LOGGER.error("Exception in OpenTelemetryTracing.createBaggage(Map)", ex);
            return new NoopCloseable();
        }
    }

    public <C> Context loadContext(C propagator, TextMapGetter<C> getter) {
        try {
            TextMapPropagator textMapPropagator = openTelemetry.getPropagators().getTextMapPropagator();
            return textMapPropagator.extract(Context.current(), propagator, getter);
        } catch (Exception ex) {
            LOGGER.error("Exception in OpenTelemetryTracing.loadContext()", ex);
            return Context.current();
        }
    }

    public <C> C saveContext(C propagator, TextMapSetter<C> setter) {
        try {
            TextMapPropagator textMapPropagator = openTelemetry.getPropagators().getTextMapPropagator();
            textMapPropagator.inject(Context.current(), propagator, setter);
            return propagator;
        } catch (Exception ex) {
            LOGGER.error("Exception in OpenTelemetryTracing.saveContext()", ex);
            return propagator;
        }
    }

    private static SdkTracerProvider createTracerProvider(String endpoint, String serviceName) {
        Resource inner = Resource.create(
            Attributes.of(ResourceAttributes.SERVICE_NAME, serviceName)
        );
        Resource resource = Resource.getDefault().merge(inner);

        SpanExporter spanExporter = createSpanExporter(endpoint);

        SpanProcessor spanProcessor = SpanProcessor.composite(
            BatchSpanProcessor.builder(spanExporter).build(),
            BaggageSpanProcessor.create()
        );

        SdkTracerProviderBuilder sdkTracerProviderBuilder = SdkTracerProvider.builder()
            .addSpanProcessor(spanProcessor)
            .setResource(resource);
        return sdkTracerProviderBuilder.build();
    }

    private static SpanExporter createSpanExporter(String endpoint) {
        if (StringUtils.isBlank(endpoint) || !isValidUrl(endpoint)) {
            LOGGER.info("Invalid tracing collector endpoint: {}, tracing disabled.", endpoint);
            return SpanExporter.composite(List.of());
        } else {
            return OtlpGrpcSpanExporter.builder()
                .setEndpoint(endpoint)
                .build();
        }
    }

    private static boolean isValidUrl(String url) {
        try {
            new URL(url);
            return true;
        } catch (MalformedURLException e) {
            return false;
        }
    }

    private static OpenTelemetrySdk createOpenTelemetry(SdkTracerProvider sdkTracerProvider) {
        ContextPropagators propagators = ContextPropagators.create(
            TextMapPropagator.composite(
                W3CTraceContextPropagator.getInstance(),
                W3CBaggagePropagator.getInstance()
            )
        );
        OpenTelemetrySdkBuilder openTelemetryBuilder = OpenTelemetrySdk.builder()
            .setTracerProvider(sdkTracerProvider)
            .setPropagators(propagators);
        return openTelemetryBuilder.build();
    }

    private static Map<String, String> fillDefaultBaggage() {
        Map<String, String> result = new HashMap<>();
        try {
            String podName = System.getenv("HOSTNAME");
            if (StringUtils.isNotBlank(podName)) {
                result.put("k8s.pod.name", podName);
            }
            String namespace = System.getenv("K8S_NAMESPACE");
            if (StringUtils.isNotBlank(namespace)) {
                result.put("k8s.namespace.name", namespace);
            }
            return result;
        } catch (Exception ex) {
            LOGGER.error("Exception when populating defaultBaggage", ex);
            return Map.of();
        }
    }
}
