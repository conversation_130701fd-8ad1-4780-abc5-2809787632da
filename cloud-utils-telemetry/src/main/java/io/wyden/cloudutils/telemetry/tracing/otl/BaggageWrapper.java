package io.wyden.cloudutils.telemetry.tracing.otl;

import io.opentelemetry.api.baggage.Baggage;
import io.opentelemetry.context.Context;
import io.opentelemetry.context.Scope;
import io.wyden.cloudutils.telemetry.tracing.SafeCloseable;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.annotation.ParametersAreNonnullByDefault;


@ParametersAreNonnullByDefault
class BaggageWrapper implements SafeCloseable {

    private static final Logger LOGGER = LoggerFactory.getLogger(BaggageWrapper.class);

    private Scope scope;
    private Baggage baggage;

    BaggageWrapper(Baggage baggage) {
        this.baggage = baggage;
        this.scope = baggage.storeInContext(Context.current()).makeCurrent();
        LOGGER.debug("Started Baggage scope: {}", baggage);
    }

    @Override
    public void close() {
        closeScope();
    }

    private void closeScope() {
        try {
            LOGGER.debug("Closing Baggage scope: {}", baggage);
            if (scope != null) {
                scope.close();
                scope = null;
            }
            baggage = null;
        } catch (Exception ex) {
            // Don't want any problems to propagate outside of observability module
            LOGGER.error("Exception:", ex);
        }
    }
}
