package io.wyden.executionengine.service.street.inbound;

import com.rabbitmq.client.AMQP;
import io.micrometer.core.instrument.MeterRegistry;
import io.opentelemetry.api.trace.Span;
import io.opentelemetry.api.trace.SpanKind;
import io.wyden.cloudutils.rabbitmq.ConsumptionResult;
import io.wyden.cloudutils.rabbitmq.queue.MessageConsumer;
import io.wyden.cloudutils.telemetry.Telemetry;
import io.wyden.cloudutils.telemetry.tracing.Tracing;
import io.wyden.cloudutils.telemetry.tracing.otl.TracingConv;
import io.wyden.executionengine.service.autohedger.AutoHedgerService;
import io.wyden.executionengine.service.utils.ProtobufUtils;
import io.wyden.published.oems.OemsRequest;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;

import static io.opentelemetry.api.trace.StatusCode.ERROR;
import static io.wyden.cloudutils.rabbitmq.ConsumptionResult.consumed;
import static io.wyden.cloudutils.rabbitmq.ConsumptionResult.failureNonRecoverable;

@Component
public class OemsStreetRequestHandler implements MessageConsumer<OemsRequest> {

    private static final Logger LOGGER = LoggerFactory.getLogger(OemsStreetRequestHandler.class);

    private final AutoHedgerService autoHedgerService;
    private final Tracing otlTracing;
    private final MeterRegistry meterRegistry;

    public OemsStreetRequestHandler(Telemetry telemetry, @Lazy AutoHedgerService autoHedgerService) {
        this.autoHedgerService = autoHedgerService;
        this.otlTracing = telemetry.getTracing();
        this.meterRegistry = telemetry.getMeterRegistry();
    }

    @Override
    public ConsumptionResult consume(OemsRequest request, AMQP.BasicProperties properties) {
        try (var ignored = otlTracing.createSpan("oemsrequest.consume.street", SpanKind.INTERNAL)) {
            updateMetrics(request);
            return consumeInner(request, properties);
        }
    }

    private ConsumptionResult consumeInner(OemsRequest request, AMQP.BasicProperties properties) {
        LOGGER.info("New OemsRequest received: {}", request);
        try {
            OemsRequest.OemsRequestType requestType = request.getRequestType();
            switch (requestType) {
                case ORDER_SINGLE -> autoHedgerService.onOemsRequest(request);
                case CANCEL -> {
                    LOGGER.error("Cancel received on Nostro side - discarding {}", request);
                    Span.current().setStatus(ERROR, "Cancel received on Nostro side - discarding");
                }
                default -> {
                    LOGGER.error("Unknown request type: {} - discarding", request);
                    Span.current().setStatus(ERROR, "Unknown request type - discarding");
                }
            }
        } catch (Exception e) {
            LOGGER.error("Failed to consume incoming: %s, discarding...".formatted(request), e);
            Span.current().recordException(e);
            Span.current().setStatus(ERROR);
            return failureNonRecoverable();
        }

        return consumed();
    }

    private void updateMetrics(OemsRequest request) {
        try {
            String instrumentId = request.getInstrumentId();
            String side = request.getSide().name();
            double qty = ProtobufUtils.toBigDecimal(request.getQuantity()).doubleValue();
            OemsRequest.OemsRequestType requestType = request.getRequestType();
            switch (requestType) {
                case ORDER_SINGLE -> {
                    this.meterRegistry.counter("wyden.order.count", "instrumentId", instrumentId, "side", side, "venueType", "street").increment();
                    this.meterRegistry.counter("wyden.order.volume", "instrumentId", instrumentId, "side", side, "venueType", "street").increment(qty);
                }
                case CANCEL -> this.meterRegistry.counter("wyden.cancel.count", "instrumentId", instrumentId, "side", side, "venueType", "street").increment();
            }
        } catch (Exception ex) {
            LOGGER.warn("Metrics update failed", ex);
        }
    }
}
