package io.wyden.connector.diagnostic;

import io.wyden.published.diagnostic.HealthStatus;
import org.springframework.context.ApplicationEvent;

public class CapabilityStateChangedEvent extends ApplicationEvent {
    final String account;
    final HealthStatus status;

    public CapabilityStateChangedEvent(Object source, String account, HealthStatus status) {
        super(source);
        this.account = account;
        this.status = status;
    }

}