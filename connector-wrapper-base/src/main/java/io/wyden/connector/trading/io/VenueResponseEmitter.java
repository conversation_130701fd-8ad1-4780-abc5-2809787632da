package io.wyden.connector.trading.io;

import com.google.protobuf.Message;
import io.micrometer.core.instrument.MeterRegistry;
import io.micrometer.core.instrument.Timer;
import io.opentelemetry.api.trace.SpanKind;
import io.wyden.cloudutils.rabbitmq.RabbitExchange;
import io.wyden.cloudutils.rabbitmq.RabbitIntegrator;
import io.wyden.cloudutils.rabbitmq.destination.OemsExchange;
import io.wyden.cloudutils.rabbitmq.destination.OemsHeader;
import io.wyden.cloudutils.telemetry.Telemetry;
import io.wyden.cloudutils.telemetry.metrics.EmptyTimer;
import io.wyden.cloudutils.telemetry.tracing.Tracing;
import io.wyden.cloudutils.telemetry.tracing.otl.RabbitHeadersPropagator;
import io.wyden.cloudutils.telemetry.tracing.otl.TracingConv;
import io.wyden.published.venue.VenueResponse;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Map;

import static io.wyden.cloudutils.telemetry.metrics.LatencyRecorder.recordLatencyIn;
import static io.wyden.connector.infra.Meters.tradingResponseOutgoingLatencyTimer;

public class VenueResponseEmitter {

    private static final Logger LOGGER = LoggerFactory.getLogger(VenueResponseEmitter.class);

    private final Tracing otlTracing;
    private final MeterRegistry meterRegistry;
    private final RabbitExchange<Message> tradingExchange;

    public VenueResponseEmitter(RabbitIntegrator rabbitIntegrator, Telemetry telemetry) {
        this.otlTracing = telemetry.getTracing();
        this.meterRegistry = telemetry.getMeterRegistry();
        this.tradingExchange = OemsExchange.Trading.declareIngressExchange(rabbitIntegrator);
    }

    public void emitExecReport(VenueResponse executionReport) {
        Map<String, String> baggage = Map.of(
            TracingConv.EXEC_TYPE, executionReport.getExecType().toString()
        );
        try (var ignored = otlTracing.createBaggage(baggage)) {
            try (var ignored2 = otlTracing.createSpan("venueexecutionreport.emit", SpanKind.PRODUCER)) {
                recordLatencyIn(latencyTimer(executionReport)).of(() -> emitExecReportInner(executionReport));
            }
        }
    }

    private void emitExecReportInner(VenueResponse executionReport) {
        String venueAccount = executionReport.getVenueAccount();
        Map<String, String> routingHeaders = Map.of(
            OemsHeader.MESSAGE_TYPE.getHeaderName(), VenueResponse.class.getSimpleName(),
            OemsHeader.VENUE_ACCOUNT.getHeaderName(), venueAccount
        );
        Map<String, String> headers = otlTracing.saveContext(RabbitHeadersPropagator.create(routingHeaders), RabbitHeadersPropagator.setter())
            .getHeaders();
        LOGGER.info("Emitting {} to {}, headers: {}\n{}", executionReport.getClass().getSimpleName(), tradingExchange.getName(), headers, executionReport);

        updateMetrics(executionReport);

        tradingExchange.publishWithHeaders(executionReport, headers);
    }

    public void emitCancelReject(VenueResponse cancelReject) {
        try (var ignored = otlTracing.createSpan("venuecancelreject.emit", SpanKind.PRODUCER)) {
            recordLatencyIn(latencyTimer(cancelReject)).of(() -> emitCancelRejectInner(cancelReject));
        }
    }

    private void emitCancelRejectInner(VenueResponse cancelReject) {
        String venueAccount = cancelReject.getVenueAccount();
        Map<String, String> routingHeaders = Map.of(
            OemsHeader.MESSAGE_TYPE.getHeaderName(), VenueResponse.class.getSimpleName(),
            OemsHeader.VENUE_ACCOUNT.getHeaderName(), venueAccount
        );
        Map<String, String> headers = otlTracing.saveContext(RabbitHeadersPropagator.create(routingHeaders), RabbitHeadersPropagator.setter())
            .getHeaders();
        LOGGER.info("Emitting {} to {}, headers: {}\n{}", cancelReject.getClass().getSimpleName(), tradingExchange.getName(), headers, cancelReject);

        updateMetrics(cancelReject);

        tradingExchange.publishWithHeaders(cancelReject, headers);
    }

    private void updateMetrics(VenueResponse venueResponse) {
        try {
            String instrumentId = venueResponse.getVenueTicker();
            String messageType = venueResponse.getResponseType().name();
            String execType = venueResponse.getExecType().name();
            this.meterRegistry.counter("wyden.trading.response.outgoing.count",
                "instrumentId", instrumentId,
                "messageType", messageType,
                "orderType", "",
                "execType", execType).increment();
        } catch (Exception ex) {
            LOGGER.warn("Metrics update failed", ex);
        }
    }

    private Timer latencyTimer(VenueResponse response) {
        try {
            return tradingResponseOutgoingLatencyTimer(meterRegistry, response.getResponseType());
        } catch (Exception e) {
            LOGGER.warn("Unable to create latency timer", e);
            return EmptyTimer.create();
        }
    }
}
