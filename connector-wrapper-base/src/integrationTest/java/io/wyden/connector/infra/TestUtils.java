package io.wyden.connector.infra;

import ch.algotrader.api.domain.exchange.ExchangeDTO;
import ch.algotrader.api.domain.exchange.ExchangeIdentity;
import ch.algotrader.api.domain.security.ForexDTO;
import ch.algotrader.api.domain.security.SecurityDTO;
import ch.algotrader.api.domain.security.SecurityIdentity;

public final class TestUtils {

    private TestUtils() {
        // Empty
    }

    public static ExchangeDTO defaultExchangeDto(String exchangeName) {
        return ExchangeDTO.builder()
            .setName(exchangeName)
            .setExchangeIdentity(ExchangeIdentity.builder()
                .with(ExchangeIdentity.CODE, exchangeName)
                .with(ExchangeIdentity.TRADING_VIEW_ID, exchangeName)
                .create())
            .create();
    }

    public static SecurityDTO defaultSecurityDto(String adapterTicker) {
        SecurityIdentity securityIdentity = SecurityIdentity.builder()
            .with("adapterTicker", adapterTicker)
            .create();
        return ForexDTO.builder()
            .setSecurityIdentity(securityIdentity)
            .create();
    }

}
