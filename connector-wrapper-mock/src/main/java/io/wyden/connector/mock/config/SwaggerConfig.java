package io.wyden.connector.mock.config;

import io.swagger.v3.oas.annotations.OpenAPIDefinition;
import io.swagger.v3.oas.models.OpenAPI;
import io.swagger.v3.oas.models.info.Info;
import io.swagger.v3.oas.models.servers.Server;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.List;

import static org.apache.commons.lang3.StringUtils.isNotBlank;

@Configuration
@OpenAPIDefinition
public class SwaggerConfig {

    @Value("${openapi.server.url:}")
    String serverUrl;

    @Bean
    public OpenAPI customOpenAPI() {
        return new OpenAPI()
            .info(new Info())
            .servers(getServers());
    }

    private List<Server> getServers() {
        if (isNotBlank(serverUrl)) {
            Server server = new Server();
            server.setUrl(serverUrl);
            return List.of(server);
        } else {
            return null;
        }
    }
}
