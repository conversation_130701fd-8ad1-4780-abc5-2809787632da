package io.wyden.pricing.service.subscription;

import com.google.common.collect.Sets;
import io.wyden.cloudutils.rabbitmq.RabbitExchange;
import io.wyden.cloudutils.rabbitmq.destination.OemsHeader;
import io.wyden.cloudutils.rabbitmq.queue.MatchingCondition;
import io.wyden.cloudutils.rabbitmq.queue.RabbitQueue;
import io.wyden.cloudutils.tools.DateUtils;
import io.wyden.pricing.service.MarketDataEventType;
import io.wyden.pricing.interfaces.rabbit.consumer.MarketDataEventConsumer;
import io.wyden.pricing.interfaces.rabbit.emitter.MarketDataRequestEmitter;
import io.wyden.published.common.Metadata;
import io.wyden.published.marketdata.InstrumentKey;
import io.wyden.published.marketdata.MarketDataEvent;
import io.wyden.published.marketdata.MarketDataRequest;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;

import java.time.ZonedDateTime;
import java.util.HashSet;
import java.util.Map;
import java.util.Set;
import java.util.UUID;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicReference;
import java.util.stream.Collectors;

/**
 * Makes sure that source market data events are continuously subscribed by re-emitting source market data request refresh.
 * Updates bindings required in {@link MarketDataEventConsumer} based on updates in source subscription list
 */
@Service
public class SourceSubscriptionHolder {

    private static final Logger LOGGER = LoggerFactory.getLogger(SourceSubscriptionHolder.class);
    public static final String PRICING_ENGINE = "pricing-engine";

    private final MarketDataRequestEmitter marketDataRequestEmitter;
    private final RabbitExchange<MarketDataEvent> allMarketDataExchange;
    private final RabbitQueue<MarketDataEvent> marketDataEventRabbitQueue;
    private final boolean isDynamicBindingEnabled;

    private final Map<String, StreamSources> sourcesByStreamId = new ConcurrentHashMap<>();
    private final AtomicReference<Set<StreamSource>> marketDataBindingsInPlace = new AtomicReference<>(new HashSet<>());

    public SourceSubscriptionHolder(MarketDataRequestEmitter marketDataRequestEmitter,
                                    RabbitExchange<MarketDataEvent> marketDataEventExchange,
                                    RabbitQueue<MarketDataEvent> marketDataEventRabbitQueue,
                                    @Value("${rabbitmq.dynamicbinding}") boolean isDynamicBindingEnabled) {
        this.marketDataRequestEmitter = marketDataRequestEmitter;
        this.allMarketDataExchange = marketDataEventExchange;
        this.marketDataEventRabbitQueue = marketDataEventRabbitQueue;
        this.isDynamicBindingEnabled = isDynamicBindingEnabled;
    }

    @Scheduled(fixedRateString = "${sourceMdRefresh.rate.in.seconds}", timeUnit = TimeUnit.SECONDS)
    void refreshSourceSubscriptions() {
        getAllUnderlyingSources()
            .forEach(source -> {
                MarketDataRequest marketDataRequest = sourceAsRequest(source);
                marketDataRequestEmitter.emit(marketDataRequest);
            });
    }

    public void onNewClientStreamCreated(String streamId, StreamSources streamSources) {
        sourcesByStreamId.put(streamId, streamSources);

        if (isDynamicBindingEnabled) {
            refreshBindingList(getAllUnderlyingSources());
        }

        streamSources.sources()
            .forEach(source -> {
                MarketDataRequest mdr = sourceAsRequest(source);
                marketDataRequestEmitter.emit(mdr);
            });
    }

    private static MarketDataRequest sourceAsRequest(StreamSource source) {
        return MarketDataRequest.newBuilder()
            .setInstrumentKey(source.instrumentKey())
            .setMarketDepth(source.depth())
            .setMetadata(pricingEngineRequestMetadata())
            .build();
    }

    private static Metadata pricingEngineRequestMetadata() {
        return Metadata.newBuilder()
            .setRequesterId(PRICING_ENGINE)
            .setRequestId(UUID.randomUUID().toString())
            .setCreatedAt(DateUtils.toIsoUtcTime(ZonedDateTime.now()))
            .build();
    }

    public void onClientStreamDisposed(String streamId) {
        sourcesByStreamId.remove(streamId);

        if (isDynamicBindingEnabled) {
            refreshBindingList(getAllUnderlyingSources());
        }
    }

    private Set<StreamSource> getAllUnderlyingSources() {
        return sourcesByStreamId.values().stream()
            .flatMap(ss -> ss.sources().stream())
            .collect(Collectors.toSet());
    }

    private void refreshBindingList(Set<StreamSource> required) {
        Set<StreamSource> current = new HashSet<>(marketDataBindingsInPlace.get());

        Sets.SetView<StreamSource> notRequired = Sets.difference(current, required);
        if (!notRequired.isEmpty()) {
            LOGGER.info("Unbinding from: {} , no longer needed.", notRequired);
            notRequired.forEach(this::unbind);
        }

        Sets.SetView<StreamSource> newRequired = Sets.difference(required, current);
        if (!newRequired.isEmpty()) {
            LOGGER.info("Adding new bindings to: {}", newRequired);
            newRequired.forEach(this::bind);
        }

        marketDataBindingsInPlace.set(required);
    }

    private void bind(StreamSource source) {
        marketDataEventRabbitQueue.bindWithHeaders(allMarketDataExchange, MatchingCondition.ALL, getHeaders(source.instrumentKey(), source.depth()));
    }

    private void unbind(StreamSource source) {
        marketDataEventRabbitQueue.unbindWithHeaders(allMarketDataExchange, MatchingCondition.ALL, getHeaders(source.instrumentKey(), source.depth()));
    }

    private Map<String, Object> getHeaders(InstrumentKey instrumentKey, int depth) {
        return Map.of(
            OemsHeader.INSTRUMENT_ID.getHeaderName(), instrumentKey.getInstrumentId(),
            OemsHeader.VENUE_ACCOUNT.getHeaderName(), instrumentKey.getVenueAccount(),
            OemsHeader.MD_LEVEL.getHeaderName(), depth == 1 ? MarketDataEventType.L1.name() : MarketDataEventType.L2.name()
        );
    }

    public StreamSources getSources(String streamId) {
        return sourcesByStreamId.get(streamId);
    }

    public Set<StreamSource> getBindings() {
        return marketDataBindingsInPlace.get();
    }
}
