spring.application.name = pricing-service
spring.profiles.active = prod

server.port = 8051

# Hazelcast setup:

# comma-separated list of hz member hosts
hz.addressList = localhost
hz.outboundPortDefinition=

rabbitmq.username = pricing-service
rabbitmq.password = password
rabbitmq.virtualHost = /
rabbitmq.host = localhost
# default RabbitMQ port for non-TLS connections: 5672, default port for TLS connections: 5671
rabbitmq.port = 5672
# specify a valid protocol name, e.g. "TLSv1.2" . leave empty for non-TLS connection
rabbitmq.tls =

tracing.collector.endpoint=http://localhost:4317
management.endpoints.web.exposure.include=health,prometheus,metrics,loggers
management.endpoint.health.show-details=always
management.endpoint.health.probes.enabled=true
management.endpoint.loggers.enabled=true
management.health.livenessState.enabled=true
management.health.readinessState.enabled=true
management.endpoint.health.group.liveness.include=livenessState,diskSpace,rabbit,requestQueueConsumer,eventQueueConsumer
management.endpoint.health.group.readiness.include=readinessState
management.metrics.tags.wyden_service=pricing-service

# initial disposal timout only marks the client request for disposal, second timeout in a row will dispose actually
clientMdDisposal.rate.in.seconds=15
sourceMdRefresh.rate.in.seconds=15

inbound.request.queue.name=pricing-service-queue.market-data.REQUEST
inbound.marketdata.queue.name=pricing-service-queue.market-data.MARKET-DATA-EVENT

# Toggles RabbitMQ dynamic binding on a marekt data inbound queue (per requested instrumentId + account + depth).
# Application will use client side filtering if turned off.
rabbitmq.dynamicbinding = false
