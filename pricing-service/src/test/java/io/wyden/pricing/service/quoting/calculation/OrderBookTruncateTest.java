package io.wyden.pricing.service.quoting.calculation;

import io.wyden.published.marketdata.MarketDataEvent;
import io.wyden.published.marketdata.OrderBook;
import io.wyden.published.marketdata.OrderBookLevel;
import org.junit.jupiter.api.Test;

import java.math.BigDecimal;
import java.util.HashMap;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;

class OrderBookTruncateTest {

    @Test
    public void testTruncateDepth() {
        // Create initial order book levels
        Map<String, OrderBookLevel> asksMap = new HashMap<>();
        asksMap.put("100.0", OrderBookLevel.newBuilder().setPrice("100.0").setAmount("50").build());
        asksMap.put("101.0", OrderBookLevel.newBuilder().setPrice("101.0").setAmount("30").build());
        asksMap.put("102.0", OrderBookLevel.newBuilder().setPrice("102.0").setAmount("20").build());
        asksMap.put("103.0", OrderBookLevel.newBuilder().setPrice("103.0").setAmount("10").build());

        Map<String, OrderBookLevel> bidsMap = new HashMap<>();
        bidsMap.put("99.0", OrderBookLevel.newBuilder().setPrice("99.0").setAmount("50").build());
        bidsMap.put("98.0", OrderBookLevel.newBuilder().setPrice("98.0").setAmount("30").build());
        bidsMap.put("97.0", OrderBookLevel.newBuilder().setPrice("97.0").setAmount("20").build());
        bidsMap.put("96.0", OrderBookLevel.newBuilder().setPrice("96.0").setAmount("10").build());

        // Create initial OrderBook and MarketDataEvent
        OrderBook orderBook = OrderBook.newBuilder().putAllAsks(asksMap).putAllBids(bidsMap).build();
        MarketDataEvent.Builder event = MarketDataEvent.newBuilder().setOrderBook(orderBook);

        // Truncate to 50%
        BigDecimal maximumDepth = new BigDecimal("0.5");
        MarketDataEvent.Builder truncatedEvent = OrderBookTruncate.truncateDepth(event, maximumDepth);

        // Verify the truncated asks
        Map<String, OrderBookLevel> truncatedAsks = truncatedEvent.getOrderBook().getAsksMap();
        assertEquals(2, truncatedAsks.size());
        assertTrue(truncatedAsks.containsKey("100.0"));
        assertTrue(truncatedAsks.containsKey("101.0"));

        // Verify the truncated bids
        Map<String, OrderBookLevel> truncatedBids = truncatedEvent.getOrderBook().getBidsMap();
        assertEquals(2, truncatedBids.size());
        assertTrue(truncatedBids.containsKey("99.0"));
        assertTrue(truncatedBids.containsKey("98.0"));
    }

    @Test
    public void testTruncateDepth_EmptyOrderBook() {
        // Create an empty OrderBook and MarketDataEvent
        OrderBook orderBook = OrderBook.newBuilder().build();
        MarketDataEvent.Builder event = MarketDataEvent.newBuilder().setOrderBook(orderBook);

        // Truncate to any depth (should remain empty)
        BigDecimal maximumDepth = new BigDecimal("0.5");
        MarketDataEvent.Builder truncatedEvent = OrderBookTruncate.truncateDepth(event, maximumDepth);

        // Verify the truncated asks
        Map<String, OrderBookLevel> truncatedAsks = truncatedEvent.getOrderBook().getAsksMap();
        assertEquals(0, truncatedAsks.size());

        // Verify the truncated bids
        Map<String, OrderBookLevel> truncatedBids = truncatedEvent.getOrderBook().getBidsMap();
        assertEquals(0, truncatedBids.size());
    }

}