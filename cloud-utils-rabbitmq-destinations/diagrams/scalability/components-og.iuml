''' OG:

    component "Order Gateway" as og_consumer {
        portin "task consumer" as Consumer_OG
    }

    queue ClientOrderStateMachineQueue#line:red [
        ===Client Order State Machine Queue
        ===
        Single Active Consumer
        ===
        ====Header bindings:
        1) type=Client.REQUEST && orderId=$orderId_A
        2) type=Oems.RESPONSE && orderId=$orderId_B
        ---
        ====SAC queue:
        * 1 queue per orderId
        * multiple consumers allowed, but only one can be active
        * two bindings - has to receive all messages that concern the Order
        (client requests and oems responses that affect the parent)
    ]

    component "Order Gateway" as og_registrar {
        portin "registration consumer" as Registrar_OG
        card registration_logic_og [
            - declare Qualified queue
            - refresh / become a consumer (SAC)
        ]
    }

    component "Order Gateway" as og_resender {
        portin "discover consumer" as Discover_OG
        card resend_logic_og [
            - declare Qualified queue
            - send msg to that queue
            - bind that queue with Trading State machines exchange
            - publish Open and Close messages to Announce Broadcast Exchange
            ]
        portout "announcer" as Announcer_OG
        portout "resender" as Resender_OG
    }

legend
    |= Color |= Description |
    | <back:#Red>Red</back>         | State machines. Only single worker should actively consume from them. Sequence is important. Messages should be partitioned per qualifier and end up in qualified queue. |
    | <back:#Yellow>Yellow</back>   | Registration.  Receives copies of all messages entering Trading space. State machine registration consumers observe those and declare qualified queues based on new qualifiers observed.|
    | <back:#Blue>Blue</back>       | Exclusive Observers. Non-destructive consumption of message copies. Non-state machines can create individual observation queues to observe events happening in the system. We can have multiple queues like this, consuming same messages. |
    | <back:#Purple>Purple</back>   | Round-robin observers. Single queue with tasks to be handled by multiple services in round-robin fashion. |
endlegend
