package io.wyden.oems.ordergateway.service.state;

import io.wyden.cloudutils.tools.DateUtils;
import io.wyden.oems.ordergateway.model.ClientOrderState;
import io.wyden.published.client.ClientOrderStatus;
import io.wyden.published.client.ClientRequest;
import io.wyden.published.referencedata.Instrument;

import java.time.ZonedDateTime;
import javax.annotation.Nullable;

class Rejected extends Terminal {

    public static ClientOrderState createPersistentState(ClientRequest clientRequest, @Nullable Instrument instrument) {
        String createdAt = clientRequest.getCreatedAt();
        ClientOrderState.Builder builder = ClientOrderState.newBuilder()
            .setOrder(clientRequest)
            .setCurrentStatus(ClientOrderStatus.REJECTED)
            .setClosed(true)
            .setQuantity(clientRequest.getQuantity())
            .setRemainingQuantity(clientRequest.getQuantity())
            .setFilledQuantity("0")
            .setCreatedAt(createdAt)
            .setSystemTimestamp(DateUtils.toIsoUtcTime(ZonedDateTime.now()));

        if (instrument != null) {
            builder.setInstrumentId(instrument.getInstrumentIdentifiers().getInstrumentId());
        } else {
            builder.setDescription("Rejected - requested instrumentId is absent " + clientRequest.getInstrumentId());

        }
        return builder.build();
    }
}
