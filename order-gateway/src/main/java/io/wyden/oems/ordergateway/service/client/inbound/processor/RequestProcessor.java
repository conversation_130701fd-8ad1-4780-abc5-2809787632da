package io.wyden.oems.ordergateway.service.client.inbound.processor;

import io.wyden.cloudutils.rabbitmq.ConsumptionResult;
import io.wyden.oems.ordergateway.model.ClientOrderState;
import io.wyden.oems.ordergateway.service.tracking.ClientOrderCache;
import io.wyden.oems.ordergateway.service.tracking.FailureNeedsRetryException;
import io.wyden.oems.ordergateway.service.tracking.OrderService;
import io.wyden.published.client.ClientRequest;
import io.wyden.published.client.ClientRequestType;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.List;
import java.util.Optional;

import static io.wyden.oems.ordergateway.service.client.inbound.ClientRequestUtils.isStreetSideSOROrder;

public abstract class RequestProcessor {

    private static final Logger LOGGER = LoggerFactory.getLogger(RequestProcessor.class);

    protected final OrderService orderService;

    private final ClientRequestType requestType;
    private final ClientOrderCache clientOrderCache;

    protected RequestProcessor(OrderService orderService,
                               ClientRequestType requestType,
                               ClientOrderCache clientOrderCache) {
        this.orderService = orderService;
        this.requestType = requestType;
        this.clientOrderCache = clientOrderCache;
    }

    protected abstract boolean hasPermissions(ClientRequest request);

    protected abstract void noAccessHandler(ClientRequest request);

    protected abstract boolean isValid(ClientRequest request);

    protected abstract void isNotValidHandler(ClientRequest request);

    protected abstract void process(ClientRequest request);

    public ConsumptionResult handle(ClientRequest request) {
        if (!hasPermissions(request)) {
            LOGGER.info("Client has no permissions to perform following request: {}", request);
            noAccessHandler(request);
            return ConsumptionResult.consumed();
        }
        if (!isValid(request)) {
            LOGGER.info("Client request is invalid: {}", request);
            isNotValidHandler(request);
            return ConsumptionResult.consumed();
        }

        process(request);
        return ConsumptionResult.consumed();
    }

    public ClientRequestType getRequestType() {
        return requestType;
    }

    public List<String> getVenueAccounts(String orderId) {
        List<String> venueAccounts;
        try {
            Optional<ClientOrderState> clientOrderState = clientOrderCache.find(orderId);
            venueAccounts = clientOrderState
                .map(this::getVenueAccounts)
                .orElseThrow(() -> new RuntimeException("Order not found for orderId: %s".formatted(orderId)));
        } catch (Exception e) {
            throw new FailureNeedsRetryException(e);
        }
        return venueAccounts;
    }

    public List<String> getVenueAccounts(ClientOrderState clientOrderState) {
        ClientRequest order = clientOrderState.getOrder();
        if (isStreetSideSOROrder(order) || order.getVenueAccountsList().size() == 1) {
            return order.getVenueAccountsList();
        } else {
            // TODO - fallback to legacy field, remove in AC-5028
            return StringUtils.isBlank(order.getTarget()) ? List.of() : List.of(order.getTarget());
        }
    }

    public List<String> getVenueAccounts(ClientRequest order) {
        if (isStreetSideSOROrder(order) || order.getVenueAccountsList().size() == 1) {
            return order.getVenueAccountsList();
        } else {
            // TODO - fallback to legacy field, remove in AC-5028
            return StringUtils.isBlank(order.getTarget()) ? List.of() : List.of(order.getTarget());
        }
    }

    public ClientOrderState getOrderState(String orderId) {
        ClientOrderState orderState;
        try {
            orderState = clientOrderCache.find(orderId)
                .orElseThrow(() -> new RuntimeException("Exception during fetching orderState for orderId: %s".formatted(orderId)));
        } catch (Exception e) {
            throw new FailureNeedsRetryException(e);
        }
        return orderState;
    }
}
