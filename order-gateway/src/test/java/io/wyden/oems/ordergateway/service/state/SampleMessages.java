package io.wyden.oems.ordergateway.service.state;

import io.wyden.published.oems.OemsExecType;
import io.wyden.published.oems.OemsOrderStatus;
import io.wyden.published.oems.OemsResponse;
import org.jetbrains.annotations.NotNull;

public class SampleMessages {

    @NotNull
    static OemsResponse getPartialFillResponse() {
        return OemsResponse.newBuilder()
            .setResponseType(OemsResponse.OemsResponseType.EXECUTION_REPORT)
            .setOrderStatus(OemsOrderStatus.STATUS_PARTIALLY_FILLED)
            .setExecType(OemsExecType.PARTIAL_FILL)
            .setOrderQty("10")
            .setLeavesQty("9")
            .setCumQty("1")
            .setAvgPrice("10000")
            .setLastPrice("10000")
            .setLastQty("1")
            .build();
    }

    static OemsResponse getFullFillResponse() {
        OemsResponse oemsResponse = OemsResponse.newBuilder()
            .setResponseType(OemsResponse.OemsResponseType.EXECUTION_REPORT)
            .setOrderStatus(OemsOrderStatus.STATUS_FILLED)
            .setExecType(OemsExecType.FILL)
            .setOrderQty("10")
            .setLeavesQty("0")
            .setCumQty("10")
            .setAvgPrice("10000")
            .setLastPrice("10000")
            .setLastQty("10")
            .build();
        return oemsResponse;
    }

    static OemsResponse getFullFillResponseAfterCancelReject() {
        OemsResponse oemsResponse = OemsResponse.newBuilder()
            .setResponseType(OemsResponse.OemsResponseType.CANCEL_REJECT)
            .setOrderStatus(OemsOrderStatus.STATUS_FILLED)
            .setExecType(OemsExecType.FILL)
            .setOrderQty("10")
            .setLeavesQty("0")
            .setCumQty("10")
            .setAvgPrice("10000")
            .setLastPrice("10000")
            .setLastQty("10")
            .build();
        return oemsResponse;
    }

    static OemsResponse getRejectedResponse() {
        OemsResponse oemsResponse = OemsResponse.newBuilder()
            .setResponseType(OemsResponse.OemsResponseType.EXECUTION_REPORT)
            .setOrderStatus(OemsOrderStatus.STATUS_REJECTED)
            .setExecType(OemsExecType.REJECTED)
            .setLastPrice("0")
            .setLastQty("0")
            .build();
        return oemsResponse;
    }
    static OemsResponse getCancelledResponse() {
        OemsResponse oemsResponse = OemsResponse.newBuilder()
            .setResponseType(OemsResponse.OemsResponseType.EXECUTION_REPORT)
            .setOrderStatus(OemsOrderStatus.STATUS_CANCELED)
            .setExecType(OemsExecType.CANCELED)
            .setLastPrice("0")
            .setLastQty("0")
            .build();
        return oemsResponse;
    }
}
