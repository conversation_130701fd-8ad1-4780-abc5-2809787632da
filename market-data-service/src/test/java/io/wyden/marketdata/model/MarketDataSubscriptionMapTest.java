package io.wyden.marketdata.model;

import io.micrometer.core.instrument.MeterRegistry;
import io.wyden.published.marketdata.InstrumentKey;
import io.wyden.published.marketdata.MarketDataRequest;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.Map;
import java.util.Set;
import java.util.UUID;

import static org.assertj.core.api.Assertions.assertThat;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.Mockito.mock;

@ExtendWith(MockitoExtension.class)
public class MarketDataSubscriptionMapTest {

    MarketDataSubscriptionMap marketDataSubscriptionMap;

    @BeforeEach
    void setup() {
        marketDataSubscriptionMap = Mockito.spy(new MarketDataSubscriptionMap(mock(MeterRegistry.class)));
    }

    @Test
    public void shouldAddNewSubscriptionUsingPut() {
        // given
        InstrumentKey testInstrumentKey = InstrumentKey.newBuilder().setInstrumentId("testInstrumentId").setVenueAccount("testVenueAccount").build();
        MarketDataSubscriptionKey testMarketDataSubscriptionKey = new MarketDataSubscriptionKey(testInstrumentKey, 1);
        MarketDataRequest marketDataRequest = MarketDataRequest.newBuilder().setInstrumentKey(testInstrumentKey).build();
        MarketDataSubscription testMarketDataSubscription = new MarketDataSubscription(UUID.randomUUID().toString(), Set.of(marketDataRequest));
        // when
        marketDataSubscriptionMap.put(testMarketDataSubscriptionKey, testMarketDataSubscription);
        // then
        assertThat(marketDataSubscriptionMap.getSubscriptionMap().size()).isEqualTo(1);
        assertThat(marketDataSubscriptionMap.getSubscriptionMap().get(testMarketDataSubscriptionKey)).isEqualTo(testMarketDataSubscription);

        assertThat(marketDataSubscriptionMap.getSubscriptionsMapById().size()).isEqualTo(1);
        assertThat(marketDataSubscriptionMap.getSubscriptionsMapById().get(testMarketDataSubscription.subscriptionId())).isEqualTo(testMarketDataSubscription);
    }

    @Test
    public void shouldAddNewSubscriptionUsingComputeIfAbsentIfNotExist() {
        // given
        InstrumentKey testInstrumentKey1 = InstrumentKey.newBuilder().setInstrumentId("testInstrumentId1").setVenueAccount("testVenueAccount1").build();
        MarketDataSubscriptionKey testMarketDataSubscriptionKey1 = new MarketDataSubscriptionKey(testInstrumentKey1, 1);
        MarketDataRequest marketDataRequest1 = MarketDataRequest.newBuilder().setInstrumentKey(testInstrumentKey1).build();
        MarketDataSubscription testMarketDataSubscription1 = new MarketDataSubscription(UUID.randomUUID().toString(), Set.of(marketDataRequest1));

        InstrumentKey testInstrumentKey2 = InstrumentKey.newBuilder().setInstrumentId("testInstrumentId2").setVenueAccount("testVenueAccount2").build();
        MarketDataSubscriptionKey testMarketDataSubscriptionKey2 = new MarketDataSubscriptionKey(testInstrumentKey2, 1);
        MarketDataRequest marketDataRequest2 = MarketDataRequest.newBuilder().setInstrumentKey(testInstrumentKey2).build();
        MarketDataSubscription testMarketDataSubscription2 = new MarketDataSubscription(UUID.randomUUID().toString(), Set.of(marketDataRequest2));
        // when
        marketDataSubscriptionMap.put(testMarketDataSubscriptionKey1, testMarketDataSubscription1);
        // then
        assertThat(marketDataSubscriptionMap.getSubscriptionMap().size()).isEqualTo(1);
        assertThat(marketDataSubscriptionMap.getSubscriptionMap().get(testMarketDataSubscriptionKey1)).isEqualTo(testMarketDataSubscription1);
        assertThat(marketDataSubscriptionMap.getSubscriptionsMapById().size()).isEqualTo(1);
        assertThat(marketDataSubscriptionMap.getSubscriptionsMapById().get(testMarketDataSubscription1.subscriptionId())).isEqualTo(testMarketDataSubscription1);
        // when
        MarketDataSubscription computeIfAbsentSubscription = marketDataSubscriptionMap.computeIfAbsent(testMarketDataSubscriptionKey2, key -> testMarketDataSubscription2);
        // then
        assertThat(computeIfAbsentSubscription).isEqualTo(testMarketDataSubscription2);
        assertThat(marketDataSubscriptionMap.getSubscriptionMap().size()).isEqualTo(2);
        assertThat(marketDataSubscriptionMap.getSubscriptionMap().get(testMarketDataSubscriptionKey2)).isEqualTo(computeIfAbsentSubscription);
        assertThat(marketDataSubscriptionMap.getSubscriptionsMapById().size()).isEqualTo(2);
        assertThat(marketDataSubscriptionMap.getSubscriptionsMapById().get(testMarketDataSubscription2.subscriptionId())).isEqualTo(computeIfAbsentSubscription);
    }

    @Test
    public void shouldReturnExistingSubscriptionUsingComputeIfAbsentIfAlreadyExist() {
        // given
        InstrumentKey testInstrumentKey1 = InstrumentKey.newBuilder().setInstrumentId("testInstrumentId1").setVenueAccount("testVenueAccount1").build();
        MarketDataSubscriptionKey testMarketDataSubscriptionKey1 = new MarketDataSubscriptionKey(testInstrumentKey1, 1);
        MarketDataRequest marketDataRequest1 = MarketDataRequest.newBuilder().setInstrumentKey(testInstrumentKey1).build();
        MarketDataSubscription testMarketDataSubscription1 = new MarketDataSubscription(UUID.randomUUID().toString(), Set.of(marketDataRequest1));

        InstrumentKey testInstrumentKey2 = InstrumentKey.newBuilder().setInstrumentId("testInstrumentId2").setVenueAccount("testVenueAccount2").build();
        MarketDataSubscriptionKey testMarketDataSubscriptionKey2 = new MarketDataSubscriptionKey(testInstrumentKey2, 1);
        MarketDataRequest marketDataRequest2 = MarketDataRequest.newBuilder().setInstrumentKey(testInstrumentKey2).build();
        MarketDataSubscription testMarketDataSubscription2 = new MarketDataSubscription(UUID.randomUUID().toString(), Set.of(marketDataRequest2));
        // when
        marketDataSubscriptionMap.put(testMarketDataSubscriptionKey1, testMarketDataSubscription1);
        // then
        assertThat(marketDataSubscriptionMap.getSubscriptionMap().size()).isEqualTo(1);
        assertThat(marketDataSubscriptionMap.getSubscriptionMap().get(testMarketDataSubscriptionKey1)).isEqualTo(testMarketDataSubscription1);
        assertThat(marketDataSubscriptionMap.getSubscriptionsMapById().size()).isEqualTo(1);
        assertThat(marketDataSubscriptionMap.getSubscriptionsMapById().get(testMarketDataSubscription1.subscriptionId())).isEqualTo(testMarketDataSubscription1);
        // when
        MarketDataSubscription computeIfAbsentSubscription = marketDataSubscriptionMap.computeIfAbsent(testMarketDataSubscriptionKey1, key -> testMarketDataSubscription2);
        // then
        assertThat(computeIfAbsentSubscription).isEqualTo(testMarketDataSubscription1);
        assertThat(marketDataSubscriptionMap.getSubscriptionMap().size()).isEqualTo(1);
        assertThat(marketDataSubscriptionMap.getSubscriptionMap().get(testMarketDataSubscriptionKey1)).isEqualTo(computeIfAbsentSubscription);
        assertThat(marketDataSubscriptionMap.getSubscriptionMap().get(testMarketDataSubscriptionKey2)).isNull();
        assertThat(marketDataSubscriptionMap.getSubscriptionsMapById().size()).isEqualTo(1);
        assertThat(marketDataSubscriptionMap.getSubscriptionsMapById().get(testMarketDataSubscription1.subscriptionId())).isEqualTo(computeIfAbsentSubscription);
        assertThat(marketDataSubscriptionMap.getSubscriptionsMapById().get(testMarketDataSubscription2.subscriptionId())).isNull();
    }

    @Test
    public void shouldRemoveSubscriptionForGivenSubscriptionKey() {
        // given
        InstrumentKey testInstrumentKey1 = InstrumentKey.newBuilder().setInstrumentId("testInstrumentId1").setVenueAccount("testVenueAccount1").build();
        MarketDataSubscriptionKey testMarketDataSubscriptionKey1 = new MarketDataSubscriptionKey(testInstrumentKey1, 1);
        MarketDataRequest marketDataRequest1 = MarketDataRequest.newBuilder().setInstrumentKey(testInstrumentKey1).build();
        MarketDataSubscription testMarketDataSubscription1 = new MarketDataSubscription(UUID.randomUUID().toString(), Set.of(marketDataRequest1));

        InstrumentKey testInstrumentKey2 = InstrumentKey.newBuilder().setInstrumentId("testInstrumentId2").setVenueAccount("testVenueAccount2").build();
        MarketDataSubscriptionKey testMarketDataSubscriptionKey2 = new MarketDataSubscriptionKey(testInstrumentKey2, 1);
        MarketDataRequest marketDataRequest2 = MarketDataRequest.newBuilder().setInstrumentKey(testInstrumentKey2).build();
        MarketDataSubscription testMarketDataSubscription2 = new MarketDataSubscription(UUID.randomUUID().toString(), Set.of(marketDataRequest2));
        // when
        marketDataSubscriptionMap.put(testMarketDataSubscriptionKey1, testMarketDataSubscription1);
        marketDataSubscriptionMap.put(testMarketDataSubscriptionKey2, testMarketDataSubscription2);
        // then
        assertThat(marketDataSubscriptionMap.getSubscriptionMap().size()).isEqualTo(2);
        assertThat(marketDataSubscriptionMap.getSubscriptionsMapById().size()).isEqualTo(2);
        // when
        MarketDataSubscription removed = marketDataSubscriptionMap.remove(testMarketDataSubscriptionKey1);
        // then
        assertThat(marketDataSubscriptionMap.getSubscriptionMap().size()).isEqualTo(1);
        assertThat(marketDataSubscriptionMap.getSubscriptionsMapById().size()).isEqualTo(1);
        assertThat(removed).isEqualTo(testMarketDataSubscription1);
    }

    @Test
    public void shouldReturnUnmodifiableMap() {
        // given
        InstrumentKey testInstrumentKey1 = InstrumentKey.newBuilder().setInstrumentId("testInstrumentId1").setVenueAccount("testVenueAccount1").build();
        MarketDataSubscriptionKey testMarketDataSubscriptionKey1 = new MarketDataSubscriptionKey(testInstrumentKey1, 1);
        MarketDataRequest marketDataRequest1 = MarketDataRequest.newBuilder().setInstrumentKey(testInstrumentKey1).build();
        MarketDataSubscription testMarketDataSubscription1 = new MarketDataSubscription(UUID.randomUUID().toString(), Set.of(marketDataRequest1));

        InstrumentKey testInstrumentKey2 = InstrumentKey.newBuilder().setInstrumentId("testInstrumentId2").setVenueAccount("testVenueAccount2").build();
        MarketDataSubscriptionKey testMarketDataSubscriptionKey2 = new MarketDataSubscriptionKey(testInstrumentKey2, 1);
        MarketDataRequest marketDataRequest2 = MarketDataRequest.newBuilder().setInstrumentKey(testInstrumentKey2).build();
        MarketDataSubscription testMarketDataSubscription2 = new MarketDataSubscription(UUID.randomUUID().toString(), Set.of(marketDataRequest2));
        // when
        marketDataSubscriptionMap.put(testMarketDataSubscriptionKey1, testMarketDataSubscription1);
        Map<MarketDataSubscriptionKey, MarketDataSubscription> subscriptionMap = marketDataSubscriptionMap.getSubscriptionMap();
        // then
        assertThrows(UnsupportedOperationException.class, () -> subscriptionMap.put(testMarketDataSubscriptionKey2, testMarketDataSubscription2));
    }

    @Test
    public void shouldReturnUnmodifiableMapById() {
        // given
        InstrumentKey testInstrumentKey1 = InstrumentKey.newBuilder().setInstrumentId("testInstrumentId1").setVenueAccount("testVenueAccount1").build();
        MarketDataSubscriptionKey testMarketDataSubscriptionKey1 = new MarketDataSubscriptionKey(testInstrumentKey1, 1);
        MarketDataRequest marketDataRequest1 = MarketDataRequest.newBuilder().setInstrumentKey(testInstrumentKey1).build();
        MarketDataSubscription testMarketDataSubscription1 = new MarketDataSubscription(UUID.randomUUID().toString(), Set.of(marketDataRequest1));

        InstrumentKey testInstrumentKey2 = InstrumentKey.newBuilder().setInstrumentId("testInstrumentId2").setVenueAccount("testVenueAccount2").build();
        MarketDataRequest marketDataRequest2 = MarketDataRequest.newBuilder().setInstrumentKey(testInstrumentKey2).build();
        MarketDataSubscription testMarketDataSubscription2 = new MarketDataSubscription(UUID.randomUUID().toString(), Set.of(marketDataRequest2));
        // when
        marketDataSubscriptionMap.put(testMarketDataSubscriptionKey1, testMarketDataSubscription1);
        Map<String, MarketDataSubscription> subscriptionMap = marketDataSubscriptionMap.getSubscriptionsMapById();
        // then
        assertThrows(UnsupportedOperationException.class, () -> subscriptionMap.put(testMarketDataSubscription2.subscriptionId(), testMarketDataSubscription2));
    }
}
