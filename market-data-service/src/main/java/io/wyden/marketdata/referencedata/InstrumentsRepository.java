package io.wyden.marketdata.referencedata;

import io.wyden.published.referencedata.Instrument;
import io.wyden.referencedata.client.InstrumentsCacheFacade;
import org.springframework.stereotype.Repository;

import java.util.Collection;
import java.util.Optional;

@Repository
public class InstrumentsRepository {

    private final InstrumentsCacheFacade instrumentsCacheFacade;

    public InstrumentsRepository(final InstrumentsCacheFacade instrumentsCacheFacade) {
        this.instrumentsCacheFacade = instrumentsCacheFacade;
    }

    public Optional<Instrument> find(String symbol) {
        return instrumentsCacheFacade.find(symbol);
    }

    public Collection<Instrument> findAll() {
        return instrumentsCacheFacade.findAll();
    }
}
