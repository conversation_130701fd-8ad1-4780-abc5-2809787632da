import '../../../../../cypress/support/component';
import { FormPO } from '../../form/__test__/FormPO.cy';

export class ExecutionConfigurationPO {
  private formPO = new FormPO();

  selectPortfolio(value: string) {
    this.formPO.selectByLabelText('Nostro portfolio', value);
  }

  insertPercentageFee(value: string) {
    cy.findByTestId('percentageFee-input').find('input').type(value);
  }

  insertMinimumFee(value: string) {
    this.formPO.insertByLabelText('Minimum fee', value);
  }

  instrumentSearchHasValue(value: string) {
    cy.findByTestId('instrument-search-textField').find('input').should('have.value', value);
  }

  symbolSearchHasValue(value: string) {
    cy.findByTestId('symbol-search-textField').find('input').should('have.value', value);
  }

  insertFixedFee(value: string) {
    this.formPO.insertByLabelText('Fixed fee', value);
  }

  selectFixedFeeCurrency(value: string) {
    this.formPO.selectByTestId('fixed-fee-currency-autocomplete', value, 'Currency');
  }

  selectMinFeeCurrency(value: string) {
    this.formPO.selectByTestId('min-fee-currency-autocomplete', value, 'Currency');
  }

  selectPercentageFeeCurrency(value: string) {
    this.formPO.selectByTestId('percentage-fee-currency-autocomplete', value, 'Currency');
  }

  feeHasValue(feeField: string, value: string) {
    cy.findByTestId(feeField).find('input').should('have.value', value);
  }

  nostroPortfolioHasValue(value: string) {
    cy.findByTestId('portfolio-autocomplete').find('input').should('have.value', value);
  }

  selectTargetAccount(value: string) {
    this.formPO.selectByLabelText('Target account', value);
  }

  switchHasValue(label: string, value: boolean) {
    cy.findByTestId(label)
      .find('input')
      .should('have.value', value ? 'true' : 'false');
  }

  toggleExchangeFee() {
    this.formPO.toggleByLabelText('Exchange fee');
  }

  toggleDiscloseVenue() {
    this.formPO.toggleByLabelText('Disclose trading venue');
  }

  selectFeeCurrencyType(value: 'Specific' | 'Base' | 'Quote') {
    cy.findByLabelText(value).click();
  }

  save() {
    cy.findByTestId('save-execution-configuration').click({ force: true });
  }

  dismissInstrumentConfig() {
    cy.findByTestId('dismiss-button').click();
  }

  saveInstrument() {
    cy.findByTestId('save-instrument-execution-configuration').click({ force: true });
  }

  expectSuccessMessage() {
    return cy.findByText('Saving configuration was successful');
  }
}
