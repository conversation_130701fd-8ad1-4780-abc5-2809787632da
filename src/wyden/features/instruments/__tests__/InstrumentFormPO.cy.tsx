import '../../../../../cypress/support/component';
import { FormPO } from '../../form/__test__/FormPO.cy';

export class InstrumentFormPO {
  private formPO = new FormPO();

  insertMinQty(value: string) {
    this.formPO.insertByLabelText('Min Qty', value);
  }

  insertMinQuoteQty(value: string) {
    this.formPO.insertByLabelText('Min Quote Qty', value);
  }

  insertMaxQty(value: string) {
    this.formPO.insertByLabelText('Max Qty', value);
  }

  insertMaxQuoteQty(value: string) {
    this.formPO.insertByLabelText('Max Quote Qty', value);
  }

  insertQuantityIncrement(value: string) {
    this.formPO.insertByLabelText('Qty increment', value);
  }

  insertQuoteQtyIncrement(value: string) {
    this.formPO.insertByLabelText('Quote Qty increment', value);
  }

  insertPriceIncrement(value: string) {
    this.formPO.insertByLabelText('Price increment', value);
  }

  insertMinNotional(value: string) {
    this.formPO.insertByLabelText('Min Notional', value);
  }

  selectVenueName(value: string) {
    this.formPO.selectByLabelTextWithOptionsLengthCheck('Venue Name', value, 1);
  }

  checkVenueNamesOptionsLength(length: number) {
    this.formPO.checkSelectOptionsLength('Venue Name', length);
  }

  insertDescription(value: string) {
    this.formPO.insertByLabelText('Description', value);
  }
  selectQuoteCurrency(value: string) {
    this.formPO.selectByTestId('quote-currency-autocomplete', value, 'Quote');
  }

  selectBaseCurrency(value: string) {
    this.formPO.selectByTestId('base-currency-autocomplete', value, 'Base');
  }

  insertSymbol(value: string) {
    this.formPO.insertByLabelText('Symbol', value);
  }

  selectAssetClass(value: string) {
    this.formPO.selectByLabelText('Asset Class', value);
  }

  editInstrumentButton() {
    return cy.findByRole('button', { name: 'Edit instrument' });
  }

  dismissModal() {
    cy.findByRole('button', { name: 'Dismiss' }).click();
  }

  clickEditInstrument() {
    cy.findByRole('button', { name: 'Edit instrument' }).click({
      force: true,
    });
  }

  clickCreateInstrument() {
    cy.findByRole('button', { name: 'Create instrument' }).click();
  }

  checkActive() {
    this.formPO.checkByLabelText('Active Instrument');
  }

  checksInverseContract() {
    this.formPO.checkByLabelText('Inverse Contract');
  }
}
