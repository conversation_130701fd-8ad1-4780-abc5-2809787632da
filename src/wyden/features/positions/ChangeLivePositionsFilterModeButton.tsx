import { useTranslation } from 'react-i18next';
import { IconButton, Tooltip } from '@mui/material';
import CheckBoxIcon from '@mui/icons-material/CheckBox';
import CheckBoxOutlineBlankIcon from '@mui/icons-material/CheckBoxOutlineBlank';

import { styled } from '@ui/styled';
import { color } from '@ui/theme/colors';

interface ChangeOrderBookModeButtonProps {
  positionsFilterModeSetting: boolean;
  setPositionsFilterModeSetting: (value: boolean) => void;
}

export function ChangeLivePositionsFilterModeButton({
  positionsFilterModeSetting,
  setPositionsFilterModeSetting,
}: ChangeOrderBookModeButtonProps) {
  const { t } = useTranslation();

  return (
    <StyledTooltip
      title={
        positionsFilterModeSetting
          ? t('positions.filterEmptyPositions')
          : t('positions.showEmptyPositions')
      }
      arrow
    >
      <IconButton onClick={() => setPositionsFilterModeSetting(!positionsFilterModeSetting)}>
        {positionsFilterModeSetting ? <CheckBoxIcon /> : <CheckBoxOutlineBlankIcon />}
      </IconButton>
    </StyledTooltip>
  );
}

const StyledTooltip = styled(Tooltip)`
  color: ${({ theme }) => color[theme.palette.mode].textElementsTextSecondary};
`;
