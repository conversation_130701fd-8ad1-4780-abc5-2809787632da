import '../../../../../cypress/support/component';
import { CurrenciesPage } from '../CurrenciesPage';
import React from 'react';
import { FormPO } from '../../form/__test__/FormPO.cy';
import { SnackbarProvider } from 'notistack';

export class CurrenciesPagePO {
  form = new FormPO();

  render() {
    cy.viewport(1920, 1080);
    return cy.mountWithProviders(
      <SnackbarProvider autoHideDuration={500}>
        <CurrenciesPage />
      </SnackbarProvider>,
    );
  }

  expectTextToBeVisible(text: string) {
    return cy.findByText(text);
  }

  getEditButtonOfRow(rowId: string) {
    return cy.get(`[row-id="${rowId}"]`).find('button');
  }

  submitEditForm() {
    return cy.findByRole('button', { name: 'Edit currency' }).click();
  }

  openCreationModal() {
    return cy.findByRole('button', { name: 'Create new currency' }).click();
  }

  submitNewForm() {
    return cy.findByRole('button', { name: 'Create currency' }).click();
  }

  insertDisplayPrecision(value: string) {
    return this.form.insertByLabelText('Display Precision', value);
  }

  insertPrecision(value: string) {
    return this.form.insertByLabelText('Precision', value);
  }

  insertSymbol(value: string) {
    return this.form.insertByLabelText('Symbol', value);
  }

  selectType(value: string) {
    return this.form.selectByLabelText('Type', value);
  }
}
