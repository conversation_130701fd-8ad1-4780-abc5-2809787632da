import { useTranslation } from 'react-i18next';
import { PortfolioResponse, Scope } from '@wyden/services/graphql/generated/graphql';
import { Link, useNavigate } from 'react-router-dom';
import { ReactComponent as ArrowBackIcon } from '@wyden/assets/arrow-back.svg';
import { ReactComponent as FolderIcon } from '@wyden/assets/folder.svg';
import { ReactComponent as EditIcon } from '@wyden/assets/edit.svg';
import { Button } from '@ui/Button';
import { styled } from '@ui/styled';
import { Header } from '@ui/Typography/Header';
import { getSpacing } from '@wyden/utils/styles';
import { PORTFOLIO_GROUP_TAG_NAME } from '@wyden/constants';
import { color } from '@ui/theme/colors';
import { TagWithColor } from '../focus/TagWithColor';
import { Paragraph } from '@ui/Typography/Paragraph';
import { EditPortfolioDialogContent } from '../portfolio-data/edit-portfolio/EditPortfolioDialogContent';
import { usePortfolioStore } from '../portfolio-data/usePortfolioStore';
import { useEnumFormatters } from '@wyden/hooks/useEnumFormatters';
import { useTagColors } from '../focus/usePortfolioTags';
import { LinearProgress, Tooltip } from '@mui/material';
import { GoBackButton } from '@wyden/components/GoBackButton';

export function PortfolioHeader({ portfolio }: { portfolio: PortfolioResponse }) {
  const { t } = useTranslation();
  const navigate = useNavigate();
  const { setPortfolioToEdit, isDataLoading } = usePortfolioStore();
  const { formatPortfolioType } = useEnumFormatters();
  const { tagKeyToColor } = useTagColors();

  const goBack = () => navigate(-1);
  const group = portfolio?.tags.find((tag) => tag.key === PORTFOLIO_GROUP_TAG_NAME)?.value;
  const displayableTags = portfolio?.tags.filter((tag) => tag.key !== PORTFOLIO_GROUP_TAG_NAME);
  const openEditDialog = () => setPortfolioToEdit(portfolio);
  const canEditPortfolio = portfolio.scopes.includes(Scope.Manage);

  const tooltipTitle = canEditPortfolio ? '' : t('portfolioPage.noPermissionToManage');

  return (
    <StyledPortfolioHeader>
      <GoBackButton variant="ghost" onClick={goBack}>
        <ArrowBackIcon />
        {t('common.back')}
      </GoBackButton>
      <StyledHeaderRow>
        <Header variant="h2">{portfolio.name}</Header>
        <Tooltip title={tooltipTitle}>
          <div>
            <Button disabled={!canEditPortfolio} onClick={openEditDialog} size="lg">
              <StyledEditIcon $disabled={!canEditPortfolio} />
              {t('portfolioPage.editPortfolio')}
            </Button>
          </div>
        </Tooltip>
      </StyledHeaderRow>
      <StyledRow>
        {group && (
          <>
            <FolderIcon />
            <Link to={`/settings/portfolio-groups/${encodeURIComponent(group)}`}>{group}</Link>
            <StyledVerticalDivider />
          </>
        )}
        <span>{formatPortfolioType(portfolio.portfolioType)}</span>
        <StyledVerticalDivider />
        <span>{portfolio.portfolioCurrency}</span>
        <StyledVerticalDivider />
        {displayableTags.map((tag) => (
          <TagWithColor color={tagKeyToColor(tag.key) as string} key={`${tag.key}-${tag.value}`}>
            {tag.value}
          </TagWithColor>
        ))}
      </StyledRow>
      <StyledRow>
        <StyledId variant="small">ID: {portfolio.id}</StyledId>
      </StyledRow>
      <StyledProgress $isPending={isDataLoading} />
      <EditPortfolioDialogContent />
    </StyledPortfolioHeader>
  );
}

const StyledId = styled(Paragraph)`
  color: ${({ theme }) => color[theme.palette.mode].textElementsTextWeak};
`;

const StyledPortfolioHeader = styled('div')`
  display: flex;
  flex-direction: column;
  gap: ${getSpacing(2)};
`;

const StyledEditIcon = styled(EditIcon)<{ $disabled?: boolean }>`
  path {
    fill: ${({ theme, $disabled }) =>
      $disabled
        ? color[theme.palette.mode].textElementsTextWeak
        : color[theme.palette.mode].textElementsTextPrimary};
  }
`;

const StyledRow = styled('div')`
  display: flex;
  gap: ${getSpacing(2)};
  align-items: center;
  color: ${({ theme }) => color[theme.palette.mode].textElementsTextSecondary};
`;

const StyledVerticalDivider = styled('div')`
  height: 100%;
  width: 1px;
  background-color: ${({ theme }) => color[theme.palette.mode].fillsElementsFillInactive};
`;

const StyledHeaderRow = styled('div')`
  display: flex;
  justify-content: space-between;
  align-items: center;
`;

const StyledProgress = styled(LinearProgress)<{ $isPending: boolean }>`
  position: absolute;
  top: 160px;
  z-index: 1;
  display: ${({ $isPending }) => ($isPending ? 'block' : 'none')};
  width: 100%;
`;
