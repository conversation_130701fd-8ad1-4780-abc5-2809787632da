import { ReactNode } from 'react';
import { NoResultsFound } from '@ui/NoResultsFound';

interface ResultsOrNoResultsContainerProps {
  recordsCount: number;
  children: ReactNode;
  emptyTextType?: 'noResults' | 'prompt';
}

export const ResultsOrNoResultsContainer = (props: ResultsOrNoResultsContainerProps) => {
  return (
    <>
      {props.recordsCount === 0 ? (
        <NoResultsFound emptyTextType={props.emptyTextType} />
      ) : (
        <>{props.children}</>
      )}
    </>
  );
};
