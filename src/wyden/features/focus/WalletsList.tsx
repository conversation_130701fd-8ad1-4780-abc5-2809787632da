import { useFocusOptions } from '@wyden/features/focus/useFocusOptions';
import {
  PortfolioOption,
  TagOption,
  VenueAccountsOption,
  WalletOption,
} from '@wyden/features/focus/focus.types';
import {
  FocusLabel,
  FocusList,
  FocusListItem,
  FocusName,
  ListHeader,
  ListHeaderItem,
} from '@wyden/features/focus/CommonListItems';
import React from 'react';
import { t } from 'i18next';
import {
  AutocompleteGroupedOption,
  UseAutocompleteRenderedOption,
} from '@mui/base/useAutocomplete';
import { useSortByColumn } from '@wyden/features/focus/useSortByColumn';
import { SortIcon } from '@wyden/features/focus/SortIcon';
import { ResultsOrNoResultsContainer } from '@wyden/features/focus/ResultsOrNoResultsContainer';

export const WALLETS_LIST_DATA_TEST_ID = 'wallets-list';

export const WalletsList = (props: {
  getOptionProps: (
    renderedOption: UseAutocompleteRenderedOption<
      PortfolioOption | VenueAccountsOption | TagOption | WalletOption
    >,
  ) => React.HTMLAttributes<HTMLLIElement>;
  groupedOptions:
    | (PortfolioOption | VenueAccountsOption | TagOption | WalletOption)[]
    | Array<
        AutocompleteGroupedOption<PortfolioOption | VenueAccountsOption | TagOption | WalletOption>
      >;
}) => {
  const { getIndexFromOptions, walletOptions } = useFocusOptions(props.groupedOptions);

  const { orderedData, asc, column, sortByColumn } = useSortByColumn<WalletOption>(walletOptions);

  return (
    <ResultsOrNoResultsContainer recordsCount={orderedData.length}>
      <ListHeader>
        <ListHeaderItem onClick={() => sortByColumn('name')}>
          {t('focusAutocomplete.name')}
          {column === 'name' && <SortIcon asc={asc} />}
        </ListHeaderItem>
      </ListHeader>
      <FocusList data-testid={WALLETS_LIST_DATA_TEST_ID}>
        {orderedData.map((option, index: number) => (
          <FocusListItem
            {...props.getOptionProps({ option, index: getIndexFromOptions(option) })}
            key={`${option.name}-${index}`}
          >
            <FocusLabel>
              <FocusName>{option.name}</FocusName>
            </FocusLabel>
          </FocusListItem>
        ))}
      </FocusList>
    </ResultsOrNoResultsContainer>
  );
};
