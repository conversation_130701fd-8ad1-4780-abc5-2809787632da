import { styled } from '@ui/styled';
import { color } from '@ui/theme/colors';
import { Header } from '@ui/Typography/Header';
import { Button } from '@ui/Button';
import { Label } from '@ui/Typography/Label';
import { ReactComponent as CloseIcon } from '@wyden/assets/close.svg';
import { useTranslation } from 'react-i18next';
import { RadioGroup } from '@ui/RadioGroup';
import { OutlinedInput } from '@mui/material';
import { MouseEvent, useState } from 'react';
import { Radio } from '@ui/Radio';
import { getSpacing } from '@wyden/utils/styles';
import { VerticalSpacer } from '@ui/VerticalSpacer';

export type Comparator = 'lessThan' | 'greaterThan' | 'equal' | 'between';
export type ComparatorValue = {
  comparator?: Comparator;
  values?: [number, number?];
};

interface ComparatorProps {
  headerText: string;
  onApply: (comparator?: ComparatorValue) => void;
  close: (event: { stopPropagation: () => void }) => void;
  value?: ComparatorValue;
}

export const Comparator = (props: ComparatorProps) => {
  const { t } = useTranslation();
  const [comparingValue, setComparingValue] = useState<Comparator | undefined>(
    props.value?.comparator,
  );
  const [lessThanValue, setLessThanValue] = useState<number | undefined>(
    props.value?.comparator === 'lessThan' ? props.value?.values?.[0] : undefined,
  );
  const [greaterThanValue, setGreaterThanValue] = useState<number | undefined>(
    props.value?.comparator === 'greaterThan' ? props.value?.values?.[0] : undefined,
  );
  const [betweenValue, setBetweenValue] = useState<[number?, number?] | undefined>(
    props.value?.comparator === 'between' ? props.value?.values : [undefined, undefined],
  );
  const [equalValue, setEqualValue] = useState<number | undefined>(
    props.value?.comparator === 'equal' ? props.value?.values?.[0] : undefined,
  );

  const handleReset = () => {
    setComparingValue(undefined);
    setLessThanValue(undefined);
    setGreaterThanValue(undefined);
    setBetweenValue([undefined, undefined]);
    setEqualValue(undefined);
  };

  const isLessThan = comparingValue === 'lessThan';
  const isGreaterThan = comparingValue === 'greaterThan';
  const isEqual = comparingValue === 'equal';
  const isBetween = comparingValue === 'between';

  const handleApply = (e: MouseEvent) => {
    if (isLessThan && lessThanValue !== undefined) {
      props.onApply({
        comparator: 'lessThan',
        values: [lessThanValue],
      });
    } else if (isGreaterThan && greaterThanValue !== undefined) {
      props.onApply({
        comparator: 'greaterThan',
        values: [greaterThanValue],
      });
    } else if (isEqual && equalValue !== undefined) {
      props.onApply({
        comparator: 'equal',
        values: [equalValue],
      });
    } else if (isBetween && betweenValue?.[0] !== undefined && betweenValue[1] !== undefined) {
      props.onApply({
        comparator: 'between',
        values: betweenValue as [number, number],
      });
    } else if (!isLessThan && !isGreaterThan && !isEqual && !isBetween) {
      props.onApply(undefined);
    }

    props.close(e);
  };

  return (
    <StyledContainer>
      <Header variant={'h5'}>{props.headerText}</Header>
      <StyledRadioGroup>
        <div>
          <StyledRadioWrapper>
            <Radio
              data-testid="lessThan-option"
              size="medium"
              checked={isLessThan}
              value={isLessThan}
              onChange={() => setComparingValue('lessThan')}
            />
            {t('orders.filters.lessThan')}
          </StyledRadioWrapper>
          {isLessThan && (
            <StyledInputWrapper>
              <OutlinedInput
                type="number"
                fullWidth
                data-testid="quantity-input-left"
                value={lessThanValue || ''}
                placeholder="0"
                onChange={(e) => {
                  if (e.target.value === '') {
                    setLessThanValue(undefined);
                  } else {
                    setLessThanValue(Number(e.target.value));
                  }
                }}
                inputProps={{
                  onWheel: (e) => e.currentTarget.blur(),
                }}
              />
            </StyledInputWrapper>
          )}
        </div>
        <div>
          <StyledRadioWrapper>
            <Radio
              data-testid="greaterThan-option"
              size="medium"
              checked={isGreaterThan}
              value={isGreaterThan}
              onChange={() => setComparingValue('greaterThan')}
            />
            {t('orders.filters.greaterThan')}
          </StyledRadioWrapper>
          {isGreaterThan && (
            <StyledInputWrapper>
              <OutlinedInput
                type="number"
                fullWidth
                data-testid="quantity-input-left"
                placeholder="0"
                value={greaterThanValue}
                onChange={(e) => {
                  if (e.target.value === '') {
                    setGreaterThanValue(undefined);
                  } else {
                    setGreaterThanValue(Number(e.target.value));
                  }
                }}
                inputProps={{
                  onWheel: (e) => e.currentTarget.blur(),
                }}
              />
            </StyledInputWrapper>
          )}
        </div>
        <div>
          <StyledRadioWrapper>
            <Radio
              data-testid="equal-option"
              size="medium"
              checked={isEqual}
              value={isEqual}
              onChange={() => setComparingValue('equal')}
            />
            {t('orders.filters.equal')}
          </StyledRadioWrapper>
          {isEqual && (
            <StyledInputWrapper>
              <OutlinedInput
                type="number"
                fullWidth
                data-testid="quantity-input-left"
                placeholder="0"
                value={equalValue}
                onChange={(e) => {
                  if (e.target.value === '') {
                    setEqualValue(undefined);
                  } else {
                    setEqualValue(Number(e.target.value));
                  }
                }}
                inputProps={{
                  onWheel: (e) => e.currentTarget.blur(),
                }}
              />
            </StyledInputWrapper>
          )}
        </div>
        <div>
          <StyledRadioWrapper>
            <Radio
              data-testid="between-option"
              size="medium"
              checked={isBetween}
              value={isBetween}
              onChange={() => setComparingValue('between')}
            />
            {t('orders.filters.between')}
          </StyledRadioWrapper>
          {isBetween && (
            <StyledInputWrapper>
              <OutlinedInput
                type="number"
                placeholder="0"
                data-testid="quantity-input-left"
                value={betweenValue?.[0]}
                onChange={(e) => {
                  if (e.target.value === '') {
                    setBetweenValue([undefined, betweenValue?.[1]]);
                  } else {
                    setBetweenValue([Number(e.target.value), betweenValue?.[1]]);
                  }
                }}
                inputProps={{
                  onWheel: (e) => e.currentTarget.blur(),
                }}
              />
              -
              <OutlinedInput
                type="number"
                value={betweenValue?.[1]}
                data-testid="quantity-input-right"
                placeholder="0"
                onChange={(e) => {
                  if (e.target.value === '') {
                    setBetweenValue([betweenValue?.[0], undefined]);
                  } else {
                    setBetweenValue([betweenValue?.[0], Number(e.target.value)]);
                  }
                }}
                inputProps={{
                  onWheel: (e) => e.currentTarget.blur(),
                }}
              />
            </StyledInputWrapper>
          )}
        </div>
      </StyledRadioGroup>
      <VerticalSpacer space={2} />
      <ActionsContiner>
        <Button onClick={handleReset}>
          <ResetLabel>
            <CloseIcon /> {t('common.reset')}
          </ResetLabel>
        </Button>
        <Button variant="primary" onClick={handleApply}>
          {t('common.apply')}
        </Button>
      </ActionsContiner>
    </StyledContainer>
  );
};

const StyledContainer = styled('div')`
  width: 300px;
  display: flex;
  flex-direction: column;
  gap: ${getSpacing(2)};
`;

const StyledInputWrapper = styled('div')`
  margin-left: 40px;
  display: flex;
  gap: ${getSpacing(1)};
  align-items: center;
  color: ${({ theme }) => color[theme.palette.mode].textElementsTextWeak};
  input::-webkit-outer-spin-button,
  input::-webkit-inner-spin-button {
    -webkit-appearance: none;
    margin: 0;
  }

  input[type='number'] {
    -moz-appearance: textfield;
  }
`;

const StyledRadioWrapper = styled('div')`
  display: flex;
  align-items: center;
`;

const StyledRadioGroup = styled(RadioGroup)`
  display: flex;
  flex-direction: column;
  gap: ${getSpacing(2)};
  margin-left: -10px;
`;

const ResetLabel = styled(Label)`
  display: flex;
  align-items: center;
  color: ${({ theme }) => color[theme.palette.mode].textElementsTextSecondary};
`;

const ActionsContiner = styled('div')`
  display: flex;
  align-items: center;
  justify-content: space-between;
`;
