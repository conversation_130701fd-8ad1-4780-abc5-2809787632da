import SettingsIcon from '@mui/icons-material/Settings';
import { IconButton, Tooltip } from '@mui/material';
import { useTranslation } from 'react-i18next';
import { useVenueAccounts, VenueAccountWithVenue } from '@wyden/hooks/useVenueAccounts';
import { Scope } from '@wyden/services/graphql/generated/graphql';
import { ROUTES } from '@wyden/features/Routes';
import { Link } from 'react-router-dom';

type TProps = {
  account: VenueAccountWithVenue;
};

export const EditAccount = ({ account }: TProps) => {
  const { t } = useTranslation();
  const { getAccountForId } = useVenueAccounts();
  const isDisabled = !getAccountForId(account.venueAccountId)?.scopes?.includes(Scope.Manage);

  return (
    <Tooltip title={t('common.edit')} arrow style={{ top: '-2px' }}>
      <span>
        <Link to={`${ROUTES.VENUE_ACCOUNT.ACCOUNTS}/${account.venueAccountId}`}>
          <IconButton disabled={isDisabled} data-testid={`edit-portfolio-element`}>
            <SettingsIcon fontSize="small" />
          </IconButton>
        </Link>
      </span>
    </Tooltip>
  );
};
