import { Slider } from '@ui/Slider';
import { Switch } from '@ui/Switch';
import { Paragraph } from '@ui/Typography/Paragraph';
import { styled } from '@ui/styled';
import { color } from '@ui/theme/colors';
import { getSpacing } from '@wyden/utils/styles';
import { ChangeEvent, useState } from 'react';
import { useTranslation } from 'react-i18next';

export type MarginValue = 1 | 2 | 5 | 10 | 25 | 50 | 100;

const laverageMarks = [
  {
    value: 0,
    label: '1x',
  },
  {
    value: 1,
    label: '2x',
  },
  {
    value: 2,
    label: '5x',
  },
  {
    value: 3,
    label: '10x',
  },
  {
    value: 4,
    label: '25x',
  },
  {
    value: 5,
    label: '50x',
  },
  {
    value: 6,
    label: '100x',
  },
];

export function ExchangeType() {
  const [laverage, setLaverage] = useState<MarginValue>(1);
  const [marginOn, setMarginOn] = useState(false);
  const { t } = useTranslation();

  const valuesMapperSet = {
    0: 1,
    1: 2,
    2: 5,
    3: 10,
    4: 25,
    5: 50,
    6: 100,
  } as const;
  const valuesMapperRead = {
    1: 0,
    2: 1,
    5: 2,
    10: 3,
    25: 4,
    50: 5,
    100: 6,
  } as const;

  const handleChange = (_event: Event, changeValue: number | number[]) => {
    setLaverage(valuesMapperSet[changeValue as 0 | 1 | 2 | 3 | 4 | 5 | 6]);
  };

  const handleSwitchChange = (_event: ChangeEvent, changeValue: boolean) => {
    setMarginOn(changeValue);
  };

  const laverageValue = valuesMapperRead[laverage];

  return (
    <div>
      <SwitchContainer>
        <Switch value={marginOn} onChange={handleSwitchChange} />
        <label>
          <StyledLabel variant="small">{t('simpleOrderForm.margin')}</StyledLabel>
        </label>
      </SwitchContainer>
      {marginOn && (
        <Slider
          onChange={handleChange}
          value={laverageValue || 0}
          step={1}
          max={6}
          marks={laverageMarks}
        />
      )}
    </div>
  );
}

const StyledLabel = styled(Paragraph)`
  color: ${({ theme }) => color[theme.palette.mode].textElementsTextPrimary};
`;

const SwitchContainer = styled('div')`
  margin: 8px 0;
  display: flex;
  align-items: center;
  gap: ${getSpacing(4)};
  display: none;
`;
