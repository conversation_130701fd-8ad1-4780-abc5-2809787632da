import { describe, expect, test } from 'vitest';
import { calculateButtonsPrice } from '../useBuySellButtons';
import { OrderType } from '@wyden/services/graphql/generated/graphql';

describe('calculateButtonsPrice function', () => {
  // Market orders

  test('should return N/A for Buy Market order without quantity', () => {
    expect(
      calculateButtonsPrice(
        { side: 'Buy', orderType: OrderType.Market, currentPrice: 100 },
        1,
        'USDT',
      ),
    ).toBe('N/A');
  });

  test('should return correct message for Buy Market order', () => {
    expect(
      calculateButtonsPrice(
        { side: 'Buy', orderType: OrderType.Market, currentPrice: 100, quantity: 1 },
        1,
        'USDT',
      ),
    ).toBe('simpleOrderForm.for ~ USDT\u00A0100.0');
  });

  test('should return correct message for Buy Market order with quantity = 2', () => {
    expect(
      calculateButtonsPrice(
        { side: 'Buy', orderType: OrderType.Market, currentPrice: 100, quantity: 2 },
        1,
        'USDT',
      ),
    ).toBe('simpleOrderForm.for ~ USDT\u00A0200.0');
  });

  test('should return correct message for Sell Market order', () => {
    expect(
      calculateButtonsPrice(
        { side: 'Sell', orderType: OrderType.Market, currentPrice: 150, quantity: 1 },
        1,
        'USDT',
      ),
    ).toBe('simpleOrderForm.for ~ USDT\u00A0150.0');
  });

  test('should return correct message for Sell Market order with quantity = 2', () => {
    expect(
      calculateButtonsPrice(
        { side: 'Sell', orderType: OrderType.Market, currentPrice: 150, quantity: 2 },
        1,
        'USDT',
      ),
    ).toBe('simpleOrderForm.for ~ USDT\u00A0300.0');
  });

  // Limit orders
  test('should return N/A for Buy Limit order without quantity or limit', () => {
    expect(
      calculateButtonsPrice(
        {
          side: 'Buy',
          orderType: OrderType.Limit,
          currentPrice: 90,
          limit: 110,
        },
        1,
        'USDT',
      ),
    ).toBe('N/A');
    expect(
      calculateButtonsPrice(
        {
          side: 'Buy',
          orderType: OrderType.Limit,
          currentPrice: 90,
        },
        1,
        'USDT',
      ),
    ).toBe('N/A');
    expect(
      calculateButtonsPrice(
        {
          side: 'Buy',
          orderType: OrderType.Limit,
          currentPrice: 90,
          quantity: 110,
        },
        1,
        'USDT',
      ),
    ).toBe('N/A');
  });

  test('should return correct message for Buy Limit order with price < limit', () => {
    expect(
      calculateButtonsPrice(
        {
          side: 'Buy',
          orderType: OrderType.Limit,
          currentPrice: 90,
          limit: 110,
          quantity: 1,
        },
        1,
        'USDT',
      ),
    ).toBe('simpleOrderForm.for ~ USDT\u00A090.0');
  });

  test('should return correct message for Buy Limit order with price < limit with quantity = 2', () => {
    expect(
      calculateButtonsPrice(
        {
          side: 'Buy',
          orderType: OrderType.Limit,
          currentPrice: 90,
          limit: 110,
          quantity: 2,
        },
        1,
        'USDT',
      ),
    ).toBe('simpleOrderForm.for ~ USDT\u00A0180.0');
  });

  test('should return correct message for Buy Limit order with price = limit', () => {
    expect(
      calculateButtonsPrice(
        {
          side: 'Buy',
          orderType: OrderType.Limit,
          currentPrice: 110,
          limit: 110,
          quantity: 1,
        },
        1,
        'USDT',
      ),
    ).toBe('simpleOrderForm.forMax USDT\u00A0110.0');
  });

  test('should return correct message for Buy Limit order with price = limit with quantity = 2', () => {
    expect(
      calculateButtonsPrice(
        {
          side: 'Buy',
          orderType: OrderType.Limit,
          currentPrice: 110,
          limit: 110,
          quantity: 2,
        },
        1,
        'USDT',
      ),
    ).toBe('simpleOrderForm.forMax USDT\u00A0220.0');
  });

  test('should return correct message for Buy Limit order with  price > limit', () => {
    expect(
      calculateButtonsPrice(
        {
          side: 'Buy',
          orderType: OrderType.Limit,
          currentPrice: 120,
          limit: 110,
          quantity: 1,
        },
        1,
        'USDT',
      ),
    ).toBe('simpleOrderForm.forMax USDT\u00A0110.0');
  });

  test('should return correct message for Buy Limit order with  price > limit with quantity = 2', () => {
    expect(
      calculateButtonsPrice(
        {
          side: 'Buy',
          orderType: OrderType.Limit,
          currentPrice: 120,
          limit: 110,
          quantity: 2,
        },
        1,
        'USDT',
      ),
    ).toBe('simpleOrderForm.forMax USDT\u00A0220.0');
  });

  test('should return correct message for Sell Limit order where price < limit', () => {
    expect(
      calculateButtonsPrice(
        {
          side: 'Sell',
          orderType: OrderType.Limit,
          currentPrice: 110,
          limit: 120,
          quantity: 1,
        },
        1,
        'USDT',
      ),
    ).toBe('simpleOrderForm.forMin USDT\u00A0120.0');
  });

  test('should return correct message for Sell Limit order where price < limit with quantity = 2', () => {
    expect(
      calculateButtonsPrice(
        {
          side: 'Sell',
          orderType: OrderType.Limit,
          currentPrice: 110,
          limit: 120,
          quantity: 2,
        },
        1,
        'USDT',
      ),
    ).toBe('simpleOrderForm.forMin USDT\u00A0240.0');
  });

  test('should return correct message for Sell Limit order where price = limit', () => {
    expect(
      calculateButtonsPrice(
        {
          side: 'Sell',
          orderType: OrderType.Limit,
          currentPrice: 120,
          limit: 120,
          quantity: 1,
        },
        1,
        'USDT',
      ),
    ).toBe('simpleOrderForm.for ~ USDT\u00A0120.0');
  });

  test('should return correct message for Sell Limit order where price = limit with quantity = 2', () => {
    expect(
      calculateButtonsPrice(
        {
          side: 'Sell',
          orderType: OrderType.Limit,
          currentPrice: 120,
          limit: 120,
          quantity: 2,
        },
        1,
        'USDT',
      ),
    ).toBe('simpleOrderForm.for ~ USDT\u00A0240.0');
  });

  test('should return correct message for Sell Limit order where price > limit', () => {
    expect(
      calculateButtonsPrice(
        {
          side: 'Sell',
          orderType: OrderType.Limit,
          currentPrice: 130,
          limit: 120,
          quantity: 1,
        },
        1,
        'USDT',
      ),
    ).toBe('simpleOrderForm.for ~ USDT\u00A0130.0');
  });

  test('should return correct message for Sell Limit order where price > limit with quantity = 2', () => {
    expect(
      calculateButtonsPrice(
        {
          side: 'Sell',
          orderType: OrderType.Limit,
          currentPrice: 130,
          limit: 120,
          quantity: 2,
        },
        1,
        'USDT',
      ),
    ).toBe('simpleOrderForm.for ~ USDT\u00A0260.0');
  });

  // Stop orders
  test('should return correct message for Buy Stop order where price < stop', () => {
    expect(
      calculateButtonsPrice(
        {
          side: 'Buy',
          orderType: OrderType.Stop,
          currentPrice: 120,
          limit: undefined,
          stop: 135,
          quantity: 1,
        },
        1,
        'USDT',
      ),
    ).toBe('simpleOrderForm.for ~ USDT\u00A0135.0');
  });

  test('should return N/A for Buy Stop order without stop or quantity', () => {
    expect(
      calculateButtonsPrice(
        {
          side: 'Buy',
          orderType: OrderType.Stop,
          currentPrice: 120,
          limit: undefined,
        },
        1,
        'USDT',
      ),
    ).toBe('N/A');
    expect(
      calculateButtonsPrice(
        {
          side: 'Buy',
          orderType: OrderType.Stop,
          currentPrice: 120,
          limit: undefined,
          quantity: 1,
        },
        1,
        'USDT',
      ),
    ).toBe('N/A');
    expect(
      calculateButtonsPrice(
        {
          side: 'Buy',
          orderType: OrderType.Stop,
          currentPrice: 120,
          limit: undefined,
          stop: 135,
        },
        1,
        'USDT',
      ),
    ).toBe('N/A');
  });

  test('should return correct message for Buy Stop order where price < stop with quantity = 2', () => {
    expect(
      calculateButtonsPrice(
        {
          side: 'Buy',
          orderType: OrderType.Stop,
          currentPrice: 120,
          limit: undefined,
          stop: 135,
          quantity: 2,
        },
        1,
        'USDT',
      ),
    ).toBe('simpleOrderForm.for ~ USDT\u00A0270.0');
  });

  test('should return correct message for Buy Stop order where price = stop', () => {
    expect(
      calculateButtonsPrice(
        {
          side: 'Buy',
          orderType: OrderType.Stop,
          currentPrice: 110,
          limit: undefined,
          stop: 110,
          quantity: 1,
        },
        1,
        'USDT',
      ),
    ).toBe('simpleOrderForm.for ~ USDT\u00A0110.0');
  });

  test('should return correct message for Buy Stop order where price = stop with quantity = 2', () => {
    expect(
      calculateButtonsPrice(
        {
          side: 'Buy',
          orderType: OrderType.Stop,
          currentPrice: 110,
          limit: undefined,
          stop: 110,
          quantity: 2,
        },
        1,
        'USDT',
      ),
    ).toBe('simpleOrderForm.for ~ USDT\u00A0220.0');
  });

  test('should return correct message for Buy Stop order where price >= stop', () => {
    expect(
      calculateButtonsPrice(
        {
          side: 'Buy',
          orderType: OrderType.Stop,
          currentPrice: 140,
          limit: undefined,
          stop: 135,
          quantity: 1,
        },
        1,
        'USDT',
      ),
    ).toBe('simpleOrderForm.for ~ USDT\u00A0140.0');
  });

  test('should return correct message for Buy Stop order where price >= stop with quantity = 2', () => {
    expect(
      calculateButtonsPrice(
        {
          side: 'Buy',
          orderType: OrderType.Stop,
          currentPrice: 140,
          limit: undefined,
          stop: 135,
          quantity: 2,
        },
        1,
        'USDT',
      ),
    ).toBe('simpleOrderForm.for ~ USDT\u00A0280.0');
  });

  test('should return correct message for Sell Stop order where price < stop', () => {
    expect(
      calculateButtonsPrice(
        {
          side: 'Sell',
          orderType: OrderType.Stop,
          currentPrice: 135,
          limit: undefined,
          stop: 140,
          quantity: 1,
        },
        1,
        'USDT',
      ),
    ).toBe('simpleOrderForm.for ~ USDT\u00A0135.0');
  });

  test('should return correct message for Sell Stop order where price < stop with quantity = 2', () => {
    expect(
      calculateButtonsPrice(
        {
          side: 'Sell',
          orderType: OrderType.Stop,
          currentPrice: 135,
          limit: undefined,
          stop: 140,
          quantity: 2,
        },
        1,
        'USDT',
      ),
    ).toBe('simpleOrderForm.for ~ USDT\u00A0270.0');
  });

  test('should return correct message for Sell Stop order where price = stop', () => {
    expect(
      calculateButtonsPrice(
        {
          side: 'Sell',
          orderType: OrderType.Stop,
          currentPrice: 140,
          limit: undefined,
          stop: 140,
          quantity: 1,
        },
        1,
        'USDT',
      ),
    ).toBe('simpleOrderForm.for ~ USDT\u00A0140.0');
  });

  test('should return correct message for Sell Stop order where price = stop with quantity = 2', () => {
    expect(
      calculateButtonsPrice(
        {
          side: 'Sell',
          orderType: OrderType.Stop,
          currentPrice: 140,
          limit: undefined,
          stop: 140,
          quantity: 2,
        },
        1,
        'USDT',
      ),
    ).toBe('simpleOrderForm.for ~ USDT\u00A0280.0');
  });

  test('should return correct message for Sell Stop order where price > stop', () => {
    expect(
      calculateButtonsPrice(
        {
          side: 'Sell',
          orderType: OrderType.Stop,
          currentPrice: 145,
          limit: undefined,
          stop: 140,
          quantity: 1,
        },
        1,
        'USDT',
      ),
    ).toBe('simpleOrderForm.for ~ USDT\u00A0140.0');
  });

  test('should return correct message for Sell Stop order where price > stop with quantity = 2', () => {
    expect(
      calculateButtonsPrice(
        {
          side: 'Sell',
          orderType: OrderType.Stop,
          currentPrice: 145,
          limit: undefined,
          stop: 140,
          quantity: 2,
        },
        1,
        'USDT',
      ),
    ).toBe('simpleOrderForm.for ~ USDT\u00A0280.0');
  });

  // Stop-Limit orders for Buy
  test('should return correct message for Buy Stop-Limit order where current <= limit <= stop', () => {
    expect(
      calculateButtonsPrice(
        {
          side: 'Buy',
          orderType: OrderType.StopLimit,
          currentPrice: 100,
          limit: 110,
          stop: 120,
          quantity: 1,
        },
        1,
        'USDT',
      ),
    ).toBe('simpleOrderForm.forMax USDT\u00A0110.0');
  });

  test('should return N/A for Buy Stop-Limit without,limit,stop or quantity', () => {
    expect(
      calculateButtonsPrice(
        {
          side: 'Buy',
          orderType: OrderType.StopLimit,
          currentPrice: 100,
          stop: 120,
          quantity: 1,
        },
        1,
        'USDT',
      ),
    ).toBe('N/A');
    expect(
      calculateButtonsPrice(
        {
          side: 'Buy',
          orderType: OrderType.StopLimit,
          currentPrice: 100,
          limit: 110,
          quantity: 1,
        },
        1,
        'USDT',
      ),
    ).toBe('N/A');
    expect(
      calculateButtonsPrice(
        {
          side: 'Buy',
          orderType: OrderType.StopLimit,
          currentPrice: 100,
          limit: 110,
          stop: 120,
        },
        1,
        'USDT',
      ),
    ).toBe('N/A');
    expect(
      calculateButtonsPrice(
        {
          side: 'Buy',
          orderType: OrderType.StopLimit,
          currentPrice: 100,
        },
        1,
        'USDT',
      ),
    ).toBe('N/A');
  });

  test('should return correct message for Buy Stop-Limit order where current <= limit <= stop with quantity = 2', () => {
    expect(
      calculateButtonsPrice(
        {
          side: 'Buy',
          orderType: OrderType.StopLimit,
          currentPrice: 100,
          limit: 110,
          stop: 120,
          quantity: 2,
        },
        1,
        'USDT',
      ),
    ).toBe('simpleOrderForm.forMax USDT\u00A0220.0');
  });

  test('should return correct message for Buy Stop-Limit order where current = limit = stop', () => {
    expect(
      calculateButtonsPrice(
        {
          side: 'Buy',
          orderType: OrderType.StopLimit,
          currentPrice: 100,
          limit: 100,
          stop: 100,
          quantity: 1,
        },
        1,
        'USDT',
      ),
    ).toBe('simpleOrderForm.forMax USDT\u00A0100.0');
  });

  test('should return correct message for Buy Stop-Limit order where current = limit = stop with quantity = 2', () => {
    expect(
      calculateButtonsPrice(
        {
          side: 'Buy',
          orderType: OrderType.StopLimit,
          currentPrice: 100,
          limit: 100,
          stop: 100,
          quantity: 2,
        },
        1,
        'USDT',
      ),
    ).toBe('simpleOrderForm.forMax USDT\u00A0200.0');
  });

  test('should return correct message for Buy Stop-Limit order where current < stop <= limit', () => {
    expect(
      calculateButtonsPrice(
        {
          side: 'Buy',
          orderType: OrderType.StopLimit,
          currentPrice: 100,
          limit: 120,
          stop: 110,
          quantity: 1,
        },
        1,
        'USDT',
      ),
    ).toBe('simpleOrderForm.for ~ USDT\u00A0110.0');
  });

  test('should return correct message for Buy Stop-Limit order where current < stop <= limit with quantity = 2', () => {
    expect(
      calculateButtonsPrice(
        {
          side: 'Buy',
          orderType: OrderType.StopLimit,
          currentPrice: 100,
          limit: 120,
          stop: 110,
          quantity: 2,
        },
        1,
        'USDT',
      ),
    ).toBe('simpleOrderForm.for ~ USDT\u00A0220.0');
  });

  test('should return correct message for Buy Stop-Limit order where stop <= curr <= limit', () => {
    expect(
      calculateButtonsPrice(
        {
          side: 'Buy',
          orderType: OrderType.StopLimit,
          currentPrice: 115,
          limit: 120,
          stop: 110,
          quantity: 1,
        },
        1,
        'USDT',
      ),
    ).toBe('simpleOrderForm.forMax USDT\u00A0120.0');
  });

  test('should return correct message for Buy Stop-Limit order where stop <= curr <= limit with quantity = 2', () => {
    expect(
      calculateButtonsPrice(
        {
          side: 'Buy',
          orderType: OrderType.StopLimit,
          currentPrice: 115,
          limit: 120,
          stop: 110,
          quantity: 2,
        },
        1,
        'USDT',
      ),
    ).toBe('simpleOrderForm.forMax USDT\u00A0240.0');
  });

  test('should return correct message for Buy Stop-Limit order where limit < curr < stop', () => {
    expect(
      calculateButtonsPrice(
        {
          side: 'Buy',
          orderType: OrderType.StopLimit,
          currentPrice: 115,
          limit: 110,
          stop: 120,
          quantity: 1,
        },
        1,
        'USDT',
      ),
    ).toBe('simpleOrderForm.forMax USDT\u00A0110.0');
  });

  test('should return correct message for Buy Stop-Limit order where limit < curr < stop with quantity = 2', () => {
    expect(
      calculateButtonsPrice(
        {
          side: 'Buy',
          orderType: OrderType.StopLimit,
          currentPrice: 115,
          limit: 110,
          stop: 120,
          quantity: 2,
        },
        1,
        'USDT',
      ),
    ).toBe('simpleOrderForm.forMax USDT\u00A0220.0');
  });

  // Stop-Limit orders for Sell
  test('should return correct message for Sell Stop-Limit order where curr < limit <= stop', () => {
    expect(
      calculateButtonsPrice(
        {
          side: 'Sell',
          orderType: OrderType.StopLimit,
          currentPrice: 100,
          limit: 110,
          stop: 120,
          quantity: 1,
        },
        1,
        'USDT',
      ),
    ).toBe('simpleOrderForm.forMin USDT\u00A0110.0');
  });

  test('should return correct message for Sell Stop-Limit order where curr < limit <= stop with quantity = 2', () => {
    expect(
      calculateButtonsPrice(
        {
          side: 'Sell',
          orderType: OrderType.StopLimit,
          currentPrice: 100,
          limit: 110,
          stop: 120,
          quantity: 2,
        },
        1,
        'USDT',
      ),
    ).toBe('simpleOrderForm.forMin USDT\u00A0220.0');
  });

  test('should return correct message for Sell Stop-Limit order where curr <= stop <= limit', () => {
    expect(
      calculateButtonsPrice(
        {
          side: 'Sell',
          orderType: OrderType.StopLimit,
          currentPrice: 110,
          limit: 120,
          stop: 115,
          quantity: 1,
        },
        1,
        'USDT',
      ),
    ).toBe('simpleOrderForm.forMin USDT\u00A0120.0');
  });

  test('should return correct message for Sell Stop-Limit order where curr <= stop <= limit with quantity = 2', () => {
    expect(
      calculateButtonsPrice(
        {
          side: 'Sell',
          orderType: OrderType.StopLimit,
          currentPrice: 110,
          limit: 120,
          stop: 115,
          quantity: 2,
        },
        1,
        'USDT',
      ),
    ).toBe('simpleOrderForm.forMin USDT\u00A0240.0');
  });

  test('should return correct message for Sell Stop-Limit order where stop < curr < limit', () => {
    expect(
      calculateButtonsPrice(
        {
          side: 'Sell',
          orderType: OrderType.StopLimit,
          currentPrice: 115,
          limit: 120,
          stop: 110,
          quantity: 1,
        },
        1,
        'USDT',
      ),
    ).toBe('simpleOrderForm.forMin USDT\u00A0120.0');
  });

  test('should return correct message for Sell Stop-Limit order where stop < curr < limit with quantity = 2', () => {
    expect(
      calculateButtonsPrice(
        {
          side: 'Sell',
          orderType: OrderType.StopLimit,
          currentPrice: 115,
          limit: 120,
          stop: 110,
          quantity: 2,
        },
        1,
        'USDT',
      ),
    ).toBe('simpleOrderForm.forMin USDT\u00A0240.0');
  });

  test('should return correct message for Sell Stop-Limit order where limit < curr <= stop', () => {
    expect(
      calculateButtonsPrice(
        {
          side: 'Sell',
          orderType: OrderType.StopLimit,
          currentPrice: 130,
          limit: 120,
          stop: 130,
          quantity: 1,
        },
        1,
        'USDT',
      ),
    ).toBe('simpleOrderForm.for ~ USDT\u00A0130.0');
  });

  test('should return correct message for Sell Stop-Limit order where limit < curr <= stop with quantity = 2', () => {
    expect(
      calculateButtonsPrice(
        {
          side: 'Sell',
          orderType: OrderType.StopLimit,
          currentPrice: 130,
          limit: 120,
          stop: 130,
          quantity: 2,
        },
        1,
        'USDT',
      ),
    ).toBe('simpleOrderForm.for ~ USDT\u00A0260.0');
  });

  test('should return correct message for Sell Stop-Limit order where limit <= stop < curr', () => {
    expect(
      calculateButtonsPrice(
        {
          side: 'Sell',
          orderType: OrderType.StopLimit,
          currentPrice: 130,
          limit: 110,
          stop: 120,
          quantity: 1,
        },
        1,
        'USDT',
      ),
    ).toBe('simpleOrderForm.for ~ USDT\u00A0120.0');
  });

  test('should return correct message for Sell Stop-Limit order where limit <= stop < curr with quantity = 2', () => {
    expect(
      calculateButtonsPrice(
        {
          side: 'Sell',
          orderType: OrderType.StopLimit,
          currentPrice: 130,
          limit: 110,
          stop: 120,
          quantity: 2,
        },
        1,
        'USDT',
      ),
    ).toBe('simpleOrderForm.for ~ USDT\u00A0240.0');
  });

  test('should return correct message for Sell Stop-Limit order where stop <= limit <= curr', () => {
    expect(
      calculateButtonsPrice(
        {
          side: 'Sell',
          orderType: OrderType.StopLimit,
          currentPrice: 130,
          limit: 120,
          stop: 110,
          quantity: 1,
        },
        1,
        'USDT',
      ),
    ).toBe('simpleOrderForm.forMin USDT\u00A0120.0');
  });

  test('should return correct message for Sell Stop-Limit order where stop <= limit <= curr with quantity = 2', () => {
    expect(
      calculateButtonsPrice(
        {
          side: 'Sell',
          orderType: OrderType.StopLimit,
          currentPrice: 130,
          limit: 120,
          stop: 110,
          quantity: 2,
        },
        1,
        'USDT',
      ),
    ).toBe('simpleOrderForm.forMin USDT\u00A0240.0');
  });
});
