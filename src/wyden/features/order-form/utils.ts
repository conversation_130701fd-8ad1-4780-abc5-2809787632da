import { countFractionDigits } from '@wyden/helpers/countFractionDigits';
import { isFulfilledValue } from '../broker-desk-configuration-form/utils';
import Big from 'big.js';
import { t } from 'i18next';

export const getNumberInputWarningMessage = ({
  value,
  min,
  max,
  step,
}: {
  value: number | string | undefined | null;
  min: number | string | undefined | null;
  max: number | string | undefined | null;
  step: number | string | undefined | null;
}) => {
  if (!isFulfilledValue(value)) return;

  try {
    const floatMin = isFulfilledValue(min) && new Big(`${min}`);
    const floatMax = isFulfilledValue(max) && new Big(`${max}`);
    const floatStep = isFulfilledValue(step) && new Big(`${step}`);
    const floatValue = new Big(`${value}`);

    if (floatMin && floatValue.lt(floatMin)) {
      return t('orderEntry.numberToSmall', {
        minValue: floatMin.toFixed(),
      });
    }
    if (floatMax && floatValue.gt(floatMax)) {
      return t('orderEntry.numberToLarge', {
        maxValue: floatMax.toFixed(),
      });
    }
    if (floatStep && floatValue.mod(floatStep).toFixed() !== '0') {
      return t('orderEntry.wrongStep', {
        stepValue: floatStep.toFixed(),
      });
    }
    return '';
  } catch (e) {
    return '';
  }
};

export const getMinValue = (min: string | null | undefined, step: string | null | undefined) =>
  // if min and incr resolution is different (not valid data) then do not set this property
  countFractionDigits(min) <= countFractionDigits(step) && min?.length ? min : 0;
