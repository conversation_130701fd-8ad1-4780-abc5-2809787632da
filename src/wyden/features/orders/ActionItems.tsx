import { ICellRendererParams } from 'ag-grid-community';
import { OrderStateResponse } from '@wyden/services/graphql/generated/graphql';
import { useOrderDetailsStore } from '@wyden/features/order-details/useOrderDetails';
import { useTranslation } from 'react-i18next';
import { ContextDropdownItem } from '@ui/ContextDropdown';
import InfoIcon from '@mui/icons-material/Info';
import SearchIcon from '@mui/icons-material/Search';
import { Label } from '@ui/Typography/Label';
import { useNestReact } from '@nest-react/hooks/useNestReact';
import { WIDGETS_NAMES } from '../widgets-renderer/widget-names';
import { useOrderHistoryFilters } from '../orders-history/useOrderHistoryFilters';

export const OrderDetails = (params: ICellRendererParams<OrderStateResponse>) => {
  const { openOrderDetailsDialog } = useOrderDetailsStore();
  const { t } = useTranslation();

  return (
    <ContextDropdownItem onClick={() => params.data && openOrderDetailsDialog(params.data)}>
      <InfoIcon />
      <Label>{t('ordersHistory.orderDetails.orderDetails')}</Label>
    </ContextDropdownItem>
  );
};

export const FindChildrenOrders = (params: ICellRendererParams<OrderStateResponse>) => {
  const { t } = useTranslation();
  const { setTabComponentActive } = useNestReact();
  const { getComponentByName } = useNestReact();
  const setParentOrderId = useOrderHistoryFilters((state) => state.setParentOrderId);
  const resetFilters = useOrderHistoryFilters((state) => state.clear);

  const handleClick = () => {
    const orderHistoryComponent = getComponentByName(WIDGETS_NAMES.ORDERS_HISTORY);
    setTabComponentActive(orderHistoryComponent);
    if (params.data?.orderId) {
      resetFilters(orderHistoryComponent.id)();
      setParentOrderId(orderHistoryComponent.id)(params.data?.orderId);
    }
  };

  return (
    <ContextDropdownItem onClick={handleClick}>
      <SearchIcon />
      <Label>{t('ordersHistory.findChildrenOrders')}</Label>
    </ContextDropdownItem>
  );
};
