import { useTheme } from '@mui/material';
import { ICellRendererParams } from 'ag-grid-community';

import { styled } from '@ui/styled';
import { color } from '@ui/theme/colors';
import { OrderStateResponse, Side } from '@wyden/services/graphql/generated/graphql';

export const SideRenderer = ({ data }: ICellRendererParams<OrderStateResponse>) => (
  <SideColoredLabel {...data} />
);

export const SideColoredLabel = ({ side }: { side?: Side | undefined }) => {
  const theme = useTheme();
  const sideColor =
    side === Side.Buy
      ? color[theme.palette.mode].textSemanticTextSuccessPrimary
      : side === Side.Sell
      ? color[theme.palette.mode].textSemanticTextErrorPrimary
      : 'inherit';

  return <StyledSide $color={sideColor}>{side}</StyledSide>;
};

const StyledSide = styled('span')<{ $color?: string }>`
  color: ${({ $color }) => $color} !important;
`;
