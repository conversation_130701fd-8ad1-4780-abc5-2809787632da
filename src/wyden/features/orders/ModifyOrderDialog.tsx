import { styled } from '@mui/material';
import { useTranslation } from 'react-i18next';
import { Dialog } from '../../../ui/Dialog';
import { Paper } from '../../../ui/Paper';
import { Label } from '../../../ui/Typography/Label';
import { color } from '../../../ui/theme/colors';
import { OrderType } from '../../services/graphql/generated/graphql';
import { getSpacing } from '../../utils/styles';
import {
  ModifyOrderForm,
  getModifyOrderFormProps,
  modifyOrderFormSchema,
  transformToModificationForm,
} from './ModifyOrderForm';
import { useCancelReplaceOrder } from './useCancelReplaceOrder';
import { useOrdersStore } from './useOrdersStore';

export const ModifyOrderDialog = () => {
  const { t } = useTranslation();
  const { cancelReplaceOrder } = useCancelReplaceOrder();
  const { orderToModify, setOrderToModify } = useOrdersStore();

  const modifyOrderFormProps = getModifyOrderFormProps(
    t,
    orderToModify?.instrument,
    orderToModify?.currency === orderToModify?.instrument?.baseInstrument.quoteCurrency,
  );
  const defaultValues = transformToModificationForm(orderToModify);
  const isAccountsExist = !!defaultValues.venueAccounts?.length;
  const isLimitFieldAllowed =
    defaultValues.orderType === OrderType.Limit || defaultValues.orderType === OrderType.StopLimit;
  const isStopFieldAllowed =
    defaultValues.orderType === OrderType.Stop || defaultValues.orderType === OrderType.StopLimit;

  return (
    <Dialog open={!!orderToModify} onClose={() => setOrderToModify(undefined)}>
      <ModifyOrderForm
        onSubmit={async (values) => {
          await cancelReplaceOrder(values);
          setOrderToModify(undefined);
        }}
        schema={modifyOrderFormSchema}
        props={modifyOrderFormProps}
        defaultValues={defaultValues}
      >
        {(fields) => (
          <>
            <StyledSectionHeader variant="small">
              {t('modifyOrderForm.orderDetails')}
            </StyledSectionHeader>
            <StyledPaper variant="outlined">
              {fields.instrumentId}
              {fields.side}
              {isAccountsExist && fields.venueAccounts}
              {fields.portfolioId}
            </StyledPaper>
            <StyledSectionHeader variant="small">{t('common.change')}</StyledSectionHeader>
            <Row>
              {fields.quantity}
              {fields.filledQty}
            </Row>
            <Row>{fields.quantityChange}</Row>
            <Row>
              {isStopFieldAllowed && fields.stopPrice}
              {isLimitFieldAllowed && fields.limitPrice}
            </Row>
            <Row>
              {isStopFieldAllowed && fields.stopChange}
              {isLimitFieldAllowed && fields.limitChange}
            </Row>
          </>
        )}
      </ModifyOrderForm>
    </Dialog>
  );
};

const Row = styled('div')`
  display: flex;
  gap: ${getSpacing(4)};
`;

const StyledPaper = styled(Paper)`
  display: grid;
  grid-template-columns: 1fr 1fr;
  padding: ${getSpacing(3)};
  gap: ${getSpacing(4)};
`;

const StyledSectionHeader = styled(Label)`
  color: ${({ theme }) => color[theme.palette.mode].textElementsTextWeak};
`;
