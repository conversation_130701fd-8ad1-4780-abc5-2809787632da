import { describe, expect, it } from 'vitest';
import {
  MoneyFromCurrencyValueGetter,
  MoneyFromGivenCurrencyValueGetter,
  MoneyFromInstrumentValueGetter,
  MoneyRenderer,
} from '../Renderers';
import { ICellRendererParams, ValueGetterParams } from 'ag-grid-community';

describe('Money Value Getters', () => {
  it('should be resistant to wrong types', () => {
    const vgp: ValueGetterParams = {
      data: { tested: { value: 'asd', currency: 500, priceIncr: 0.01 } },
    } as ValueGetterParams;
    const tested = MoneyFromInstrumentValueGetter(
      'tested.value',
      'tested.currency',
      'tested.priceIncr',
    );
    expect(tested(vgp)).not.toBeNull();
  });

  it('MoneyFromInstrument', () => {
    const vgp: ValueGetterParams = {
      data: { tested: { value: 12.345, currency: 'USD', priceIncr: '0.01000' } },
    } as ValueGetterParams;
    const tested = MoneyFromInstrumentValueGetter(
      'tested.value',
      'tested.currency',
      'tested.priceIncr',
    );
    expect(tested(vgp)).toStrictEqual(
      JSON.stringify({
        value: 12.345,
        currencyCode: 'USD',
        valueFractionDigits: 2,
        valueKey: 'tested.value',
        currencyCodeSource: 'tested.currency',
        valueFractionDigitsSource: 'tested.priceIncr',
      }),
    );
  });
  it('MoneyFromCurrency', () => {
    const vgp: ValueGetterParams = {
      data: { tested: { value: 12.345, currency: 'USD' } },
    } as ValueGetterParams;
    const tested = MoneyFromCurrencyValueGetter('tested.value', 'tested.currency');
    expect(tested(vgp)).toStrictEqual(
      JSON.stringify({
        value: 12.345,
        currencyCode: 'USD',
        valueFractionDigits: 2,
        valueKey: 'tested.value',
        currencyCodeSource: 'tested.currency',
        valueFractionDigitsSource: 'Currencies object',
      }),
    );
  });

  it('MoneyFromCurrency non-mapped currency', () => {
    const vgp: ValueGetterParams = {
      data: { tested: { value: 12.345, currency: 'DIAXY' } },
    } as ValueGetterParams;
    const tested = MoneyFromCurrencyValueGetter('tested.value', 'tested.currency');
    expect(tested(vgp)).toStrictEqual(
      JSON.stringify({
        value: 12.345,
        currencyCode: 'DIAXY',
        valueFractionDigits: 2,
        valueKey: 'tested.value',
        currencyCodeSource: 'tested.currency',
        valueFractionDigitsSource: 'Fallback to 2',
      }),
    );
  });

  it('MoneyFromCurrency wrong mapping', () => {
    const vgp: ValueGetterParams = {
      data: { tested: { value: 12.345, currency: 'DIAXY' } },
    } as ValueGetterParams;
    const tested = MoneyFromCurrencyValueGetter('tested.value', 'tested.currencyWrong');
    expect(tested(vgp)).toStrictEqual(
      JSON.stringify({
        value: 12.345,
        currencyCode: undefined,
        valueFractionDigits: 2,
        valueKey: 'tested.value',
        currencyCodeSource: 'tested.currencyWrong',
        valueFractionDigitsSource: 'Fallback to 2',
      }),
    );
  });

  it('MoneyFromGivenCurrency', () => {
    const vgp: ValueGetterParams = { data: { tested: { value: 12.345 } } } as ValueGetterParams;
    const tested = MoneyFromGivenCurrencyValueGetter('tested.value', 'USD');
    expect(tested(vgp)).toStrictEqual(
      JSON.stringify({
        value: 12.345,
        currencyCode: 'USD',
        valueFractionDigits: 2,
        valueKey: 'tested.value',
        currencyCodeSource: 'Given currency',
        valueFractionDigitsSource: 'Currencies object',
      }),
    );
  });
  it('MoneyFromGivenCurrency non-mapped currency', () => {
    const vgp: ValueGetterParams = { data: { tested: { value: 12.345 } } } as ValueGetterParams;
    const tested = MoneyFromGivenCurrencyValueGetter('tested.value', 'DIAXY');
    expect(tested(vgp)).toStrictEqual(
      JSON.stringify({
        value: 12.345,
        currencyCode: 'DIAXY',
        valueFractionDigits: 2,
        valueKey: 'tested.value',
        currencyCodeSource: 'Given currency',
        valueFractionDigitsSource: 'Fallback to 2',
      }),
    );
  });
});

describe('Money Renderers', () => {
  it('should be resistant to wrong types', () => {
    const vgpr = JSON.stringify({
      currencyCode: 'DIAXY',
      currencyCodeSource: 'Given currency',
      value: 'wrong',
      valueFractionDigits: 2,
      valueFractionDigitsSource: 'Fallback to 2',
      valueKey: 'tested.value',
    });
    const crp: ICellRendererParams<unknown, string> = {
      value: vgpr,
    } as unknown as ICellRendererParams<unknown, string>;
    const tested = MoneyRenderer(false, false);
    expect(tested(crp)).toMatchSnapshot();
  });
  it('MoneyRenderer', () => {
    const vgpr = JSON.stringify({
      currencyCode: 'USD',
      currencyCodeSource: 'Given currency',
      value: 12.345,
      valueFractionDigits: 2,
      valueFractionDigitsSource: 'Fallback to 2',
      valueKey: 'tested.value',
    });
    const crp: ICellRendererParams<unknown, string> = {
      value: vgpr,
    } as unknown as ICellRendererParams<unknown, string>;
    const tested = MoneyRenderer(false, false);
    expect(tested(crp)).toMatchSnapshot();
  });
  it('MoneyRenderer omiting the currency code', () => {
    const vgpr = JSON.stringify({
      currencyCode: 'USD',
      currencyCodeSource: 'Given currency',
      value: 12.345,
      valueFractionDigits: 2,
      valueFractionDigitsSource: 'Fallback to 2',
      valueKey: 'tested.value',
    });
    const crp: ICellRendererParams<unknown, string> = {
      value: vgpr,
    } as unknown as ICellRendererParams<unknown, string>;
    const tested = MoneyRenderer(true, false);
    expect(tested(crp)).toMatchSnapshot();
  });
  it('MoneyRenderer with custom currency code', () => {
    const vgpr = JSON.stringify({
      currencyCode: 'DIAXY',
      currencyCodeSource: 'Given currency',
      value: 12.345,
      valueFractionDigits: 2,
      valueFractionDigitsSource: 'Fallback to 2',
      valueKey: 'tested.value',
    });
    const crp: ICellRendererParams<unknown, string> = {
      value: vgpr,
    } as unknown as ICellRendererParams<unknown, string>;
    const tested = MoneyRenderer(false, false);
    expect(tested(crp)).toMatchSnapshot();
  });

  it('MoneyRenderer with with currency code and no rounding', () => {
    const vgpr = JSON.stringify({
      currencyCode: 'USD',
      currencyCodeSource: 'Given currency',
      // eslint-disable-next-line @typescript-eslint/no-loss-of-precision
      value: 12.3456789101234567891,
      valueFractionDigits: 2,
      valueFractionDigitsSource: 'Fallback to 2',
      valueKey: 'tested.value',
    });
    const crp: ICellRendererParams<unknown, string> = {
      value: vgpr,
    } as unknown as ICellRendererParams<unknown, string>;
    const tested = MoneyRenderer(false, true);
    expect(tested(crp)).toMatchSnapshot();
  });
});
