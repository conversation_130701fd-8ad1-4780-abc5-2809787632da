import { ApolloError } from '@apollo/client';
import { Skeleton } from '../../../ui/Skeleton';
import { styled } from '../../../ui/styled';
import { color } from '../../../ui/theme/colors';
import { LoadingErrorComponent } from '../LoadingDataContainer';
import { ReactNode } from 'react';
import { Paragraph } from '@ui/Typography/Paragraph';
import { getSpacing } from '@wyden/utils/styles';

type Props = {
  error?: ApolloError;
  initialLoading: boolean;
  reloading: boolean;
  showNoRows: boolean;
  refresh: () => void;
  children?: ReactNode;
  noRowsInfo?: string;
  spacingBottom?: number;
};

export const InfiniteScrollGridWrapper = ({
  error,
  reloading,
  initialLoading,
  showNoRows,
  refresh,
  noRowsInfo,
  children,
  spacingBottom = 0,
}: Props) => {
  return (
    <StyledContainer $spacingBottom={spacingBottom}>
      {children}
      {initialLoading && (
        <StyledSkeletonWrapper>
          <StyledSkeleton variant="rectangular" />
        </StyledSkeletonWrapper>
      )}
      {error && <LoadingErrorComponent absolute error={error} refetch={refresh} />}
      {reloading && !initialLoading && <StyledReloading />}
      {showNoRows && !error && (
        <StyledNoRows>
          <Paragraph variant="small">{noRowsInfo}</Paragraph>
        </StyledNoRows>
      )}
    </StyledContainer>
  );
};

const StyledContainer = styled('div')<{ $spacingBottom: number }>`
  height: calc(100% - ${({ $spacingBottom }) => getSpacing($spacingBottom)});
  width: 100%;
  position: relative;
  padding-bottom: ${({ $spacingBottom }) => getSpacing($spacingBottom)};
`;

const StyledReloading = styled('div')`
  width: 100%;
  position: absolute;
  top: 30px;
  background-color: ${({ theme }) => color[theme.palette.mode].fillsSurfaceSurfaceSecondary};
  height: calc(100% - 30px);
`;

const StyledSkeletonWrapper = styled('div')`
  width: 100%;
  position: absolute;
  top: 0;
  background-color: ${({ theme }) => color[theme.palette.mode].fillsSurfaceSurfaceSecondary};
  height: 100%;
`;

const StyledSkeleton = styled(Skeleton)`
  width: 100%;
  position: absolute;
  top: 0;
  height: 100%;
`;

const StyledNoRows = styled('div')`
  position: absolute;
  left: 0;
  top: 30px;
  width: 100%;
  height: calc(100% - 30px);
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: ${({ theme }) => color[theme.palette.mode].fillsSurfaceSurfaceSecondary};
`;
