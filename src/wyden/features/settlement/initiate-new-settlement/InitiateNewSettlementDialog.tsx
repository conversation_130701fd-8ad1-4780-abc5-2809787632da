import { Dialog } from '@ui/Dialog';
import { styled } from '@ui/styled';
import { getSpacing } from '@wyden/utils/styles';
import { TFunction } from 'i18next';
import { useTranslation } from 'react-i18next';
import {
  InitiateNewSettlementForm,
  InitiateNewSettlementFormSchema,
} from './InitiateNewSettlementForm';
import { useInitiateNewSettlementStore } from './useInitiateNewSettlementStore';
import { useSettlementManagement } from './useSettlementManagement';
import { VenueType } from '../../../services/graphql/generated/graphql';

export const InitiateNewSettlementDialog = () => {
  const { initiateNewSettlement } = useSettlementManagement();
  const { initiateNewSettlementDialogOpen, toggleInitiateNewSettlementDialog } =
    useInitiateNewSettlementStore();
  const { t } = useTranslation();

  const getInitiateNewSettlementFormProps = (t: TFunction<'translation', undefined>) => ({
    accounts: {
      label: t('common.accounts'),
      required: true,
      id: 'accounts',
      venueTypes: [VenueType.Street],
      includeDeactivated: true,
    },
  });

  return (
    <StyledDialog
      open={initiateNewSettlementDialogOpen}
      onClose={toggleInitiateNewSettlementDialog}
    >
      <InitiateNewSettlementForm
        onSubmit={(values) => {
          initiateNewSettlement(values);
          toggleInitiateNewSettlementDialog();
        }}
        props={getInitiateNewSettlementFormProps(t)}
        schema={InitiateNewSettlementFormSchema}
      />
    </StyledDialog>
  );
};

const StyledDialog = styled(Dialog)`
  padding-top: ${getSpacing(3)};
`;
