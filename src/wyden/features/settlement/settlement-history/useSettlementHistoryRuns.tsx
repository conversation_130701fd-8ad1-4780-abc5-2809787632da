import { getTime } from 'date-fns';
import { useEffect, useMemo } from 'react';
import { useTranslation } from 'react-i18next';
import { ERRORS } from '../../../constants';
import {
  SettlementRunResponse,
  useSettlementHistoryRunsQuery,
} from '../../../services/graphql/generated/graphql';
import { useEventLogs } from '../../error-indicators/event-logs/useEventLogs';
import { NetworkEvents } from '../../error-indicators/network-indicators/events';
import { useNetworkStore } from '../../error-indicators/network-indicators/useNetworkStore';
import { sortByDateDesc } from '../settlement-opened/useLiveSettlementRunsStore';
import { useSettlementFilters } from './useSettlementFilters';

export const useSettlementHistoryRuns = () => {
  const { upsertRequest } = useNetworkStore();
  const { addEventLog } = useEventLogs();
  const { t } = useTranslation();
  const { filters } = useSettlementFilters();

  const {
    data,
    loading: settlementHistoryRunsLoading,
    error: settlementHistoryRunsError,
    refetch: settlementHistoryRunsRefetch,
  } = useSettlementHistoryRunsQuery({
    variables: { date: getTime(filters.selectedDate).toString() },
    fetchPolicy: 'network-only',
    onError: (err) => {
      upsertRequest('settlementHistoryRuns', {
        pending: false,
        error: ERRORS.CLIENT_ERROR,
        err,
      });
      addEventLog({
        type: NetworkEvents.SETTLEMENT_HISTORY_RUNS,
        message: t('eventLogs.requestFailed', {
          name: t('settlement.settlementHistoryRunsQuery'),
        }),
        timestamp: Date.now(),
      });
    },
  });

  useEffect(() => {
    upsertRequest('settlementHistoryRuns', {
      pending: settlementHistoryRunsLoading,
    });
  }, [settlementHistoryRunsLoading, upsertRequest]);

  const settlementHistoryRuns: SettlementRunResponse[] = useMemo(
    () => sortByDateDesc(data?.settlementHistoryRuns ?? []),
    [data],
  );

  return {
    settlementHistoryRuns,
    settlementHistoryRunsRefetch,
    settlementHistoryRunsLoading,
    settlementHistoryRunsError,
  };
};
