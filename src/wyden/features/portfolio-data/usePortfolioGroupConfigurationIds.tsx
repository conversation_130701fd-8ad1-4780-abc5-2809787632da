import { useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { ERRORS } from '../../constants';
import { usePortfolioGroupConfigurationListQuery } from '../../services/graphql/generated/graphql';
import { useEventLogs } from '../error-indicators/event-logs/useEventLogs';
import { NetworkEvents } from '../error-indicators/network-indicators/events';
import { useNetworkStore } from '../error-indicators/network-indicators/useNetworkStore';

export const usePortfolioGroupConfigurationList = () => {
  const { upsertRequest } = useNetworkStore();
  const { addEventLog } = useEventLogs();
  const { t } = useTranslation();

  const { data, loading, error } = usePortfolioGroupConfigurationListQuery({
    variables: {},
    onError: (err) => {
      upsertRequest('portfolioGroupConfigurationList', {
        pending: false,
        error: ERRORS.CLIENT_ERROR,
        err,
      });
      addEventLog({
        type: NetworkEvents.PORTFOLIO_GROUP_CONFIGURATION_LIST,
        message: t('eventLogs.requestFailed', {
          name: t('portfolioData.portfolioGroupConfigurationIdsQuery'),
        }),
        timestamp: Date.now(),
      });
    },
  });

  useEffect(() => {
    upsertRequest('portfolioGroupConfigurationList', {
      pending: loading,
    });
  }, [loading, upsertRequest]);

  return {
    portfolioGroupConfigurationList: data?.portfolioGroupConfigurationList,
    loading,
    error,
  };
};
