// Vitest Snapshot v1, https://vitest.dev/guide/snapshot.html

exports[`User Data > should save created user data with given widget data id:history-positions, type:POSITIONS_QUERY  1`] = `
{
  "request": {
    "data": "{\\"widgets\\":[{\\"id\\":\\"history-positions\\",\\"type\\":\\"POSITIONS_QUERY\\",\\"filters\\":{\\"currency\\":[\\"1000SATS\\"],\\"accounts\\":[],\\"portfolios\\":[{\\"__typename\\":\\"PortfolioResponse\\",\\"archivedAt\\":null,\\"createdAt\\":\\"2024-08-14T13:26:24.983252Z\\",\\"id\\":\\"test\\",\\"name\\":\\"test\\",\\"portfolioCurrency\\":\\"USDT\\",\\"portfolioType\\":\\"VOSTRO\\",\\"scopes\\":[\\"MANAGE\\",\\"READ\\",\\"TRADE\\"],\\"tags\\":[]}]}}],\\"timestamp\\":************}",
  },
}
`;
