import '../../../../../cypress/support/component';
import { OrderDetailsOrderBook, SYNC_ORDERBOOK_DATA_TEST_ID } from '../OrderDetailsOrderBook';
import { match, order } from './OrderBook.mock';

export class OrderDetailsOrderBookPO {
  render() {
    cy.viewport(1920, 1080);
    return cy.mountWithProviders(<OrderDetailsOrderBook order={order} match={match} />);
  }

  refreshOrderBookData() {
    return cy.findByTestId(SYNC_ORDERBOOK_DATA_TEST_ID).click();
  }

  expectTextToBeVisible(text: string) {
    return cy.findByText(text);
  }
}
