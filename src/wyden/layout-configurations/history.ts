import { WIDGETS_NAMES } from '@wyden/features/widgets-renderer/widget-names';

export const historyWorkspace = {
  id: 'nest-basicRow',
  type: 'LayoutCell',
  direction: 'DIRECTION_VERTICAL',
  children: [
    {
      id: 'nest-mainRow',
      type: 'LayoutCell',
      direction: 'DIRECTION_VERTICAL',
      children: [
        {
          id: 'nest-main-workspace',
          type: 'LayoutCell',
          direction: 'DIRECTION_HORIZONTAL',
          children: [
            {
              id: 'nest-center-workspace',
              type: 'LayoutCell',
              name: 'CENTERWORKSPACE',
              direction: 'DIRECTION_HORIZONTAL',
              children: [
                {
                  id: 'nest-dashboard-workspace',
                  type: 'LayoutCell',
                  name: 'DASHBOAR<PERSON>',
                  children: [
                    {
                      id: 'nest-dashboard',
                      type: 'LayoutCell',
                      name: 'DASHBOARD_WORKSPACE',
                      direction: 'DIRECTION_VERTICAL',
                      children: [
                        {
                          id: 'nest-dashboard-workspace',
                          type: 'Layout<PERSON>ell',
                          name: 'B<PERSON><PERSON>MR<PERSON>',
                          children: [
                            {
                              id: 'nest-workspace-content',
                              type: 'LayoutCell',
                              name: '<PERSON><PERSON><PERSON><PERSON>',
                              direction: 'DIRECTION_HORIZONTAL',
                              children: [
                                {
                                  id: 'nest-south-west-column',
                                  type: 'LayoutCell',
                                  name: 'LEFTCOLUMN',
                                  children: [
                                    {
                                      id: 'history-order-history',
                                      type: 'LayoutComponent',
                                      name: WIDGETS_NAMES.ORDERS_HISTORY,
                                      displayName: 'Orders History',
                                      direction: 'DIRECTION_HORIZONTAL',
                                    },
                                  ],
                                },
                                {
                                  id: 'nest-east-column',
                                  type: 'LayoutCell',
                                  name: 'RIGHTCOLUMN',
                                  direction: 'DIRECTION_VERTICAL',
                                  children: [
                                    {
                                      id: 'history-positions',
                                      type: 'LayoutComponent',
                                      name: WIDGETS_NAMES.POSITIONS_QUERY,
                                      displayName: 'Positions',
                                    },
                                    {
                                      id: 'history-transactions',
                                      type: 'LayoutComponent',
                                      name: WIDGETS_NAMES.TRANSACTIONS_HISTORY,
                                      displayName: 'Transactions History',
                                    },
                                  ],
                                },
                              ],
                            },
                          ],
                        },
                      ],
                    },
                  ],
                },
              ],
            },
          ],
        },
      ],
    },
  ],
};
