package io.wyden.test.scenariorunner.trading.permission;

import io.qameta.allure.Epic;
import io.qameta.allure.Step;
import io.wyden.apiserver.rest.security.accessgateway.AddOrRemoveUserPermissionsRequestDto;
import io.wyden.apiserver.rest.security.model.AuthorityDto;
import io.wyden.apiserver.rest.security.model.Resource;
import io.wyden.apiserver.rest.security.model.Scope;
import io.wyden.published.client.ClientCancelRejectResponseTo;
import io.wyden.published.client.ClientOrderStatus;
import io.wyden.test.scenariorunner.config.Timeouts;
import io.wyden.test.scenariorunner.data.ErrorMsg;
import io.wyden.test.scenariorunner.data.Permission;
import io.wyden.test.scenariorunner.data.user.Manager;
import io.wyden.test.scenariorunner.extension.annotation.Fix;
import io.wyden.test.scenariorunner.extension.annotation.GraphQL;
import io.wyden.test.scenariorunner.extension.annotation.Rest;
import io.wyden.test.scenariorunner.extension.eachtest.ClientActorConnectorExtension;
import io.wyden.test.scenariorunner.extension.eachtest.ClientActorExtension;
import io.wyden.test.scenariorunner.extension.eachtest.ClientSessionExtension;
import io.wyden.test.scenariorunner.extension.eachtest.PermissionSharingExtension;
import io.wyden.test.scenariorunner.integration.ClientActor;
import io.wyden.test.scenariorunner.integration.gqlclient.GraphQLActor;
import io.wyden.test.scenariorunner.session.ClientSession;
import io.wyden.test.scenariorunner.session.ConnectorMockSession;
import io.wyden.test.scenariorunner.trading.TradingTestBase;
import io.wyden.test.scenariorunner.util.WaitUtils;
import org.assertj.core.api.InstanceOfAssertFactories;
import org.assertj.core.api.ThrowableAssert;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Order;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.RegisterExtension;
import org.springframework.web.client.RestClientException;
import org.springframework.web.reactive.function.client.WebClientResponseException;

import java.util.Set;

import static io.wyden.published.client.ClientExecType.CLIENT_EXEC_TYPE_REJECTED;
import static io.wyden.test.scenariorunner.data.infra.Epics.STREET_ORDER_MANAGEMENT;
import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatExceptionOfType;
import static org.junit.jupiter.api.Assertions.assertThrows;

@Epic(STREET_ORDER_MANAGEMENT)
public abstract class TradingPermissionTest extends TradingTestBase {

    @Order(1)
    @RegisterExtension
    ClientActorExtension nonAdminActorExtension = new ClientActorExtension(Manager.GROUP_NAME);
    @Order(2)
    @RegisterExtension
    ClientSessionExtension nonAdminClientSessionExtension = new ClientSessionExtension(
        nonAdminActorExtension.clientId(),
        connectorSessionExtension.venueAccountName(),
        portfolioExtension.portfolioId());
    @Order(3)
    @RegisterExtension
    PermissionSharingExtension permissionSharingExtension = new PermissionSharingExtension(
        clientActorExtension.clientId(),
        nonAdminActorExtension.clientId(),
        Set.of(
            new AuthorityDto(Resource.VENUE_ACCOUNT, connectorSessionExtension.venueAccountName(), Scope.TRADE),
            new AuthorityDto(Resource.PORTFOLIO, portfolioExtension.portfolioId(), Scope.TRADE)));
    @Order(4)
    @RegisterExtension
    ClientActorConnectorExtension nonAdminConnectorExtension = new ClientActorConnectorExtension(nonAdminActorExtension.clientId(), true);

    protected ClientActor nonAdmin;
    protected GraphQLActor admin;
    protected ClientSession nonAdminClientSession;

    @BeforeEach
    void setupActors() {
        nonAdmin = nonAdminActorExtension.actor();
        admin = clientActorExtension.configGqlActor();
        nonAdminClientSession = nonAdminClientSessionExtension.clientSession();
    }

    @Test
    void cancelWithoutPermissions_rejectedByAG(ConnectorMockSession conn) {

        sendOrderAndRemoveUserPermissions(conn);

        verifyCancelIsNotPermitted();
    }

    abstract void verifyCancelIsNotPermitted();

    @Step
    private void sendOrderAndRemoveUserPermissions(ConnectorMockSession conn) {
        nonAdminClientSession.sendDefaultStreetLimitOrder(REQUESTED_QTY);
        conn.acceptNewOrder();
        nonAdminClientSession.receiveExecutionReport();

        admin.permission()
            .removeUserPermissions(new AddOrRemoveUserPermissionsRequestDto(nonAdmin.getClientId(),
                Set.of(new AuthorityDto(Resource.VENUE_ACCOUNT, conn.getAccount(), Scope.TRADE))));

        // give time for permissions change to propagate
        WaitUtils.justWait(Timeouts.JUST_WAIT);
    }

    @Test
    void cancelReplaceWithoutPermissions_rejectedByAG(ConnectorMockSession conn) {

        sendOrderAndRemoveUserPermissions(conn);

        verifyCancelReplaceIsNotPermitted();
    }

    abstract void verifyCancelReplaceIsNotPermitted();

    @Epic(STREET_ORDER_MANAGEMENT)
    abstract class NoPermissionClientTradingTest {
        @RegisterExtension
        ClientActorExtension actorWithoutPermissionsExtension = new ClientActorExtension(Manager.GROUP_NAME);
        @RegisterExtension
        ClientActorConnectorExtension actorWithoutPermissionsConnectorExtension = new ClientActorConnectorExtension(
            actorWithoutPermissionsExtension.clientId(), true
        );
        ClientSession clientWithoutPermissions;

        @BeforeEach
        void setupActorWithoutPermissions() {
            clientWithoutPermissions = new ClientSession(actorWithoutPermissionsExtension.actor(), connectorSessionExtension.venueAccountName(), portfolio);
        }

        @Test
        void whenUserHasNoVenueAccountAndNoPortfolioPermissions_userCannotTradeOnVenueAccountAndPortfolio() {
            verifySendOrderResultInAccessDenied(clientWithoutPermissions);
        }
    }

    @Epic(STREET_ORDER_MANAGEMENT)
    abstract class OnlyVenueAccountPermissionClientTradingTest {
        @RegisterExtension
        @Order(0)
        ClientActorExtension actorWithOnlyVenueAccountPermissionExtension = new ClientActorExtension(Manager.GROUP_NAME);
        @RegisterExtension
        @Order(1)
        PermissionSharingExtension permissionExtension = new PermissionSharingExtension(
            clientActorExtension.clientId(),
            actorWithOnlyVenueAccountPermissionExtension.clientId(),
            Permission.venueAccountTradingPermissions(connectorSessionExtension.venueAccountName())
        );
        @RegisterExtension
        @Order(2)
        ClientActorConnectorExtension actorWithOnlyVenueAccountPermissionConnectorExtension = new ClientActorConnectorExtension(
            actorWithOnlyVenueAccountPermissionExtension.clientId(), true
        );
        ClientSession clientWithOnlyVenueAccountPermission;

        @BeforeEach
        void setupActorWithOnlyVenueAccountPermission() {
            clientWithOnlyVenueAccountPermission = new ClientSession(actorWithOnlyVenueAccountPermissionExtension.actor(), connectorSessionExtension.venueAccountName(), portfolio);
        }

        @Test
        void whenUserHasVenueAccountAndHasNoPortfolioPermissions_userCannotTradeOnPortfolio() {
            verifySendOrderResultInAccessDenied(clientWithOnlyVenueAccountPermission);
        }
    }

    void verifySendOrderResultInAccessDenied(ClientSession client) {
        RestClientException exception = assertThrows(RestClientException.class, () -> client.sendDefaultStreetLimitOrder(10000));
        assertThat(exception.getMessage())
            .contains(ErrorMsg.ACCESS_DENIED);
    }

}

@GraphQL
@Disabled("[AC-5878] Cancel reject msg doesn't come for 15s of timeout from rest-api")
class GraphQLTradingPermissionTest extends TradingPermissionTest {

    @Step
    @Override
    void verifyCancelIsNotPermitted() {
        assertThatExceptionOfType(RestClientException.class)
            .isThrownBy(nonAdminClientSession::sendCancel)
            .withMessageContaining(ErrorMsg.ACCESS_DENIED);
    }

    @Step
    @Override
    void verifyCancelReplaceIsNotPermitted() {
        assertThatExceptionOfType(RestClientException.class)
            .isThrownBy(() -> nonAdminClientSession.sendCancelReplace(5000.0))
            .withMessageContaining(ErrorMsg.ACCESS_DENIED);
    }

    @Nested
    @GraphQL
    class GraphQLNoPermissionClientTradingTest extends NoPermissionClientTradingTest {

    }

    @Nested
    @GraphQL
    class GraphQLOnlyVenueAccountPermissionClientTradingTest extends OnlyVenueAccountPermissionClientTradingTest {

    }

}

@Fix
class FixTradingPermissionTest extends TradingPermissionTest {

    @Step
    @Override
    void verifyCancelIsNotPermitted() {
        nonAdminClientSession.sendCancel();
        nonAdminClientSession.receiveCancelRejectAfterCancel();
        assertThat(nonAdminClientSession.getCancelReject().getCancelRejectResponseTo()).isEqualTo(ClientCancelRejectResponseTo.ORDER_CANCEL_REQUEST);
        verifyCancelRejectByPermission();
        nonAdmin.awaitNoMoreCancelReject();
    }

    @Step
    @Override
    void verifyCancelReplaceIsNotPermitted() {
        nonAdminClientSession.sendCancelReplace(5000.0);
        nonAdminClientSession.receiveCancelRejectAfterCancelReplace();
        assertThat(nonAdminClientSession.getCancelReject().getCancelRejectResponseTo()).isEqualTo(ClientCancelRejectResponseTo.ORDER_CANCEL_REPLACE_REQUEST);
        verifyCancelRejectByPermission();
        nonAdmin.awaitNoMoreCancelReject();
    }

    private void verifyCancelRejectByPermission() {
        assertThat(nonAdminClientSession.cancelRejectOrderStatus()).isEqualTo(ClientOrderStatus.REJECTED);
        assertThat(nonAdminClientSession.cancelRejectCxlRejectReason()).isEqualTo("Other reason.");
        assertThat(nonAdminClientSession.getCancelReject().getText()).isEqualTo(ErrorMsg.ACCESS_DENIED);
    }

    @Nested
    @Fix
    class FixNoPermissionClientTradingTest extends NoPermissionClientTradingTest {
        @Override
        @Test
        void whenUserHasNoVenueAccountAndNoPortfolioPermissions_userCannotTradeOnVenueAccountAndPortfolio() {
            verifySendOrderResultInAccessDenied(clientWithoutPermissions);
        }
    }

    @Nested
    @Fix
    class FixOnlyVenueAccountPermissionClientTradingTest extends OnlyVenueAccountPermissionClientTradingTest {
        @Override
        @Test
        void whenUserHasVenueAccountAndHasNoPortfolioPermissions_userCannotTradeOnPortfolio() {
            verifySendOrderResultInAccessDenied(clientWithOnlyVenueAccountPermission);
        }
    }

    void verifySendOrderResultInAccessDenied(ClientSession client) {
        client.sendDefaultStreetLimitOrder(REQUESTED_QTY);
        client.receiveExecutionReport();
        client.assertExecutionReportStatus(CLIENT_EXEC_TYPE_REJECTED, ClientOrderStatus.REJECTED);
        client.assertExecutionReportText(ErrorMsg.ACCESS_DENIED);
    }

}

@Rest
class RestTradingPermissionTest extends TradingPermissionTest {

    @Override
    void verifyCancelIsNotPermitted() {
        // not implemented
    }

    @Override
    void verifyCancelReplaceIsNotPermitted() {
        assertNoPermissionError(() -> nonAdminClientSession.sendCancelReplace(5000.0));
    }

    @Override
    void verifySendOrderResultInAccessDenied(ClientSession client) {
        assertNoPermissionError(() -> client.sendDefaultStreetLimitOrder(10000));
    }

    @Nested
    @Rest
    class RestNoPermissionClientTradingTest extends NoPermissionClientTradingTest {

    }

    @Nested
    @Rest
    class RestOnlyVenueAccountPermissionClientTradingTest extends OnlyVenueAccountPermissionClientTradingTest {

    }

    private void assertNoPermissionError(ThrowableAssert.ThrowingCallable callable) {
        assertThatExceptionOfType(WebClientResponseException.Forbidden.class)
            .isThrownBy(callable)
            .extracting(WebClientResponseException::getResponseBodyAsString)
            .asInstanceOf(InstanceOfAssertFactories.STRING)
            .contains(ErrorMsg.ACCESS_DENIED);
    }

}
