package io.wyden.test.scenariorunner.referencedata;

import io.qameta.allure.Epic;
import io.wyden.apiserver.rest.referencedata.instruments.model.dto.VenueTypeDto;
import io.wyden.apiserver.rest.referencedata.instruments.model.dto.create.CreateInstrumentDto;
import io.wyden.apiserver.rest.referencedata.instruments.model.dto.instrumentsquery.InstrumentResponseDto;
import io.wyden.test.scenariorunner.data.refdata.InstrumentFactory;
import io.wyden.test.scenariorunner.data.refdata.InstrumentId;
import io.wyden.test.scenariorunner.data.refdata.VenueAccountConstants;
import io.wyden.test.scenariorunner.data.user.Administrator;
import io.wyden.test.scenariorunner.data.user.Manager;
import io.wyden.test.scenariorunner.extension.alltest.ClientActorExtension;
import io.wyden.test.scenariorunner.extension.alltest.SecurityIntegratorExtension;
import io.wyden.test.scenariorunner.extension.alltest.VenueAccountExtension;
import io.wyden.test.scenariorunner.extension.annotation.Fix;
import io.wyden.test.scenariorunner.extension.annotation.GraphQL;
import io.wyden.test.scenariorunner.extension.annotation.RequiredServiceUp;
import io.wyden.test.scenariorunner.extension.infra.OemsHealthObserverExtension;
import io.wyden.test.scenariorunner.integration.ClientActor;
import io.wyden.test.scenariorunner.integration.gqlclient.GraphQLActor;
import io.wyden.test.scenariorunner.integration.service.Service;
import org.assertj.core.api.Assertions;
import org.junit.jupiter.api.BeforeAll;
import org.junit.jupiter.api.Order;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.TestInstance;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.api.extension.RegisterExtension;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.client.RestClientException;

import java.util.Collection;
import java.util.List;

import static io.wyden.test.scenariorunner.data.infra.Epics.REFERENCE_DATA;
import static org.assertj.core.api.Assertions.assertThat;

@Epic(REFERENCE_DATA)
@TestInstance(TestInstance.Lifecycle.PER_CLASS)
@ExtendWith({
    OemsHealthObserverExtension.class,
    SecurityIntegratorExtension.class
})
@RequiredServiceUp({
    Service.ACCESS_GATEWAY,
    Service.REFERENCE_DATA,
    Service.STORAGE,
    Service.CONNECTOR_WRAPPER_MOCK
})
public abstract class InstrumentSearchTest {

    private static final Logger LOGGER = LoggerFactory.getLogger(InstrumentSearchTest.class);

    private static final String MOCK_STREET_INSTRUMENT_ID = InstrumentId.ADA_USD_FOREX_WYDENMOCK.getName();

    @Order(1)
    @RegisterExtension
    ClientActorExtension accountCreatorExtension = new ClientActorExtension(Administrator.GROUP_NAME, true);
    @Order(2)
    @RegisterExtension
    ClientActorExtension nonAccountCreatorExtension = new ClientActorExtension(Manager.GROUP_NAME, false);
    @Order(3)
    @RegisterExtension
    VenueAccountExtension venueAccountCreatorExtension = new VenueAccountExtension(
        accountCreatorExtension.clientId(), List.of(VenueAccountConstants.MOCK_VENUE));

    protected ClientActor accountCreator;
    protected ClientActor nonAccountCreator;

    protected GraphQLActor accountCreatorGql;

    @BeforeAll
    protected void setupActors() {
        accountCreator = accountCreatorExtension.actor();
        nonAccountCreator = nonAccountCreatorExtension.actor();
    }

    @BeforeAll
    protected void createClientInstrument() {
        accountCreatorGql = accountCreatorExtension.configGqlActor();
        CreateInstrumentDto clientInstrument = InstrumentFactory.createClientSideForexInstrument(VenueAccountConstants.BROKER_DESK_VENUE, "BTC", "USD");
        accountCreatorGql.instrument().createAndWait(clientInstrument);
    }

    @Test
    protected void whenExternalVenueAccountCreated_accountCreatorCanRetrieveInstrumentsFromVenue() {
        Collection<InstrumentResponseDto> mockInstruments = accountCreator.getInstruments(VenueAccountConstants.MOCK_VENUE);

        assertThat(mockInstruments).isNotEmpty();

        verifyOnlyMockInstrumentsArePresent(mockInstruments);

        assertThat(mockInstruments)
            .as("Street WydenMock instrument with instrumentId [%s] is not present".formatted(MOCK_STREET_INSTRUMENT_ID))
            .anyMatch(instrument -> instrument.instrumentIdentifiers().instrumentId().equals(MOCK_STREET_INSTRUMENT_ID));
    }

    protected void verifyOnlyMockInstrumentsArePresent(Collection<InstrumentResponseDto> instruments) {
        assertThat(instruments)
            .allMatch(instrument -> instrument.baseInstrument().venueName().equals(VenueAccountConstants.MOCK_VENUE));
    }

    @Test
    protected void aggregatedInstrumentInstrumentIdIsGenerated() {
        Collection<InstrumentResponseDto> instruments = accountCreator.getInstruments(VenueAccountConstants.MOCK_VENUE);

        assertThat(instruments)
                .anyMatch(instrument -> instrument.instrumentIdentifiers().instrumentId().equals("ADAUSD@FOREX@WydenMock"));
    }

}

@GraphQL
class GraphQLInstrumentSearchTest extends InstrumentSearchTest {

    @Override
    @Test
    protected void whenExternalVenueAccountCreated_accountCreatorCanRetrieveInstrumentsFromVenue() {
        super.whenExternalVenueAccountCreated_accountCreatorCanRetrieveInstrumentsFromVenue();
    }

    @Override
    @Test
    protected void aggregatedInstrumentInstrumentIdIsGenerated() {
        super.aggregatedInstrumentInstrumentIdIsGenerated();
    }

    @Test
    protected void whenExternalVenueAccountCreated_nonCreatorCannotRetrieveInstrumentsFromVenue() {
        Collection<InstrumentResponseDto> streetInstruments = nonAccountCreator.getInstruments()
            .stream().filter(i -> VenueTypeDto.STREET.equals(i.baseInstrument().venueType()))
            .toList();

        assertThat(streetInstruments)
            .as("Non account creator shouldn't have accessible instruments with venueType=STREET")
            .isEmpty();

        Assertions.assertThatExceptionOfType(RestClientException.class)
            .isThrownBy(() -> nonAccountCreator.getInstruments(VenueAccountConstants.BITMEX_VENUE))
            .withMessageContaining("User has no access to one of passed venue names");
    }

    @Test
    protected void clientInstrumentsReturnedForAllUsers() {
        Collection<InstrumentResponseDto> allInstruments = nonAccountCreator.getInstruments();

        assertThat(allInstruments)
            .filteredOn(i -> VenueTypeDto.CLIENT.equals(i.baseInstrument().venueType()))
            .as("Non account creator should have instruments with venueType=CLIENT")
            .isNotEmpty();
    }

}

/**
 * At the test creation moment:
 * - FIX API doesn't support filtering instruments by venue
 * - FIX API doesn't support restrictions based on user permissions
 */
@Fix
class FixInstrumentSearchTest extends InstrumentSearchTest {

    @Override
    protected void verifyOnlyMockInstrumentsArePresent(Collection<InstrumentResponseDto> instruments) {
        //FIX API doesn't support filtering instruments by venue
    }

    @Override
    @Test
    protected void whenExternalVenueAccountCreated_accountCreatorCanRetrieveInstrumentsFromVenue() {
        super.whenExternalVenueAccountCreated_accountCreatorCanRetrieveInstrumentsFromVenue();
    }

    @Override
    @Test
    protected void aggregatedInstrumentInstrumentIdIsGenerated() {
        super.aggregatedInstrumentInstrumentIdIsGenerated();
    }

}
