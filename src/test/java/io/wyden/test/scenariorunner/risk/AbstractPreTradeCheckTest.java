package io.wyden.test.scenariorunner.risk;

import io.qameta.allure.Epic;
import io.wyden.test.scenariorunner.extension.annotation.RequiredServiceUp;
import io.wyden.test.scenariorunner.integration.keycloak.KeycloakClient;
import io.wyden.test.scenariorunner.integration.restclient.RestActor;
import io.wyden.test.scenariorunner.integration.service.Service;
import io.wyden.test.scenariorunner.trading.TradingTestBase;
import org.junit.jupiter.api.AfterAll;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeAll;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.parallel.Isolated;

import java.util.HashSet;
import java.util.Set;

import static io.wyden.test.scenariorunner.data.infra.Epics.RISK_MANAGEMENT;
import static io.wyden.test.scenariorunner.data.infra.TestTags.RISK;
import static io.wyden.test.scenariorunner.tool.TestGarbageRemover.E2E_TEST_PREFIX;

/**
 * Risk engine rejects street orders right after placing so order goes PENDING_NEW -> REJECTED.<br>
 * To verify that order was not rejected by PTC: enough to wait until order reaches status NEW (PENDING_NEW -> NEW).<br>
 * NOTE:<br>
 * Consider using isolated portfolios, instruments for parallel runs of PTC tests. <br>
 * <br>
 * There is an issue currently that there are PTC parameterized tests to which we cannot inject portfolio from non-static extension.<br>
 * So currently all PTC tests are @Isolated to be executed in separate thread sequentially.
 */
@Epic(RISK_MANAGEMENT)
@Tag(RISK)
@RequiredServiceUp({
    Service.ACCESS_GATEWAY,
    Service.ORDER_COLLIDER,
    Service.ORDER_GATEWAY,
    Service.REFERENCE_DATA,
    Service.CONNECTOR_WRAPPER_MOCK,
    Service.RISK_ENGINE,
    Service.STORAGE
})
@Isolated
public abstract class AbstractPreTradeCheckTest extends TradingTestBase {

    protected static final String INSTRUMENT_REASON = "Instrument not allowed";
    protected static final String ORDER_TYPE_REASON = "Order type not allowed";
    protected static final String MAX_ORDER_SIZE_REASON = "Order exceeds maximum order quantity";

    protected static RestActor restActor;

    protected Set<String> ptcIds = new HashSet<>();

    @BeforeAll
    protected static void setupRestActor() {
        restActor = new RestActor(new KeycloakClient());
    }

    @AfterEach
    protected void removePtc() {
        ptcIds.forEach(restActor::deletePreTradeCheck);
    }

    @AfterAll
    protected static void closeRestActor() {
        restActor.getPreTradeChecks()
            .stream()
            .filter(ptc -> ptc.id().startsWith(E2E_TEST_PREFIX))
            .forEach(ptc -> restActor.deletePreTradeCheck(ptc.id()));
        restActor.close();
    }

}
