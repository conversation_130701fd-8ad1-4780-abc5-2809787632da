package io.wyden.test.scenariorunner.brokerdesk.permissions;

import io.qameta.allure.Step;
import io.wyden.apiserver.rest.brokerdesk.config.BrokerDeskConfigModel;
import io.wyden.apiserver.rest.brokerdesk.config.BrokerDeskConfigModel.ExecutionConfiguration;
import io.wyden.apiserver.rest.brokerdesk.config.BrokerDeskConfigModel.ExecutionConfigurationInput;
import io.wyden.apiserver.rest.brokerdesk.config.BrokerDeskConfigModel.InstrumentConfigurationInput;
import io.wyden.apiserver.rest.brokerdesk.config.BrokerDeskConfigModel.InstrumentPricingConfigurationInput;
import io.wyden.apiserver.rest.brokerdesk.config.BrokerDeskConfigModel.PricingConfiguration;
import io.wyden.apiserver.rest.referencedata.instruments.model.dto.instrumentsquery.InstrumentResponseDto;
import io.wyden.apiserver.rest.venueaccount.model.VenueAccountDesc;
import io.wyden.test.scenariorunner.assertion.ResponseAssert;
import io.wyden.test.scenariorunner.assertion.brokerdesk.ExecutionConfigurationSoftAssert;
import org.junit.jupiter.api.Test;

import java.util.Collection;
import java.util.List;
import java.util.NoSuchElementException;

import static org.assertj.core.api.Assertions.assertThat;

public class PortfolioGroupBrokerDeskPermissionsTest extends BrokerDeskPermissionsTestBase {

    @Test
    void whenPortfolioPermissionIsManageAndBrokerConfigStaticIsManage_thenUpdatingPortfolioGroupExecutionAndPricingConfigAndResettingIsAvailable() {

        //when portfolio permission is manage and static permissions for config is manage
        setUserToVostroPortfolioManageAndBrokerManage();
        //then the user tries to update execution, pricing and instrument configuration for portfolio group
        ExecutionConfigurationInput executionConfigurationInput = getExecutionConfigurationInput(nostroPortfolio.portfolioId());

        InstrumentPricingConfigurationInput instrumentPricingConfigurationInput = getInstrumentPricingConfigurationInput();

        InstrumentConfigurationInput instrumentConfigurationInput = new InstrumentConfigurationInput(instrument.getClientInstrumentId(), false, executionConfigurationInput, instrumentPricingConfigurationInput);

        userActor.brokerDesk().updatePortfolioGroupInstrumentConfiguration(portfolioGroup.getPortfolioGroupId(), instrumentConfigurationInput);

        userActor.brokerDesk().updatePortfolioGroupExecutionConfiguration(
            portfolioGroup.getPortfolioGroupId(), executionConfigurationInput);

        userActor.brokerDesk().updatePortfolioGroupPricingConfiguration(
            portfolioGroup.getPortfolioGroupId(), new BrokerDeskConfigModel.PricingConfigurationInput(List.of(VENUE_ACCOUNT_NAME), MARK_UP_VALUE));

        ExecutionConfiguration groupExecutionConfiguration = userActor
            .brokerDesk()
            .getPortfolioGroupConfig(portfolioGroup.getPortfolioGroupId())
            .executionConfiguration();

        PricingConfiguration groupPricingConfiguration = userActor
            .brokerDesk().getPortfolioGroupConfig(portfolioGroup.getPortfolioGroupId())
            .pricingConfiguration();

        //then the user is able to change execution, pricing and instrument conifugrations for portfolio group

        ExecutionConfigurationSoftAssert.assertThat(groupExecutionConfiguration)
            .executionConfigurationIs(executionConfigurationInput)
            .assertAll();

        assertThat(groupPricingConfiguration.markup()).isEqualTo(MARK_UP_VALUE);
        assertThat(groupPricingConfiguration.venueAccounts().stream().map(VenueAccountDesc::name).toList())
            .contains(VENUE_ACCOUNT_NAME);

        verifyPortfolioGroupInstrumentPricingAndExecConfigurationAreLike(executionConfigurationInput);

        //then the user is resetting the execution, pricing configurations and instrument configuration
        //then the execution, pricing configuration and instrument configuration of the portfolio group are nulled
        verifyPortfolioGroupResetExecAndPricingConfigurationsAreAvailable();
        verifyGroupInstrumentResetExecAndPricingConfigurationsAreAvailable();
    }

    @Test
    void whenPortfolioPermissionIsReadAndBrokerConfigStaticIsManage_thenUpdatingPortfolioGroupExecutionAndPricingConfigAndResettingIsAvailable() {
        //when portfolio permission is read and static permissions for config is manage
        setUserToVostroPortfolioReadAndBrokerManage();
        ExecutionConfigurationInput executionConfigurationInput = getExecutionConfigurationInput(nostroPortfolio.portfolioId());
        //then the user tries to update execution, pricing and instrument configuration for portfolioGroup
        ExecutionConfigurationInput groupExecutionConfigurationInput = getExecutionConfigurationInput(vostroPortfolio.portfolioId());

        InstrumentPricingConfigurationInput instrumentPricingConfigurationInput = getInstrumentPricingConfigurationInput();

        InstrumentConfigurationInput instrumentConfigurationInput = new InstrumentConfigurationInput(instrument.getClientInstrumentId(), false, executionConfigurationInput, instrumentPricingConfigurationInput);

        userActor.brokerDesk().updatePortfolioGroupInstrumentConfiguration(portfolioGroup.getPortfolioGroupId(), instrumentConfigurationInput);

        userActor.brokerDesk().updatePortfolioGroupPricingConfiguration(
            portfolioGroup.getPortfolioGroupId(), new BrokerDeskConfigModel.PricingConfigurationInput(List.of(VENUE_ACCOUNT_NAME), MARK_UP_VALUE));

        PricingConfiguration groupPricingConfiguration = userActor
            .brokerDesk().getPortfolioGroupConfig(portfolioGroup.getPortfolioGroupId())
            .pricingConfiguration();

        //Cannot change - No access to portfolio, correct behavior
        ResponseAssert.assertThrowsRestClientExceptionWithAccessDeniedMsg(
            () -> userActor.brokerDesk().updatePortfolioGroupExecutionConfiguration(
                portfolioGroup.getPortfolioGroupId(), groupExecutionConfigurationInput));
        //then the user is able to change pricing and instrument conifugrations for portfolio group

        assertThat(groupPricingConfiguration.markup()).isEqualTo(MARK_UP_VALUE);
        assertThat(groupPricingConfiguration.venueAccounts().stream().map(VenueAccountDesc::name).toList())
            .contains(VENUE_ACCOUNT_NAME);

        verifyPortfolioGroupInstrumentPricingAndExecConfigurationAreLike(executionConfigurationInput);
        //then the user is resetting the execution, pricing configurations and instrument configuration
        //then the execution, pricing configuration and instrument configuration of the portfolio group are nulled
        verifyPortfolioGroupResetExecAndPricingConfigurationsAreAvailable();
        verifyGroupInstrumentResetExecAndPricingConfigurationsAreAvailable();
    }

    @Test
    void whenPortfolioPermissionIsManageAndBrokerConfigStaticIsRead_thenPortfolioGroupConfigurationsAreNotChangeable() {
        //when portfolio permission is manage and static permissions for config is read
        setUserToVostroPortfolioManageAndBrokerRead();

        //then the user tries to update execution, pricing and instrument configuration for portfolio portfolioGroup
        ExecutionConfigurationInput executionConfigurationInput = getExecutionConfigurationInput(nostroPortfolio.portfolioId());
        ExecutionConfigurationInput groupExecutionConfigurationInput = getExecutionConfigurationInput(vostroPortfolio.portfolioId());

        InstrumentPricingConfigurationInput instrumentPricingConfigurationInput = getInstrumentPricingConfigurationInput();
        InstrumentConfigurationInput instrumentConfigurationInput = new InstrumentConfigurationInput(instrument.getClientInstrumentId(), false, executionConfigurationInput, instrumentPricingConfigurationInput);

        //then user is NOT able to modify portfolio group
        ResponseAssert.assertThrowsRestClientExceptionWithAccessDeniedMsg(
            () -> userActor.brokerDesk().updatePortfolioGroupExecutionConfiguration(
                portfolioGroup.getPortfolioGroupId(), groupExecutionConfigurationInput));
        ResponseAssert.assertThrowsRestClientExceptionWithAccessDeniedMsg(
            () -> userActor.brokerDesk().updatePortfolioGroupInstrumentConfiguration(portfolioGroup.getPortfolioGroupId(), instrumentConfigurationInput));
        ResponseAssert.assertThrowsRestClientExceptionWithAccessDeniedMsg(
            () -> userActor.brokerDesk().updatePortfolioGroupPricingConfiguration(
                portfolioGroup.getPortfolioGroupId(), new BrokerDeskConfigModel.PricingConfigurationInput(List.of(VENUE_ACCOUNT_NAME), MARK_UP_VALUE)));
        //then the user is resetting the execution, pricing configurations and instrument configuration
        //then the user has no access to reset
        verifyGroupInstrumentResetExecAndPricingConfigurationsAreNotAvailable();
        verifyPortfolioGroupResetExecAndPricingConfigurationsAreNotAvailable();
    }

    @Test
    void whenPortfolioPermissionIsReadAndBrokerConfigStaticIsRead_thenAllPortfolioGroupConfigurationsAreNotChangeable() {

        //when portfolio permission is read and static permissions for config is read
        setUserToVostroPortfolioReadAndBrokerRead();

        //then the user tries to update execution, pricing and instrument configuration for portfolio group
        ExecutionConfigurationInput executionConfigurationInput = getExecutionConfigurationInput(nostroPortfolio.portfolioId());
        ExecutionConfigurationInput groupExecutionConfigurationInput = getExecutionConfigurationInput(vostroPortfolio.portfolioId());

        InstrumentPricingConfigurationInput instrumentPricingConfigurationInput = getInstrumentPricingConfigurationInput();
        InstrumentConfigurationInput instrumentConfigurationInput = new InstrumentConfigurationInput(instrument.getClientInstrumentId(), false, executionConfigurationInput, instrumentPricingConfigurationInput);

        //then user is NOT able to modify portfolio group
        ResponseAssert.assertThrowsRestClientExceptionWithAccessDeniedMsg(
            () -> userActor.brokerDesk().updatePortfolioGroupExecutionConfiguration(
                portfolioGroup.getPortfolioGroupId(), groupExecutionConfigurationInput));
        ResponseAssert.assertThrowsRestClientExceptionWithAccessDeniedMsg(
            () -> userActor.brokerDesk().updatePortfolioGroupInstrumentConfiguration(portfolioGroup.getPortfolioGroupId(), instrumentConfigurationInput));
        ResponseAssert.assertThrowsRestClientExceptionWithAccessDeniedMsg(
            () -> userActor.brokerDesk().updatePortfolioGroupPricingConfiguration(
                portfolioGroup.getPortfolioGroupId(), new BrokerDeskConfigModel.PricingConfigurationInput(List.of(VENUE_ACCOUNT_NAME), MARK_UP_VALUE)));

        //then the user is resetting the execution, pricing configurations and instrument configuration
        //then the user has no access to reset
        verifyGroupInstrumentResetExecAndPricingConfigurationsAreNotAvailable();
        verifyPortfolioGroupResetExecAndPricingConfigurationsAreNotAvailable();
    }

    @Test
    void whenPortfolioPermissionIsNoneAndBrokerConfigStaticIsManage_thenAllPortfolioGroupConfigurationsAreAvailable() {
        //when portfolio permission are none and static permissions for config is manage
        setUserToNoVostroPortfolioPermissionsAndBrokerManage();

        //then the user tries to update execution, pricing and instrument configuration for  portfolioGroup
        ExecutionConfigurationInput executionConfigurationInput = getExecutionConfigurationInput(nostroPortfolio.portfolioId());

        ExecutionConfigurationInput groupExecutionConfigurationInput = getExecutionConfigurationInput(vostroPortfolio.portfolioId());

        InstrumentPricingConfigurationInput instrumentPricingConfigurationInput = getInstrumentPricingConfigurationInput();

        InstrumentConfigurationInput instrumentConfigurationInput = new InstrumentConfigurationInput(instrument.getClientInstrumentId(), false, executionConfigurationInput, instrumentPricingConfigurationInput);

        userActor.brokerDesk().updatePortfolioGroupInstrumentConfiguration(portfolioGroup.getPortfolioGroupId(), instrumentConfigurationInput);

        userActor.brokerDesk().updatePortfolioGroupPricingConfiguration(
            portfolioGroup.getPortfolioGroupId(), new BrokerDeskConfigModel.PricingConfigurationInput(List.of(VENUE_ACCOUNT_NAME), MARK_UP_VALUE));

        PricingConfiguration groupPricingConfiguration = userActor
            .brokerDesk().getPortfolioGroupConfig(portfolioGroup.getPortfolioGroupId())
            .pricingConfiguration();


        //Cannot change - No access to portfolio, correct behavior
        ResponseAssert.assertThrowsRestClientExceptionWithAccessDeniedMsg(
            () -> userActor.brokerDesk().updatePortfolioGroupExecutionConfiguration(
                portfolioGroup.getPortfolioGroupId(), groupExecutionConfigurationInput));

        //then the user is able to change pricing and instrument conifugrations for portfolio group
        assertThat(groupPricingConfiguration.markup()).isEqualTo(MARK_UP_VALUE);
        assertThat(groupPricingConfiguration.venueAccounts().stream().map(VenueAccountDesc::name).toList())
            .contains(VENUE_ACCOUNT_NAME);

        verifyPortfolioGroupInstrumentPricingAndExecConfigurationAreLike(executionConfigurationInput);
        //then the user is resetting the execution, pricing configurations and instrument configuration
        //then the execution, pricing configuration and instrument configuration of the portfolio group are nulled
        verifyPortfolioGroupResetExecAndPricingConfigurationsAreAvailable();
        verifyGroupInstrumentResetExecAndPricingConfigurationsAreAvailable();
    }

    @Test
    void whenPortfolioPermissionIsNoneAndBrokerConfigStaticIsRead_thenAllPortfolioGroupConfigurationsAreNotAvailable() {

        //when portfolio permission are none and static permissions for config is read
        setUserToNoVostroPortfolioPermissionsAndBrokerRead();

        //then the user tries to update execution, pricing and instrument configuration for portfolio group
        ExecutionConfigurationInput groupExecutionConfigurationInput = getExecutionConfigurationInput(vostroPortfolio.portfolioId());
        InstrumentPricingConfigurationInput instrumentPricingConfigurationInput = getInstrumentPricingConfigurationInput();
        InstrumentConfigurationInput instrumentConfigurationInput = new InstrumentConfigurationInput(instrument.getClientInstrumentId(), false, groupExecutionConfigurationInput, instrumentPricingConfigurationInput);

        //then user is NOT able to modify portfolio group
        ResponseAssert.assertThrowsRestClientExceptionWithAccessDeniedMsg(
            () -> userActor.brokerDesk().updatePortfolioGroupExecutionConfiguration(
                portfolioGroup.getPortfolioGroupId(), groupExecutionConfigurationInput));
        ResponseAssert.assertThrowsRestClientExceptionWithAccessDeniedMsg(
            () -> userActor.brokerDesk().updatePortfolioGroupInstrumentConfiguration(portfolioGroup.getPortfolioGroupId(), instrumentConfigurationInput));
        ResponseAssert.assertThrowsRestClientExceptionWithAccessDeniedMsg(
            () -> userActor.brokerDesk().updatePortfolioGroupPricingConfiguration(
                portfolioGroup.getPortfolioGroupId(), new BrokerDeskConfigModel.PricingConfigurationInput(List.of(VENUE_ACCOUNT_NAME), MARK_UP_VALUE)));

        //then the user is resetting the execution, pricing configurations and instrument configuration
        //then the user has no access to reset
        verifyGroupInstrumentResetExecAndPricingConfigurationsAreNotAvailable();
        verifyPortfolioGroupResetExecAndPricingConfigurationsAreNotAvailable();
    }

    @Test
    void whenPortfolioPermissionIsNoneAndBrokerConfigStaticIsNone_thenAllPortfolioGroupConfigurationsAreNotAvailable() {
        //when user has no permissions at all
        //then the user is NOT able to read execution, pricing and instrument configurations of portfolio group
        ResponseAssert.assertThrowsRestClientExceptionWithAccessDeniedMsg(
            () -> userActor
                .brokerDesk()
                .getPortfolioGroupConfig(portfolioGroup.getPortfolioGroupId()));
        //then the user is resetting the execution, pricing configurations and instrument configuration
        //then the user has no access to reset
        verifyGroupInstrumentResetExecAndPricingConfigurationsAreNotAvailable();
        verifyPortfolioGroupResetExecAndPricingConfigurationsAreNotAvailable();
    }

    @Step
    protected void verifyPortfolioGroupResetExecAndPricingConfigurationsAreNotAvailable() {
        BrokerDeskConfigModel.ResetConfigurationInput resetExecutionConfigurationInput = getResetConfigurationInput(BrokerDeskConfigModel.ConfigurationType.EXECUTION, BrokerDeskConfigModel.ConfigurationLevel.PORTFOLIO_GROUP, portfolioGroup.getPortfolioGroupId());
        BrokerDeskConfigModel.ResetConfigurationInput resetPricingConfigurationInput = getResetConfigurationInput(BrokerDeskConfigModel.ConfigurationType.PRICING, BrokerDeskConfigModel.ConfigurationLevel.PORTFOLIO_GROUP, portfolioGroup.getPortfolioGroupId());

        ResponseAssert.assertThrowsRestClientExceptionWithAccessDeniedMsg(
            () -> userActor.brokerDesk().resetConfiguration(resetExecutionConfigurationInput));
        ResponseAssert.assertThrowsRestClientExceptionWithAccessDeniedMsg(
            () -> userActor.brokerDesk().resetConfiguration(resetPricingConfigurationInput));
    }

    @Step
    protected void verifyPortfolioGroupResetExecAndPricingConfigurationsAreAvailable() {
        ExecutionConfiguration executionConfiguration;
        PricingConfiguration pricingConfiguration;
        BrokerDeskConfigModel.ResetConfigurationInput resetExecutionConfigurationInput = getResetConfigurationInput(BrokerDeskConfigModel.ConfigurationType.EXECUTION, BrokerDeskConfigModel.ConfigurationLevel.PORTFOLIO_GROUP, portfolioGroup.getPortfolioGroupId());
        BrokerDeskConfigModel.ResetConfigurationInput resetPricingConfigurationInput = getResetConfigurationInput(BrokerDeskConfigModel.ConfigurationType.PRICING, BrokerDeskConfigModel.ConfigurationLevel.PORTFOLIO_GROUP, portfolioGroup.getPortfolioGroupId());

        userActor.brokerDesk().resetConfiguration(resetExecutionConfigurationInput);
        userActor.brokerDesk().resetConfiguration(resetPricingConfigurationInput);

        executionConfiguration = userActor
            .brokerDesk()
            .getPortfolioGroupConfig(portfolioGroup.getPortfolioGroupId())
            .executionConfiguration();
        pricingConfiguration = userActor
            .brokerDesk()
            .getPortfolioGroupConfig(portfolioGroup.getPortfolioGroupId())
            .pricingConfiguration();

        assertThat(executionConfiguration).isNull();
        assertThat(pricingConfiguration).isNull();
    }

    @Step
    protected void verifyGroupInstrumentResetExecAndPricingConfigurationsAreAvailable() {
        BrokerDeskConfigModel.ResetConfigurationInput resetExecutionConfigurationInput = getResetConfigurationInput(BrokerDeskConfigModel.ConfigurationType.EXECUTION, BrokerDeskConfigModel.ConfigurationLevel.PORTFOLIO_GROUP_INSTRUMENT, portfolioGroup.getPortfolioGroupId());
        BrokerDeskConfigModel.ResetConfigurationInput resetPricingConfigurationInput = getResetConfigurationInput(BrokerDeskConfigModel.ConfigurationType.PRICING, BrokerDeskConfigModel.ConfigurationLevel.PORTFOLIO_GROUP_INSTRUMENT, portfolioGroup.getPortfolioGroupId());

        userActor.brokerDesk().resetConfiguration(resetExecutionConfigurationInput);
        userActor.brokerDesk().resetConfiguration(resetPricingConfigurationInput);

        BrokerDeskConfigModel.InstrumentGroupConfiguration instrumentGroupConfiguration = getPortfolioGroupInstrumentConfiguration(instrument.getClientInstrumentId());

        assertThat(instrumentGroupConfiguration.executionConfiguration()).isNull();
        assertThat(instrumentGroupConfiguration.pricingConfiguration()).isNull();
    }

    @Step
    protected void verifyGroupInstrumentResetExecAndPricingConfigurationsAreNotAvailable() {
        BrokerDeskConfigModel.ResetConfigurationInput resetExecutionConfigurationInput = getResetConfigurationInput(BrokerDeskConfigModel.ConfigurationType.EXECUTION, BrokerDeskConfigModel.ConfigurationLevel.PORTFOLIO_GROUP_INSTRUMENT, portfolioGroup.getPortfolioGroupId());
        BrokerDeskConfigModel.ResetConfigurationInput resetPricingConfigurationInput = getResetConfigurationInput(BrokerDeskConfigModel.ConfigurationType.PRICING, BrokerDeskConfigModel.ConfigurationLevel.PORTFOLIO_GROUP_INSTRUMENT, portfolioGroup.getPortfolioGroupId());

        ResponseAssert.assertThrowsRestClientExceptionWithAccessDeniedMsg(
            () -> userActor.brokerDesk().resetConfiguration(resetExecutionConfigurationInput));
        ResponseAssert.assertThrowsRestClientExceptionWithAccessDeniedMsg(
            () -> userActor.brokerDesk().resetConfiguration(resetPricingConfigurationInput));
    }

    @Step
    protected void verifyPortfolioGroupInstrumentPricingAndExecConfigurationAreLike(ExecutionConfigurationInput executionConfigurationInput) {
        BrokerDeskConfigModel.InstrumentGroupConfiguration instrumentConfiguration = getPortfolioGroupInstrumentConfiguration(instrument.getClientInstrumentId());
        InstrumentResponseDto instrumentResponseDto = getInstrumentByInstrumentId(userActor.getInstruments(), STREET_INSTRUMENT);
        BrokerDeskConfigModel.InstrumentKey instrumentKey = new BrokerDeskConfigModel.InstrumentKey(STREET_VENUE_ACCOUNT_NAME, STREET_VENUE_ACCOUNT_NAME, instrumentResponseDto);

        ExecutionConfigurationSoftAssert.assertThat(instrumentConfiguration.executionConfiguration())
            .executionConfigurationIs(executionConfigurationInput)
            .assertAll();

        assertThat(instrumentConfiguration.pricingConfiguration().markup()).isEqualTo(MARK_UP_VALUE);
        InstrumentResponseDto expectedInstrument = instrumentConfiguration.pricingConfiguration().pricingSource().stream()
            .filter(i -> i.instrument().baseInstrument().equals(instrumentKey.instrument().baseInstrument()))
            .findFirst()
            .orElseThrow(() -> new NoSuchElementException("Instrument " + instrumentKey.instrument().baseInstrument().symbol() + " is not present in pricing config"))
            .instrument();
        assertThat(expectedInstrument.instrumentIdentifiers()).isEqualTo(instrumentKey.instrument().instrumentIdentifiers());
        assertThat(expectedInstrument.forexSpotProperties()).isEqualTo(instrumentKey.instrument().forexSpotProperties());
        assertThat(expectedInstrument.tradingConstraints()).isEqualTo(instrumentKey.instrument().tradingConstraints());
    }

    @Step
    protected BrokerDeskConfigModel.InstrumentGroupConfiguration getPortfolioGroupInstrumentConfiguration(String instrumentId) {
        Collection<BrokerDeskConfigModel.InstrumentGroupConfiguration> instrumentConfigurations = userActor
            .brokerDesk()
            .getPortfolioGroupConfig(portfolioGroup.getPortfolioGroupId()).instrumentConfiguration();

        for (BrokerDeskConfigModel.InstrumentGroupConfiguration iConfig : instrumentConfigurations) {
            if (iConfig.instrumentId().equals(instrumentId)) {
                return iConfig;
            }
        }
        throw new NoSuchElementException("Instrument Configuration with id " + instrumentId + " wasn't found!");
    }

}
