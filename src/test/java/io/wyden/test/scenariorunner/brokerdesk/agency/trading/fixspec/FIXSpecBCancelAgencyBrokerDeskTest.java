package io.wyden.test.scenariorunner.brokerdesk.agency.trading.fixspec;

import io.wyden.apiserver.rest.referencedata.instruments.model.dto.instrumentsquery.InstrumentResponseDto;
import io.wyden.published.client.ClientExecType;
import io.wyden.published.client.ClientOrderStatus;
import io.wyden.published.client.ClientOrderType;
import io.wyden.test.scenariorunner.brokerdesk.agency.AgencyTradingTestBase;
import io.wyden.test.scenariorunner.data.ErrorMsg;
import io.wyden.test.scenariorunner.data.source.SimpleClientOrderTypesSource;
import io.wyden.test.scenariorunner.data.trading.SimpleOrderFactory;
import io.wyden.test.scenariorunner.extension.annotation.Fix;
import io.wyden.test.scenariorunner.extension.annotation.Rest;
import io.wyden.test.scenariorunner.session.ClientSession;
import io.wyden.test.scenariorunner.session.ConnectorMockSession;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.params.ParameterizedTest;

import static org.assertj.core.api.Assertions.assertThat;

//TODO extend with GraphQL: https://algotrader.atlassian.net/browse/AC-5653
public abstract class FIXSpecBCancelAgencyBrokerDeskTest extends AgencyTradingTestBase {

    //TODO: check fees when AC-2632 will be implemented

    @Test
    void B1a_cancelRequestIssuedForAZeroFilledOrder_rejected(ConnectorMockSession conn,
                                                             InstrumentResponseDto clientInstrument, ClientSession client) {
        // when
        client.sendOrder(clientOrderFactory.defaultOrder(ClientOrderType.STOP, clientInstrument.instrumentIdentifiers().instrumentId()));
        conn.acceptNewOrder();

        // then
        client.receiveExecutionReport();
        client.assertExecutionReportStatus(ClientExecType.CLIENT_EXEC_TYPE_NEW, ClientOrderStatus.NEW);

        // when
        client.sendCancel();

        // then
        client.receiveExecutionReport();
        client.assertExecutionReportStatus(ClientExecType.CLIENT_EXEC_TYPE_PENDING_CANCEL, ClientOrderStatus.PENDING_CANCEL);
        double orderQuantity = Double.parseDouble(client.getOrder().getQuantity());
        client.assertExecutionReportQuantities(orderQuantity, 0.0, orderQuantity, 0.0);
        client.assertExecutionReportSide(client.getOrder().getSide());
        assertThat(client.getExecutionReport().getOrderId()).isNotBlank();
        assertThat(client.getExecutionReport().getInstrumentId()).isEqualTo(clientInstrument.instrumentIdentifiers().instrumentId());
        assertThat(Double.parseDouble(client.getExecutionReport().getAvgPrice())).isEqualTo(0);

        // when
        conn.rejectCancel(ErrorMsg.BROKER_OPTION);

        // then
        client.receiveCancelRejectAfterCancel();
        client.assertCancelRejectOrderOriginalClientId();
        assertThat(client.getCancelReject().getClOrderId()).isEqualTo(client.getCancelClientOrderId());
        assertThat(client.getCancelReject().getOrderStatus()).isEqualTo(ClientOrderStatus.NEW);
    }

    @ParameterizedTest
    @SimpleClientOrderTypesSource
    void B1a_cancelRequestIssuedForAZeroFilledOrder_cancelled(ClientOrderType orderType, ConnectorMockSession conn,
                                                              InstrumentResponseDto clientInstrument, ClientSession client) {
        // when
        client.sendOrder(clientOrderFactory.defaultOrder(orderType, clientInstrument.instrumentIdentifiers().instrumentId()));
        conn.acceptNewOrder();

        // then
        client.receiveExecutionReport();
        client.assertExecutionReportStatus(ClientExecType.CLIENT_EXEC_TYPE_NEW, ClientOrderStatus.NEW);

        // when
        client.sendCancel();

        // then
        client.receiveExecutionReport();
        client.assertExecutionReportStatus(ClientExecType.CLIENT_EXEC_TYPE_PENDING_CANCEL, ClientOrderStatus.PENDING_CANCEL);

        // when
        conn.acceptCancel(0);

        // then
        client.receiveExecutionReport();
        client.assertExecutionReportStatus(ClientExecType.CLIENT_EXEC_TYPE_CANCELED, ClientOrderStatus.CANCELED);
        double orderQuantity = Double.parseDouble(client.getOrder().getQuantity());
        client.assertExecutionReportQuantities(orderQuantity, 0.0, 0.0, 0.0);
        client.assertExecutionReportSide(client.getOrder().getSide());
        assertThat(client.getExecutionReport().getOrderId()).isNotBlank();
        assertThat(client.getExecutionReport().getInstrumentId()).isEqualTo(clientInstrument.instrumentIdentifiers().instrumentId());
        assertThat(Double.parseDouble(client.getExecutionReport().getAvgPrice())).isEqualTo(0);
        assertThat(client.getExecutionReport().getClOrderId()).isEqualTo(client.getCancelClientOrderId());
        assertThat(client.getExecutionReport().getOrigClOrderId()).isEqualTo(client.getOriginalClientOrderId());
    }

    @Test
    void B1b_cancelRequestIssuedForAPartFilledOrderPartialExecutionOccurWhileCancelRequestIsActive(ConnectorMockSession conn,
                                                                                                   InstrumentResponseDto clientInstrument,
                                                                                                   ClientSession client) {
        // when
        client.sendOrder(clientOrderFactory.defaultOrder(ClientOrderType.STOP_LIMIT, clientInstrument.instrumentIdentifiers().instrumentId()));
        conn.acceptNewOrder();

        // then
        client.receiveExecutionReport();
        client.assertExecutionReportStatus(ClientExecType.CLIENT_EXEC_TYPE_NEW, ClientOrderStatus.NEW);

        // when
        client.sendCancel();

        // then
        client.receiveExecutionReport();
        client.assertExecutionReportStatus(ClientExecType.CLIENT_EXEC_TYPE_PENDING_CANCEL, ClientOrderStatus.PENDING_CANCEL);

        // when
        double orderQuantity = Double.parseDouble(client.getOrder().getQuantity());
        conn.fillPart(orderQuantity / 4, Double.parseDouble(SimpleOrderFactory.DEFAULT_LIMIT_PRICE));

        // then
        client.receiveExecutionReport();
        client.assertExecutionReportStatus(ClientExecType.CLIENT_EXEC_TYPE_PARTIAL_FILL, ClientOrderStatus.PENDING_CANCEL);
        client.assertExecutionReportQuantities(orderQuantity, orderQuantity / 4, 3.0 / 4.0 * orderQuantity, 1.0 / 4.0 * orderQuantity);

        // when
        conn.acceptCancel(0);

        // then
        client.receiveExecutionReport();
        client.assertExecutionReportStatus(ClientExecType.CLIENT_EXEC_TYPE_CANCELED, ClientOrderStatus.CANCELED);
        client.assertExecutionReportQuantities(orderQuantity, orderQuantity / 4, 0.0, 0.0);
        client.assertExecutionReportSide(client.getOrder().getSide());
        assertThat(client.getExecutionReport().getOrderId()).isNotBlank();
        assertThat(client.getExecutionReport().getInstrumentId()).isEqualTo(clientInstrument.instrumentIdentifiers().instrumentId());
        assertThat(Double.parseDouble(client.getExecutionReport().getAvgPrice())).isEqualTo(Double.parseDouble(SimpleOrderFactory.DEFAULT_LIMIT_PRICE));
        assertThat(client.getExecutionReport().getClOrderId()).isEqualTo(client.getCancelClientOrderId());
        assertThat(client.getExecutionReport().getOrigClOrderId()).isEqualTo(client.getOriginalClientOrderId());
    }

    @Test
    void B1c_cancelRequestIssuedForOrderThatBecomesPartiallyFilledBeforeCancelRequestCanBeAccepted(ConnectorMockSession conn,
                                                                                          InstrumentResponseDto clientInstrument,
                                                                                          ClientSession client) {
        // when
        client.sendOrder(clientOrderFactory.defaultOrder(ClientOrderType.LIMIT, clientInstrument.instrumentIdentifiers().instrumentId()));
        conn.acceptNewOrder();

        // then
        client.receiveExecutionReport();
        client.assertExecutionReportStatus(ClientExecType.CLIENT_EXEC_TYPE_NEW, ClientOrderStatus.NEW);

        // when
        client.sendCancel();

        // then
        client.receiveExecutionReport();
        client.assertExecutionReportStatus(ClientExecType.CLIENT_EXEC_TYPE_PENDING_CANCEL, ClientOrderStatus.PENDING_CANCEL);

        // when
        double orderQuantity = Double.parseDouble(client.getOrder().getQuantity());
        conn.fillPart(orderQuantity / 4, Double.parseDouble(SimpleOrderFactory.DEFAULT_LIMIT_PRICE));

        // then
        client.receiveExecutionReport();
        client.assertExecutionReportStatus(ClientExecType.CLIENT_EXEC_TYPE_PARTIAL_FILL, ClientOrderStatus.PENDING_CANCEL);
        client.assertExecutionReportQuantities(orderQuantity, orderQuantity / 4, 3.0 / 4.0 * orderQuantity, 1.0 / 4.0 * orderQuantity);

        // when
        conn.rejectCancel(ErrorMsg.BROKER_OPTION);

        // then
        client.receiveCancelRejectAfterCancel();
        client.assertCancelRejectOrderOriginalClientId();
        assertThat(client.getCancelReject().getClOrderId()).isEqualTo(client.getCancelClientOrderId());
        assertThat(client.getCancelReject().getOrderStatus()).isEqualTo(ClientOrderStatus.PARTIALLY_FILLED);
    }

    @Test
    void B1c_cancelRequestIssuedForOrderThatBecomesFilledBeforeCancelRequestCanBeAccepted(ConnectorMockSession conn,
                                                                                                   InstrumentResponseDto clientInstrument,
                                                                                                   ClientSession client) {
        // when
        client.sendOrder(clientOrderFactory.defaultOrder(ClientOrderType.MARKET, clientInstrument.instrumentIdentifiers().instrumentId()));
        conn.acceptNewOrder();

        // then
        client.receiveExecutionReport();
        client.assertExecutionReportStatus(ClientExecType.CLIENT_EXEC_TYPE_NEW, ClientOrderStatus.NEW);

        // when
        client.sendCancel();

        // then
        client.receiveExecutionReport();
        client.assertExecutionReportStatus(ClientExecType.CLIENT_EXEC_TYPE_PENDING_CANCEL, ClientOrderStatus.PENDING_CANCEL);

        // when
        double orderQuantity = Double.parseDouble(client.getOrder().getQuantity());
        conn.fillFull(orderQuantity, Double.parseDouble(SimpleOrderFactory.DEFAULT_LIMIT_PRICE));

        // then
        client.receiveExecutionReport();
        client.assertExecutionReportStatus(ClientExecType.CLIENT_EXEC_TYPE_FILL, ClientOrderStatus.PENDING_CANCEL);
        client.assertExecutionReportQuantities(orderQuantity, orderQuantity, 0, orderQuantity);

        // when
        conn.rejectCancel(ErrorMsg.BROKER_OPTION);

        // then
        client.receiveCancelRejectAfterCancel();
        client.assertCancelRejectOrderOriginalClientId();
        assertThat(client.getCancelReject().getClOrderId()).isEqualTo(client.getCancelClientOrderId());
        assertThat(client.getCancelReject().getOrderStatus()).isEqualTo(ClientOrderStatus.FILLED);
    }

    @Test
    void B1de_cancelRequestIssuedForAnOrderThatHasNotYetBeenAcknowledged(InstrumentResponseDto clientInstrument, ClientSession client) {
        // scenario is different from fix B.1.d and B.1.e, because we reject cancel if it was sent before receiving new order accept
        // when
        client.sendDefaultLimitOrder(10000.0, clientInstrument.instrumentIdentifiers().instrumentId());
        client.sendCancel();
        client.receiveCancelRejectAfterCancel();

        // then
        client.assertCancelRejectOrderOriginalClientId();
        assertThat(client.getCancelReject().getClOrderId()).isEqualTo(client.getCancelClientOrderId());
        assertThat(client.cancelRejectOrderStatus()).isEqualTo(ClientOrderStatus.PENDING_NEW);
        assertThat(client.getCancelReject().getReason()).isEqualTo(expectedCancelBeforeSubmissionErrorMessage());
    }

    protected String expectedCancelBeforeSubmissionErrorMessage() {
        return "Cancel not allowed after OEMS request sent and before OEMS submission confirmed.";
    }

}

@Fix
class FixFIXSpecBCancelAgencyBrokerDeskTest extends FIXSpecBCancelAgencyBrokerDeskTest {
    @Override
    protected String expectedCancelBeforeSubmissionErrorMessage() {
        return "Other reason.";
    }
}

@Rest
class RestFIXSpecBCancelAgencyBrokerDeskTest extends FIXSpecBCancelAgencyBrokerDeskTest {

}
