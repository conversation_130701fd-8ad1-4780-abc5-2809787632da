package io.wyden.exchange.marketdata;

import ch.algotrader.api.connector.marketdata.domain.AskDTO;
import ch.algotrader.api.connector.marketdata.domain.BidAskQuoteDTO;
import ch.algotrader.api.connector.marketdata.domain.BidDTO;
import ch.algotrader.api.connector.marketdata.domain.MarketDataEventDTO;
import ch.algotrader.api.connector.marketdata.domain.OrderBookDTO;
import ch.algotrader.api.connector.marketdata.domain.TradeDTO;

import quickfix.field.MDEntryType;

import java.util.HashSet;
import java.util.Set;

public enum MarketDataEntryType {
    BID(BidDTO.class),
    ASK(AskDTO.class),
    BIDASK(BidAskQuoteDTO.class),
    TRADE(TradeDTO.class),
    ORDERBOOK(OrderBookDTO.class);

    private final Class<? extends MarketDataEventDTO> eventClass;

    MarketDataEntryType(Class<? extends MarketDataEventDTO> eventClass) {
        this.eventClass = eventClass;
    }

    boolean matches(MarketDataEventDTO event) {
        return event.getClass().isAssignableFrom(eventClass);
    }

    static Set<MarketDataEntryType> fromMDEntryType(Set<Character> mdEntryTypes) {
        Set<MarketDataEntryType> result = new HashSet<>();
        if (mdEntryTypes.contains(MDEntryType.BID) && mdEntryTypes.contains(MDEntryType.OFFER)) {
            result.add(BIDASK);
        }
        if (mdEntryTypes.contains(MDEntryType.BID)) {
            result.add(BID);
            result.add(ORDERBOOK);
        }
        if (mdEntryTypes.contains(MDEntryType.OFFER)) {
            result.add(ASK);
            result.add(ORDERBOOK);
        }
        if (mdEntryTypes.contains(MDEntryType.TRADE)) {
            result.add(TRADE);
        }
        return result;
    }
}
