/*
 * Copyright 2019 <PERSON><PERSON><PERSON>
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package io.wyden.aeron.report;


import org.eclipse.collections.impl.map.mutable.primitive.IntObjectHashMap;

import exchange.core2.core.common.CoreSymbolSpecification;
import exchange.core2.core.common.api.reports.ReportResult;
import net.openhft.chronicle.bytes.BytesOut;

public final class SymbolSpecificationReportResult implements ReportResult {

    private final IntObjectHashMap<CoreSymbolSpecification> symbols;

    public SymbolSpecificationReportResult(IntObjectHashMap<CoreSymbolSpecification> symbols) {
        this.symbols = symbols;
    }

    public IntObjectHashMap<CoreSymbolSpecification> getSymbols() {
        return symbols;
    }

    public static SymbolSpecificationReportResult create(IntObjectHashMap<CoreSymbolSpecification> symbols) {
        return new SymbolSpecificationReportResult(symbols);
    }

    @Override
    public void writeMarshallable(final BytesOut bytes) {
        // do nothing
    }

    @Override
    public String toString() {
        return "[SymbolSpecificationReportResult]symbols=" + symbols;
    }
}
