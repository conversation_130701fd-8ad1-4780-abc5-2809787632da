package io.wyden.aeron.client.pojo;

import io.wyden.aeron.client.enumeration.EventType;

public abstract class Event {

    private final EventType eventType;
    private final int symbol;
    private final long timestamp;

    public Event(EventType eventType, final int symbol, final long timestamp) {

        this.eventType = eventType;
        this.symbol = symbol;
        this.timestamp = timestamp;
    }

    public EventType getEventType() {
        return eventType;
    }

    public int getSymbol() {
        return symbol;
    }

    public long getTimestamp() {
        return timestamp;
    }

}
