package io.wyden.test.scenariorunner.integration.fixclient.mapper;

import io.wyden.cloudutils.tools.DateUtils;
import io.wyden.published.client.ClientCancelRejectResponseTo;
import io.wyden.published.client.ClientExecType;
import io.wyden.published.client.ClientInstrumentType;
import io.wyden.published.client.ClientOrderStatus;
import io.wyden.published.client.ClientOrderType;
import io.wyden.published.client.ClientResponse;
import io.wyden.published.client.ClientSide;
import io.wyden.published.client.ClientTIF;
import io.wyden.published.client.FeeBasis;
import io.wyden.published.client.FeeData;
import io.wyden.published.client.FeeType;
import io.wyden.test.scenariorunner.data.refdata.InstrumentFactory;
import org.apache.commons.lang3.ObjectUtils;
import quickfix.Group;
import quickfix.field.AvgPx;
import quickfix.field.ClOrdID;
import quickfix.field.CumQty;
import quickfix.field.CxlRejReason;
import quickfix.field.CxlRejResponseTo;
import quickfix.field.ExecID;
import quickfix.field.ExecType;
import quickfix.field.ExpireTime;
import quickfix.field.LastPx;
import quickfix.field.LastQty;
import quickfix.field.LeavesQty;
import quickfix.field.MiscFeeAmt;
import quickfix.field.MiscFeeBasis;
import quickfix.field.MiscFeeCurr;
import quickfix.field.MiscFeeType;
import quickfix.field.NoMiscFees;
import quickfix.field.OrdRejReason;
import quickfix.field.OrdStatus;
import quickfix.field.OrdType;
import quickfix.field.OrderID;
import quickfix.field.OrderQty;
import quickfix.field.OrigClOrdID;
import quickfix.field.SecondaryExecID;
import quickfix.field.SecondaryOrderID;
import quickfix.field.SecurityExchange;
import quickfix.field.SecurityType;
import quickfix.field.Side;
import quickfix.field.Symbol;
import quickfix.field.Text;
import quickfix.field.TimeInForce;
import quickfix.field.TransactTime;
import quickfix.fix44.ExecutionReport;
import quickfix.fix44.OrderCancelReject;

import java.util.List;

import static io.wyden.test.scenariorunner.integration.fixclient.dictionary.CustomFixFields.WYDEN_ACCOUNT;
import static io.wyden.test.scenariorunner.integration.fixclient.dictionary.CustomFixFields.WYDEN_PORTFOLIO;
import static io.wyden.test.scenariorunner.integration.fixclient.dictionary.CustomFixFields.WYDEN_ROOT_EXECUTION_ID;
import static io.wyden.test.scenariorunner.integration.fixclient.mapper.FromFixMsgMapper.getCharField;
import static io.wyden.test.scenariorunner.integration.fixclient.mapper.FromFixMsgMapper.getDoubleField;
import static io.wyden.test.scenariorunner.integration.fixclient.mapper.FromFixMsgMapper.getIntField;
import static io.wyden.test.scenariorunner.integration.fixclient.mapper.FromFixMsgMapper.getStringField;
import static org.apache.commons.lang3.StringUtils.EMPTY;
import static quickfix.field.OrdRejReason.DUPLICATE_ORDER;

public class FromFixTradingMapper {

    public static ClientResponse createExecutionReport(quickfix.fix44.ExecutionReport executionReport, String clientId) {

        // TODO: This should translate to ISO 8601 but RestActor copies verbatim epoch millis from rest-api-server
        String transactTime = ObjectUtils.firstNonNull(
            DateUtils.zonedDateTimeToEpochMillis(DateUtils.fromFixUtcTime(getStringField(executionReport, TransactTime.FIELD))),
            EMPTY
        );

        ClientResponse.Builder builder = ClientResponse.newBuilder()
            .setOrderId(getStringField(executionReport, OrderID.FIELD))
            .setExecutionId(getStringField(executionReport, ExecID.FIELD))
            .setVenueExecutionId(getStringField(executionReport, SecondaryExecID.FIELD))
            .setClOrderId(getStringField(executionReport, ClOrdID.FIELD))
            .setOrigClOrderId(getStringField(executionReport, OrigClOrdID.FIELD))
            .setClientId(clientId)
            // .setOrderStatusRequestId() // TODO: Value
            // .setTargetVenueAccount() // TODO: Value
            .setExecType(createExecType(getCharField(executionReport, ExecType.FIELD)))
            .setOrderStatus(createOrderStatus(getCharField(executionReport, OrdStatus.FIELD)))
            .setReason(getRejectReason(executionReport)) // TODO: better way of passing this values
            .setSide(createSide(getCharField(executionReport, Side.FIELD)))
            .setOrderQty(Double.toString(getDoubleField(executionReport, OrderQty.FIELD)))
            .setLastQty(Double.toString(getDoubleField(executionReport, LastQty.FIELD)))
            .setLeavesQty(Double.toString(getDoubleField(executionReport, LeavesQty.FIELD)))
            .setCumQty(Double.toString(getDoubleField(executionReport, CumQty.FIELD)))
            .setLastPrice(Double.toString(getDoubleField(executionReport, LastPx.FIELD)))
            .setAvgPrice(Double.toString(getDoubleField(executionReport, AvgPx.FIELD)))
            .setTimestamp(transactTime)
            .setTargetVenueTimestamp(transactTime)
            .setSymbol(getStringField(executionReport, Symbol.FIELD))
            .setInstrumentType(ClientInstrumentType.valueOf(getStringField(executionReport, SecurityType.FIELD)))
            .setVenueAccount(getStringField(executionReport, WYDEN_ACCOUNT.fieldId()))
            .setInstrumentId(InstrumentFactory.createInstrumentId(getStringField(executionReport, Symbol.FIELD), getStringField(executionReport, SecurityType.FIELD), getStringField(executionReport, SecurityExchange.FIELD)))
            // .setTargetVenueTicker() // TODO: Value
            .setText(getStringField(executionReport, Text.FIELD))
            .setPortfolioId(getStringField(executionReport, WYDEN_PORTFOLIO.fieldId()))
            .setTif(createTif(getCharField(executionReport, TimeInForce.FIELD)))
            .setExpireTime(getStringField(executionReport, ExpireTime.FIELD))
            .setRootExecution(ClientResponse.ExecutionReference.newBuilder()
                .setExecutionId(getStringField(executionReport, WYDEN_ROOT_EXECUTION_ID.fieldId()))
                .build())
            // temporary solution to unblock AC-6031 test
            // TODO proper FIX execution report structure: https://algotrader.atlassian.net/browse/AC-6102
            .setUnderlyingExecution(ClientResponse.ExecutionReference.newBuilder()
                .setOrderId(getStringField(executionReport, SecondaryOrderID.FIELD))
                .build());

        if (executionReport.isSetField(NoMiscFees.FIELD)) {
            setFeeData(executionReport, builder);
        }

        return builder.build();
    }

    private static void setFeeData(ExecutionReport executionReport, ClientResponse.Builder builder) {
        List<Group> feeDataGroups = executionReport.getGroups(NoMiscFees.FIELD);

        for (int i = 0; i < feeDataGroups.size(); i++) {

            Group group = feeDataGroups.get(i);

            String feeAmount = getStringField(group, MiscFeeAmt.FIELD);
            String feeCurrency = getStringField(group, MiscFeeCurr.FIELD);
            int feeBasis = getIntField(group, MiscFeeBasis.FIELD);
            String feeType = getStringField(group, MiscFeeType.FIELD);

            FeeData feeData = FeeData.newBuilder()
                .setAmount(feeAmount)
                .setCurrency(feeCurrency)
                .setBasis(fromFixFeeBasis(feeBasis))
                .setType(fromFixFeeType(feeType))
                .build();

            builder.addFeeData(i, feeData);
        }
    }

    private static FeeType fromFixFeeType(String feeType) {
        return switch (feeType) {
            case MiscFeeType.EXCHANGE_FEES -> FeeType.EXCHANGE_FEE;
            case MiscFeeType.PER_TRANSACTION -> FeeType.TRANSACTION_FEE;
            default -> FeeType.FEE_TYPE_UNSPECIFIED;
        };
    }

    private static FeeBasis fromFixFeeBasis(int feeBasis) {
        return switch (feeBasis) {
            case MiscFeeBasis.ABSOLUTE -> FeeBasis.ABSOLUTE;
            default -> FeeBasis.FEE_BASIS_UNSPECIFIED;
        };
    }

    private static ClientExecType createExecType(char execType) {
        return switch (execType) {
            case ExecType.NEW -> ClientExecType.CLIENT_EXEC_TYPE_NEW;
            case ExecType.PARTIAL_FILL -> ClientExecType.CLIENT_EXEC_TYPE_PARTIAL_FILL;
            case ExecType.FILL -> ClientExecType.CLIENT_EXEC_TYPE_FILL;
            case ExecType.DONE_FOR_DAY -> ClientExecType.CLIENT_EXEC_TYPE_DONE_FOR_DAY;
            case ExecType.CANCELED -> ClientExecType.CLIENT_EXEC_TYPE_CANCELED;
            case ExecType.REPLACED -> ClientExecType.CLIENT_EXEC_TYPE_REPLACED;
            case ExecType.PENDING_CANCEL -> ClientExecType.CLIENT_EXEC_TYPE_PENDING_CANCEL;
            case ExecType.STOPPED -> ClientExecType.CLIENT_EXEC_TYPE_STOPPED;
            case ExecType.REJECTED -> ClientExecType.CLIENT_EXEC_TYPE_REJECTED;
            case ExecType.SUSPENDED -> ClientExecType.CLIENT_EXEC_TYPE_SUSPENDED;
            case ExecType.PENDING_NEW -> ClientExecType.CLIENT_EXEC_TYPE_PENDING_NEW;
            case ExecType.CALCULATED -> ClientExecType.CLIENT_EXEC_TYPE_CALCULATED;
            case ExecType.EXPIRED -> ClientExecType.CLIENT_EXEC_TYPE_EXPIRED;
            case ExecType.RESTATED -> ClientExecType.CLIENT_EXEC_TYPE_RESTATED;
            case ExecType.PENDING_REPLACE -> ClientExecType.CLIENT_EXEC_TYPE_PENDING_REPLACE;
            case ExecType.TRADE -> ClientExecType.CLIENT_EXEC_TYPE_TRADE;
            case ExecType.TRADE_CORRECT -> ClientExecType.CLIENT_EXEC_TYPE_TRADE_CORRECT;
            case ExecType.TRADE_CANCEL -> ClientExecType.CLIENT_EXEC_TYPE_TRADE_CANCEL;
            case ExecType.ORDER_STATUS -> ClientExecType.CLIENT_EXEC_TYPE_ORDER_STATUS;
            case ExecType.TRADE_IN_A_CLEARING_HOLD -> ClientExecType.CLIENT_EXEC_TYPE_TRADE_IN_A_CLEARING_HOLD;
            case ExecType.TRADE_HAS_BEEN_RELEASED_TO_CLEARING -> ClientExecType.CLIENT_EXEC_TYPE_TRADE_HAS_BEEN_RELEASED_TO_CLEARING;
            case ExecType.TRIGGERED_OR_ACTIVATED_BY_SYSTEM -> ClientExecType.CLIENT_EXEC_TYPE_TRIGGERED_OR_ACTIVATED_BY_SYSTEM;
            default -> throw new IllegalArgumentException("Cannot map ExecType from FIX: " + execType);
        };
    }

    private static ClientOrderStatus createOrderStatus(char orderStatus) {
        return switch (orderStatus) {
            case OrdStatus.NEW -> ClientOrderStatus.NEW;
            case OrdStatus.CALCULATED -> ClientOrderStatus.CALCULATED;
            case OrdStatus.CANCELED -> ClientOrderStatus.CANCELED;
            case OrdStatus.ACCEPTED_FOR_BIDDING -> ClientOrderStatus.ACCEPTED_FOR_BIDDING;
            case OrdStatus.EXPIRED -> ClientOrderStatus.EXPIRED;
            case OrdStatus.FILLED -> ClientOrderStatus.FILLED;
            case OrdStatus.DONE_FOR_DAY -> ClientOrderStatus.DONE_FOR_DAY;
            case OrdStatus.PARTIALLY_FILLED -> ClientOrderStatus.PARTIALLY_FILLED;
            case OrdStatus.PENDING_CANCEL -> ClientOrderStatus.PENDING_CANCEL;
            case OrdStatus.PENDING_NEW -> ClientOrderStatus.PENDING_NEW;
            case OrdStatus.REJECTED -> ClientOrderStatus.REJECTED;
            case OrdStatus.PENDING_REPLACE -> ClientOrderStatus.PENDING_REPLACE;
            case OrdStatus.REPLACED -> ClientOrderStatus.REPLACED;
            case OrdStatus.STOPPED -> ClientOrderStatus.STOPPED;
            case OrdStatus.SUSPENDED -> ClientOrderStatus.SUSPENDED;
            default -> ClientOrderStatus.UNRECOGNIZED;
        };
    }

    private static ClientSide createSide(char side) {
        return switch (side) {
            // todo we may need something custom here to allow FIX clients to determin cross margin vs isolated trading type on target exvhange
            case quickfix.field.Side.BUY -> ClientSide.BUY;
            case quickfix.field.Side.SELL -> ClientSide.SELL;
            case quickfix.field.Side.SELL_SHORT -> ClientSide.SELL_SHORT;
            case quickfix.field.Side.UNDISCLOSED -> ClientSide.SIDE_UNDETERMINED;
            default -> throw new IllegalArgumentException("Cannot map side from FIX: " + side);
        };
    }

    private static String getRejectReason(quickfix.fix44.ExecutionReport executionReport) {
        boolean isDuplicatedRejectReason = (getIntField(executionReport, OrdRejReason.FIELD) == DUPLICATE_ORDER);
        return isDuplicatedRejectReason ? "Unique constraint violation" : ""; //TODO: better mapping
    }

    private static ClientTIF createTif(char tif) {
        return switch (tif) {
            case TimeInForce.DAY -> ClientTIF.DAY;
            case TimeInForce.FILL_OR_KILL -> ClientTIF.FOK;
            case TimeInForce.GOOD_TILL_CANCEL -> ClientTIF.GTC;
            case TimeInForce.GOOD_TILL_DATE -> ClientTIF.GTD;
            case TimeInForce.IMMEDIATE_OR_CANCEL -> ClientTIF.IOC;
            default -> ClientTIF.TIF_UNSPECIFIED;
        };

    }

    public static ClientResponse createCancelReject(OrderCancelReject orderCancelReject) {
        return ClientResponse.newBuilder()
            .setClOrderId(getStringField(orderCancelReject, ClOrdID.FIELD))
            .setOrigOrderId(getStringField(orderCancelReject, OrderID.FIELD))
            .setOrigClOrderId(getStringField(orderCancelReject, OrigClOrdID.FIELD))
            .setOrderStatus(createOrderStatus(getCharField(orderCancelReject, OrdStatus.FIELD)))
            .setReason(fromFIXCxlRejReason(getIntField(orderCancelReject, CxlRejReason.FIELD)))
            .setCancelRejectResponseTo(fromFIXCxlRejResponseTo(getCharField(orderCancelReject, CxlRejResponseTo.FIELD)))
            .setText(getStringField(orderCancelReject, Text.FIELD))
            .build();
    }

    private static String fromFIXCxlRejReason(int value) {
        return switch (value) {
            case CxlRejReason.TOO_LATE_TO_CANCEL -> "Too late to cancel.";
            case CxlRejReason.UNKNOWN_ORDER -> "Unknown order.";
            case CxlRejReason.BROKER_EXCHANGE_OPTION -> "Broker exchange option.";
            case CxlRejReason.ORDER_ALREADY_IN_PENDING_CANCEL_OR_PENDING_REPLACE_STATUS ->
                "Order already in pending cancel or pending replace status.";
            case CxlRejReason.UNABLE_TO_PROCESS_ORDER_MASS_CANCEL_REQUEST -> "Unable to process order mass cancel request.";
            case CxlRejReason.ORIGORDMODTIME_OF_ORDER -> "OrigOrdModTime of order.";
            case CxlRejReason.DUPLICATE_CLORDID_RECEIVED -> "Duplicate ClOrdID received.";
            case CxlRejReason.PRICE_EXCEEDS_CURRENT_PRICE -> "Price exceeds current price.";
            case CxlRejReason.PRICE_EXCEEDS_CURRENT_PRICE_BAND -> "Price exceeds current price band.";
            case CxlRejReason.INVALID_PRICE_INCREMENT -> "Invalid price increment.";
            case CxlRejReason.OTHER -> "Other reason.";
            default -> throw new IllegalArgumentException("Cannot map ClxRejReason from FIX: " + value);
        };
    }

    private static ClientCancelRejectResponseTo fromFIXCxlRejResponseTo(char value) {
        return switch (value) {
            case CxlRejResponseTo.ORDER_CANCEL_REQUEST -> ClientCancelRejectResponseTo.ORDER_CANCEL_REQUEST;
            case CxlRejResponseTo.ORDER_CANCEL_REPLACE_REQUEST -> ClientCancelRejectResponseTo.ORDER_CANCEL_REPLACE_REQUEST;
            default -> ClientCancelRejectResponseTo.UNRECOGNIZED;
        };
    }

    private static ClientOrderType fromFixOrdType(char orderType) {
        return switch (orderType) {
            case OrdType.MARKET -> ClientOrderType.MARKET;
            case OrdType.LIMIT -> ClientOrderType.LIMIT;
            case OrdType.STOP_STOP_LOSS -> ClientOrderType.STOP;
            case OrdType.STOP_LIMIT -> ClientOrderType.STOP_LIMIT;
            default -> ClientOrderType.UNRECOGNIZED;
        };
    }
}
