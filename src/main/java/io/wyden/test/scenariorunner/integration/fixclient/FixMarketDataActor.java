package io.wyden.test.scenariorunner.integration.fixclient;

import io.wyden.test.scenariorunner.config.Configuration;
import io.wyden.test.scenariorunner.integration.service.accessgateway.KeySecret;
import io.wyden.test.scenariorunner.model.marketdata.L1Event;
import io.wyden.test.scenariorunner.model.marketdata.L2Event;
import reactor.core.publisher.Flux;

/**
 * Actor to operate FIX market data server through FIX actor service.<br>
 * See FIX server implementation: <a href="https://gitlab.wyden.io/atcloud/oems/fix-api-server">fix-api-server</a>
 */
public class FixMarketDataActor extends FixActor {

    private static final String TARGET = "MarketData";

    private final FixMarketDataService fixMarketDataService;

    public FixMarketDataActor(String clientId, KeySecret keySecret) {
        super(Configuration.getHostSettings().fixMarketDataActorHost(), TARGET, clientId, keySecret);
        this.fixMarketDataService = new FixMarketDataService(fixActorClient, clientId);
    }

    public Flux<L1Event> getStreetSideL1Flux(String venueAccount, String instrumentId) {
        return fixMarketDataService.getStreetSideL1Flux(venueAccount, instrumentId);
    }

    public Flux<L1Event> getClientSideL1Flux(String portfolioId, String instrumentId) {
        return fixMarketDataService.getClientSideL1Flux(portfolioId, instrumentId);
    }

    public Flux<L2Event> getStreetSideL2Flux(String venueAccount, String instrumentId) {
        return fixMarketDataService.getStreetSideL2Flux(venueAccount, instrumentId);
    }

    public Flux<L2Event> getClientSideL2Flux(String portfolioId, String instrumentId) {
        return fixMarketDataService.getClientSideL2Flux(portfolioId, instrumentId);
    }

}
