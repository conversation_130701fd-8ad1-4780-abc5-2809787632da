package io.wyden.test.scenariorunner.integration.tool.connectormock;

import io.wyden.published.venue.VenueReconciliationRequest;
import io.wyden.published.venue.VenueRequest;
import org.springframework.http.MediaType;
import org.springframework.web.reactive.function.client.WebClient;
import reactor.core.publisher.Flux;

import static io.wyden.test.scenariorunner.config.Timeouts.WAIT_FOR_CONDITION;

public class ConnectorMockQueries {

    public static final String ACTUATOR_HEALTH = "/actuator/health";
    public static final String RECEIVED_NEW_ORDER_REQUEST = "/received/new/%s";
    public static final String RECEIVED_CANCEL_REQUEST = "/received/cancel/%s";
    public static final String RECONCILIATION_REQUEST = "/reconciliation/request/%s";

    public static final String UP = "UP";

    private final WebClient webClient;

    public ConnectorMockQueries(WebClient webClient) {
        this.webClient = webClient;
    }

    public boolean isHealthy() {
        return Boolean.TRUE.equals(webClient.get()
            .uri(ACTUATOR_HEALTH)
            .retrieve()
            .bodyToMono(String.class)
            .map(reply -> reply.contains(UP))
            .onErrorReturn(false)
            .block());

    }

    public VenueRequest awaitNewSingleOrderReceived(String accountName) {
        return awaitNewSingleOrderReceived(accountName, 0);
    }

    public VenueRequest awaitNewSingleOrderReceived(String accountName, int skip) {
        return webClient
            .get()
            .uri(RECEIVED_NEW_ORDER_REQUEST.formatted(accountName))
            .accept(MediaType.APPLICATION_OCTET_STREAM)
            .retrieve()
            .bodyToFlux(VenueRequest.class)
            .skip(skip)
            .blockFirst(WAIT_FOR_CONDITION);
    }

    public VenueRequest awaitOrderCancelReceived(String accountName, String extId) {
        return webClient
            .get()
            .uri(RECEIVED_CANCEL_REQUEST.formatted(accountName))
            .accept(MediaType.APPLICATION_OCTET_STREAM)
            .retrieve()
            .bodyToFlux(VenueRequest.class)
            .filter(cancel -> cancel.getExtId().equals(extId))
            .blockFirst(WAIT_FOR_CONDITION);
    }

    public Flux<VenueRequest> getVenueOrderFlux(String accountName) {
        return webClient.get()
            .uri(RECEIVED_NEW_ORDER_REQUEST.formatted(accountName))
            .accept(MediaType.APPLICATION_OCTET_STREAM)
            .retrieve()
            .bodyToFlux(VenueRequest.class);
    }

    public Flux<VenueRequest> getVenueCancelFlux(String accountName) {
        return webClient.get()
            .uri(RECEIVED_CANCEL_REQUEST.formatted(accountName))
            .accept(MediaType.APPLICATION_OCTET_STREAM)
            .retrieve()
            .bodyToFlux(VenueRequest.class);
    }

    public Flux<VenueReconciliationRequest> getReconciliationRequests(String accountName) {
        return webClient.get()
            .uri(RECONCILIATION_REQUEST.formatted(accountName))
            .accept(MediaType.APPLICATION_OCTET_STREAM)
            .retrieve()
            .bodyToFlux(VenueReconciliationRequest.class);
    }

}
