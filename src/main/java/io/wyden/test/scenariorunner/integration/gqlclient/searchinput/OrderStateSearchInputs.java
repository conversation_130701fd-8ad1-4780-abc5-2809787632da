package io.wyden.test.scenariorunner.integration.gqlclient.searchinput;

import io.wyden.apiserver.rest.orderhistory.model.OrderStateSearchInput;
import io.wyden.apiserver.rest.apiui.SharedModel.SortingOrder;
import io.wyden.apiserver.rest.orderhistory.model.CollectionPredicateInput;
import io.wyden.apiserver.rest.orderhistory.model.DatePredicateInput;
import io.wyden.apiserver.rest.orderhistory.model.SimplePredicateInput;

import java.util.Arrays;
import java.util.List;

public class OrderStateSearchInputs {

    public static OrderStateSearchInput all() {
        return new OrderStateSearchInput(null, null, null, SortingOrder.DESC, null, null);
    }

    public static OrderStateSearchInput firstNRecordsInSortOrder(int first, SortingOrder sortOrder) {
        return new OrderStateSearchInput(null, null, null, sortOrder, first, null);
    }

    public static OrderStateSearchInput firstNRecords(int first, String after) {
        return new OrderStateSearchInput(null, null, null, SortingOrder.DESC, first, after);
    }

    public static OrderStateSearchInput byPortfolio(String portfolioId) {
        return new OrderStateSearchInput(List.of(equalToPortfolio(portfolioId)), null, null, null, null, null);
    }

    public static OrderStateSearchInput byPortfolios(List<String> portfolioIds) {
        return new OrderStateSearchInput(portfolioIds.stream().map(OrderStateSearchInputs::equalToPortfolio).toList(), null, null, null, null, null);
    }

    public static OrderStateSearchInput byPortfolioAndOrderId(String portfolioId, String orderId) {
        return new OrderStateSearchInput(List.of(equalToPortfolio(portfolioId), equalToOrderId(orderId)), null, null, null, null, null);
    }

    public static OrderStateSearchInput byAccount(String account) {
        return new OrderStateSearchInput(List.of(equalToVenueAccountId(account)), null, null, null, null, null);
    }

    public static OrderStateSearchInput byOrderId(String orderId) {
        return new OrderStateSearchInput(List.of(equalToOrderId(orderId)), null, null, null, null, null);
    }

    public static OrderStateSearchInput byInstrumentIds(String... instrumentIds) {
        return new OrderStateSearchInput(null, List.of(equalToInstrumentIds(instrumentIds)), null, null, null, null);
    }

    public static OrderStateSearchInput byOrderStatuses(String... orderStatuses) {
        return new OrderStateSearchInput(null, List.of(inOrderStatuses(orderStatuses)), null, null, null, null);
    }

    public static OrderStateSearchInput byPortfolioIdAndInstrumentIdAndFromDate(String portfolioId, String instrumentId, long to) {
        return new OrderStateSearchInput(
            List.of(equalToPortfolio(portfolioId)),
            List.of(equalToInstrumentIds(instrumentId)),
            List.of(byDate(DatePredicateInput.PredicateType.FROM, DatePredicateInput.Field.CREATED_AT, String.valueOf(to))),
            null, null, null);
    }

    public static OrderStateSearchInput byInstrumentIdAndOrderStatus(String instrumentId, String orderStatus) {
        return new OrderStateSearchInput(null, List.of(equalToInstrumentIds(instrumentId), inOrderStatuses(orderStatus)), null, null, null, null);
    }

    public static OrderStateSearchInput fromCreatedAt(long epochMillis) {
        return new OrderStateSearchInput(null, null, List.of(byDate(DatePredicateInput.PredicateType.FROM, DatePredicateInput.Field.CREATED_AT, String.valueOf(epochMillis))), null, null, null);
    }

    public static OrderStateSearchInput fromCreatedAt(String epochMillis) {
        return new OrderStateSearchInput(null, null, List.of(byDate(DatePredicateInput.PredicateType.FROM, DatePredicateInput.Field.CREATED_AT, epochMillis)), null, null, null);
    }

    public static OrderStateSearchInput toCreatedAt(long epochMillis) {
        return new OrderStateSearchInput(null, null, List.of(byDate(DatePredicateInput.PredicateType.TO, DatePredicateInput.Field.CREATED_AT, String.valueOf(epochMillis))), null, null, null);
    }

    public static OrderStateSearchInput fromUpdatedAt(String epochMillis) {
        return new OrderStateSearchInput(null, null, List.of(byDate(DatePredicateInput.PredicateType.FROM, DatePredicateInput.Field.UPDATED_AT, epochMillis)), null, null, null);
    }

    public static OrderStateSearchInput toUpdatedAt(String epochMillis) {
        return new OrderStateSearchInput(null, null, List.of(byDate(DatePredicateInput.PredicateType.TO, DatePredicateInput.Field.UPDATED_AT, epochMillis)), null, null, null);
    }

    public static OrderStateSearchInput toCreatedAt(String epochMillis) {
        return new OrderStateSearchInput(null, null, List.of(byDate(DatePredicateInput.PredicateType.TO, DatePredicateInput.Field.CREATED_AT, epochMillis)), null, null, null);
    }

    public static OrderStateSearchInput fromCreatedAtToUpdatedAt(String from, String to) {
        return new OrderStateSearchInput(null, null, List.of(byDate(DatePredicateInput.PredicateType.FROM, DatePredicateInput.Field.CREATED_AT, from), byDate(DatePredicateInput.PredicateType.TO, DatePredicateInput.Field.UPDATED_AT, to)), null, null, null);
    }

    private static SimplePredicateInput equalToPortfolio(String portfolioId) {
        return new SimplePredicateInput(SimplePredicateInput.PredicateType.EQUAL, SimplePredicateInput.Field.PORTFOLIO_ID, portfolioId);
    }

    private static SimplePredicateInput equalToVenueAccountId(String account) {
        return new SimplePredicateInput(SimplePredicateInput.PredicateType.EQUAL, SimplePredicateInput.Field.VENUE_ACCOUNT_ID, account);
    }

    private static SimplePredicateInput equalToOrderId(String orderId) {
        return new SimplePredicateInput(SimplePredicateInput.PredicateType.EQUAL, SimplePredicateInput.Field.ORDER_ID, orderId);
    }

    private static CollectionPredicateInput equalToInstrumentIds(String... instrumentId) {
        return new CollectionPredicateInput(CollectionPredicateInput.PredicateType.IN, CollectionPredicateInput.Field.INSTRUMENT_ID, Arrays.asList(instrumentId));
    }

    private static CollectionPredicateInput inOrderStatuses(String... orderStatuses) {
        return new CollectionPredicateInput(CollectionPredicateInput.PredicateType.IN, CollectionPredicateInput.Field.ORDER_STATUS, Arrays.asList(orderStatuses));
    }

    private static DatePredicateInput byDate(DatePredicateInput.PredicateType predicateType, DatePredicateInput.Field field, String epochMillis) {
        return new DatePredicateInput(predicateType, field, epochMillis);
    }

    public static OrderStateSearchInput firstNRecordsInSortOrderInStatus(int first, SortingOrder sortOrder, String orderStatus) {
        return new OrderStateSearchInput(null, List.of(inOrderStatuses(orderStatus)), null, sortOrder, first, null);
    }

}
