package io.wyden.test.scenariorunner.model.booking.trade;

import java.util.Objects;

public abstract class AssetTrade extends Trade {

    protected String instrument;

    public String getInstrument() {
        return instrument;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        if (!super.equals(o)) return false;
        AssetTrade that = (AssetTrade) o;
        return super.equals(that) && Objects.equals(instrument, that.instrument);
    }

    @Override
    public int hashCode() {
        return Objects.hash(super.hashCode(), instrument);
    }

}
