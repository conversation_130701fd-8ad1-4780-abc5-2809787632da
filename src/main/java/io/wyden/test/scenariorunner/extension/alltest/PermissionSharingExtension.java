package io.wyden.test.scenariorunner.extension.alltest;

import io.wyden.apiserver.rest.security.model.AuthorityDto;
import io.wyden.test.scenariorunner.extension.common.PermissionSharer;
import org.junit.jupiter.api.extension.BeforeAllCallback;
import org.junit.jupiter.api.extension.ExtensionContext;

import java.util.Set;

/**
 * This extension:<br>
 * <ul>
 *     <li>beforeAll tests:</li>
 *     - shares defined permissions from owner user to target user
 * </ul>
 */
public class PermissionSharingExtension implements BeforeAllCallback {

    private final PermissionSharer permissionSharer;

    public PermissionSharingExtension(String ownerClientId, String targetClientId, Set<AuthorityDto> permissionsToShare) {
        this.permissionSharer = new PermissionSharer(ownerClientId, targetClientId, permissionsToShare);
    }

    @Override
    public void beforeAll(ExtensionContext context) throws Exception {
        permissionSharer.sharePermissions(context);
    }

}
