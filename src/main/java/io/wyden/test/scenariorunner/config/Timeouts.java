package io.wyden.test.scenariorunner.config;

import java.time.Duration;

public class Timeouts {

    private static final int MULTIPLIER_LOCAL = 10;
    private static final int MULTIPLIER_CI = 1;
    private static final int MULTIPLIER = MULTIPLIER_CI;

    public static final Duration JUST_WAIT = Duration.ofMillis(200 * MULTIPLIER);
    public static final Duration WAIT_FOR_CONDITION_S = Duration.ofSeconds(2 * MULTIPLIER);
    public static final Duration WAIT_FOR_CONDITION = Duration.ofSeconds(5 * MULTIPLIER);
    public static final Duration WAIT_FOR_CONDITION_M = Duration.ofSeconds(15 * MULTIPLIER);
    public static final Duration WAIT_FOR_CONDITION_L = Duration.ofSeconds(30 * MULTIPLIER);
    public static final Duration WAIT_FOR_CONDITION_XL = Duration.ofSeconds(60 * MULTIPLIER);
    public static final Duration POLL_INTERVAL = Duration.ofMillis(250);
    public static final Duration POLL_INTERVAL_L = WAIT_FOR_CONDITION;
    public static final Duration SOCKET_READ_TIMEOUT = Duration.ofMinutes(1);
    public static final Duration SOCKET_CONNECT_TIMEOUT = Duration.ofMinutes(1);
    public static final Duration LOGIN_TIMEOUT = Duration.ofSeconds(15 * MULTIPLIER);

    /**
     * Timeout is used to define if condition didn't happen<br>
     * e.g. no event came in 5 seconds.<br>
     *
     * Usage:<br>
     * {@code await().during(WAIT_FOR_NOT_HAPPEN_CONDITION).until(() -> ...)}<br>
     *
     * This timeout required to be less than atMost timeout of {@link org.awaitility.Awaitility} which is by default 10s .<br>
     *
     * Can be overcome by setting longer atMost timeout:<br>
     * {@code await().during(WAIT_FOR_NOT_HAPPEN_CONDITION).atMost(WAIT_FOR_NOT_HAPPEN_CONDITION.plus(Duration.ofSeconds(2))).until(() -> ...)}
     */
    public static final Duration WAIT_FOR_NOT_HAPPEN_CONDITION = Duration.ofSeconds(5);

    public static final long SCENARIO_TIMEOUT_L = 60 * MULTIPLIER;
    public static final long SCENARIO_TIMEOUT_M = 30 * MULTIPLIER;
    public static final long SCENARIO_TIMEOUT_S = 10 * MULTIPLIER;
}
