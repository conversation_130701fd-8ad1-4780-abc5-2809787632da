package io.wyden.test.scenariorunner.data.source;

import io.wyden.published.client.ClientOrderType;
import org.junit.jupiter.params.provider.EnumSource;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

@Retention(RetentionPolicy.RUNTIME)
@Target(ElementType.METHOD)
@EnumSource(value = ClientOrderType.class, mode = EnumSource.Mode.INCLUDE, names = {"MARKET", "LIMIT", "STOP", "STOP_LIMIT"})
public @interface SimpleClientOrderTypesSource {
}
