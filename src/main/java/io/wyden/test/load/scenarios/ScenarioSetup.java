package io.wyden.test.load.scenarios;

import io.wyden.test.load.actors.cloud.ConfigurationService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.ArrayList;
import java.util.List;

import static io.wyden.test.load.actors.cloud.ConfigurationService.RETAIL_PORTFOLIO;
import static io.wyden.test.load.actors.cloud.ConfigurationService.SIMULATOR_ACCOUNT;

public class ScenarioSetup {

    private static final String STREET_PREFIX = "Street";
    private static final String CLIENT_PREFIX = "Client";
    private static final String CLOB_PREFIX = "Clob";

    private static final Logger LOGGER = LoggerFactory.getLogger(ScenarioSetup.class);

    private final ConfigurationService configService;

    private final List<String> globallyConfiguredScenarioPrefixes = new ArrayList<>();

    public ScenarioSetup(ConfigurationService configService) {
        this.configService = configService;
    }

    //TODO perform one-time setups where needed
    public void setup(String scenarioName) {

        if (scenarioName.startsWith(STREET_PREFIX)) {
            if (isGlobalSetupRequired(STREET_PREFIX)) {
                configService.setupClientPortfolio(RETAIL_PORTFOLIO);
                configService.shareSimulatorAccountPermissionsToAdministrators();
                globallyConfiguredScenarioPrefixes.add(STREET_PREFIX);
                LOGGER.info("Configured street scenarios globally");
            }
        }

        if (scenarioName.startsWith(CLIENT_PREFIX)) {
            if (isGlobalSetupRequired(CLIENT_PREFIX)) {
                String portfolio = RETAIL_PORTFOLIO;
                configService.setupClientPortfolio(portfolio);
                configService.setupBankPortfolio();
                configService.createBrokerDeskConfig(portfolio, SIMULATOR_ACCOUNT);
                configService.createClientSideInstrumentIfNeeded();

                globallyConfiguredScenarioPrefixes.add(CLIENT_PREFIX);
                LOGGER.info("Configured client scenarios globally");
            }
        }

        if (scenarioName.startsWith(CLOB_PREFIX)) {
            if (isGlobalSetupRequired(CLOB_PREFIX)) {
                configService.setupBankPortfolio();
                configService.createPortfolioGroupWithWydenExchangeBrokerDeskConfig();
                configService.setupClobClientPortfolios();
                configService.createClientSideInstrumentIfNeeded();
                configService.createWydenExchangeInstrumentIfNotExist();
                String clobUid = configService.configureQuotingEngine();
                configService.updateClobInstrumentConfig(clobUid, "DOGEORT@FOREX@Wyden Exchange", "DOGEORT@FOREX@Generic", null);

                globallyConfiguredScenarioPrefixes.add(CLOB_PREFIX);
                LOGGER.info("Configured clob globally");
            }
        }

    }

    private boolean isGlobalSetupRequired(String scenarioName) {
        return !globallyConfiguredScenarioPrefixes.contains(scenarioName);
    }

}
