package io.wyden.orderhistory.infrastructure.rabbit;

import com.rabbitmq.client.AMQP;
import io.opentelemetry.api.trace.Span;
import io.opentelemetry.api.trace.SpanKind;
import io.opentelemetry.api.trace.StatusCode;
import io.opentelemetry.context.Context;
import io.wyden.cloudutils.rabbitmq.consumer.ConsumptionResult;
import io.wyden.cloudutils.rabbitmq.consumer.MessageConsumer;
import io.wyden.cloudutils.rabbitmq.integrator.RabbitIntegrator;
import io.wyden.cloudutils.rabbitmq.queue.MatchingCondition;
import io.wyden.cloudutils.rabbitmq.queue.RabbitQueue;
import io.wyden.cloudutils.rabbitmq.queue.RabbitQueueBuilder;
import io.wyden.cloudutils.telemetry.tracing.Tracing;
import io.wyden.cloudutils.telemetry.tracing.otl.RabbitHeadersPropagator;
import io.wyden.orderhistory.model.OrderEventEntity;
import io.wyden.orderhistory.repository.OrderEventRepository;
import io.wyden.orderhistory.service.AsyncOrderStateProcessor;
import io.wyden.published.client.ClientResponse;
import io.wyden.published.oems.OemsResponse;
import io.wyden.published.protobuf.Message;
import io.wyden.cloudutils.rabbitmq.destination.OemsHeader;
import io.wyden.cloudutils.rabbitmq.exchange.RabbitExchange;
import jakarta.annotation.PostConstruct;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.time.OffsetDateTime;
import java.time.ZonedDateTime;
import java.util.Map;

@Component
public class SimplifiedTradingMessageConsumer implements MessageConsumer<Message> {

    private static final Logger LOGGER = LoggerFactory.getLogger(SimplifiedTradingMessageConsumer.class);

    private final RabbitIntegrator rabbitIntegrator;
    private final RabbitExchange<Message> tradingIngressExchange;
    private final OrderEventRepository orderEventRepository;
    private final AsyncOrderStateProcessor asyncProcessor;
    private final Tracing otlTracing;
    private final String queueName;
    private final String consumerName;

    public SimplifiedTradingMessageConsumer(
            RabbitIntegrator rabbitIntegrator,
            RabbitExchange<Message> tradingIngressExchange,
            OrderEventRepository orderEventRepository,
            AsyncOrderStateProcessor asyncProcessor,
            Tracing otlTracing,
            @Value("${order.history.queue.name:order-history-simplified}") String queueName,
            @Value("${order.history.consumer.name:order-history-simplified-consumer}") String consumerName) {
        this.rabbitIntegrator = rabbitIntegrator;
        this.tradingIngressExchange = tradingIngressExchange;
        this.orderEventRepository = orderEventRepository;
        this.asyncProcessor = asyncProcessor;
        this.otlTracing = otlTracing;
        this.queueName = queueName;
        this.consumerName = consumerName;
    }

    @PostConstruct
    void init() {
        declareQueue();
    }

    @Override
    public ConsumptionResult consume(Message message, AMQP.BasicProperties properties) {
        Context parent = otlTracing.loadContext(RabbitHeadersPropagator.create(properties.getHeaders()), RabbitHeadersPropagator.getter());
        try (var ignored = otlTracing.createBaggage(parent)) {
            try (var ignored2 = otlTracing.createSpan("simplified.request.consume", SpanKind.CONSUMER, parent)) {
                return consumeInner(message, properties);
            }
        }
    }

    private ConsumptionResult consumeInner(Message data, AMQP.BasicProperties properties) {
        LOGGER.debug("Received new Trading message. Properties: {}", properties);

        try {
            if (data == null) {
                LOGGER.error("Message parsing failed");
                Span.current().setStatus(StatusCode.ERROR, "Message parsing failed");
                return ConsumptionResult.failureNonRecoverable();
            }

            if (isResponseWithRequest(data)) {
                // Fast path: only store event
                Long eventId = storeEvent(data);
                
                // Trigger async processing
                asyncProcessor.scheduleProcessing(eventId);
                
                LOGGER.debug("Stored event {} for order {}", eventId, extractOrderId(data));
                return ConsumptionResult.consumed();
            } else {
                LOGGER.debug("Skipping message type: {} (not a Response with Request)", data.getClass().getSimpleName());
                return ConsumptionResult.consumed();
            }
        } catch (Exception ex) {
            LOGGER.error("Failed to process message, dropping", ex);
            Span.current().setStatus(StatusCode.ERROR);
            Span.current().recordException(ex);
            return ConsumptionResult.failureNonRecoverable();
        }
    }

    private boolean isResponseWithRequest(Message message) {
        if (message instanceof OemsResponse oemsResponse) {
            return oemsResponse.hasRequest();
        } else if (message instanceof ClientResponse clientResponse) {
            return clientResponse.hasRequest();
        }
        return false;
    }

    private Long storeEvent(Message message) {
        OrderEventEntity event = OrderEventEntity.builder()
            .orderId(extractOrderId(message))
            .messageType(message.getClass().getSimpleName())
            .messageTimestamp(extractMessageTimestamp(message))
            .protoBlob(message.toByteArray())
            .build();

        OrderEventEntity savedEvent = orderEventRepository.save(event);
        return savedEvent.getId();
    }

    private String extractOrderId(Message message) {
        if (message instanceof OemsResponse oemsResponse) {
            return oemsResponse.getOrderId();
        } else if (message instanceof ClientResponse clientResponse) {
            return clientResponse.getOrderId();
        }
        throw new IllegalArgumentException("Unsupported message type for order ID extraction: " + message.getClass());
    }

    private OffsetDateTime extractMessageTimestamp(Message message) {
        if (message instanceof OemsResponse oemsResponse) {
            // Try to get venue timestamp first, fallback to system timestamp
            if (oemsResponse.hasVenueTimestamp() && !oemsResponse.getVenueTimestamp().isEmpty()) {
                return OffsetDateTime.parse(oemsResponse.getVenueTimestamp());
            } else if (oemsResponse.hasSystemTimestamp() && !oemsResponse.getSystemTimestamp().isEmpty()) {
                return OffsetDateTime.parse(oemsResponse.getSystemTimestamp());
            }
        } else if (message instanceof ClientResponse clientResponse) {
            // Try to get venue timestamp first, fallback to system timestamp
            if (clientResponse.hasVenueTimestamp() && !clientResponse.getVenueTimestamp().isEmpty()) {
                return OffsetDateTime.parse(clientResponse.getVenueTimestamp());
            } else if (clientResponse.hasSystemTimestamp() && !clientResponse.getSystemTimestamp().isEmpty()) {
                return OffsetDateTime.parse(clientResponse.getSystemTimestamp());
            }
        }
        
        // Fallback to current time if no timestamp available
        LOGGER.warn("No reliable timestamp found in message, using current time");
        return OffsetDateTime.now();
    }

    private void declareQueue() {
        LOGGER.info("Declaring queue {} for simplified trading messages (OemsResponse, ClientResponse with Request).", queueName);
        RabbitQueue<Message> queue = new RabbitQueueBuilder<>(rabbitIntegrator)
            .setQueueName(queueName)
            .setConsumerName(consumerName)
            .setSingleActiveConsumer(true)
            .declare();

        // Bind only Response messages
        bindQueue(queue, tradingIngressExchange, Map.of(
            OemsHeader.MESSAGE_TYPE.getHeaderName(), ClientResponse.class.getSimpleName()
        ));
        bindQueue(queue, tradingIngressExchange, Map.of(
            OemsHeader.MESSAGE_TYPE.getHeaderName(), OemsResponse.class.getSimpleName()
        ));

        queue.attachConsumer(TradingMessageParser.parser(), this);
    }

    private <T extends Message> void bindQueue(RabbitQueue<Message> queue, RabbitExchange<Message> exchange, Map<String, Object> headers) {
        LOGGER.info("Binding exchange {} and queue {} with headers {}", exchange.getName(), queue.getName(), headers);
        queue.bindWithHeaders(exchange, MatchingCondition.ALL, headers);
    }
}
