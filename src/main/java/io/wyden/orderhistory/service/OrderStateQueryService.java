package io.wyden.orderhistory.service;

import com.google.protobuf.InvalidProtocolBufferException;
import io.wyden.orderhistory.model.OrderEventEntity;
import io.wyden.orderhistory.model.OrderHistorySearchInput;
import io.wyden.orderhistory.repository.OrderEventRepository;
import io.wyden.orderhistory.repository.OrderHistorySearchIndexRepository;
import io.wyden.orderhistory.service.utils.OrderStateMapper;
import io.wyden.published.client.ClientResponse;
import io.wyden.published.oems.OemsResponse;
import io.wyden.published.protobuf.Message;
import io.wyden.published.reporting.OrderState;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;

import java.time.OffsetDateTime;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

@Service
public class OrderStateQueryService {

    private static final Logger LOGGER = LoggerFactory.getLogger(OrderStateQueryService.class);

    private final OrderEventRepository orderEventRepository;
    private final OrderHistorySearchIndexRepository searchIndexRepository;

    public OrderStateQueryService(
            OrderEventRepository orderEventRepository,
            OrderHistorySearchIndexRepository searchIndexRepository) {
        this.orderEventRepository = orderEventRepository;
        this.searchIndexRepository = searchIndexRepository;
    }

    /**
     * Find order states by search criteria
     */
    public List<OrderState> findOrderStates(OrderHistorySearchInput searchInput) {
        LOGGER.debug("Searching for order states with criteria: {}", searchInput);

        // 1. Find order IDs using search index
        List<String> orderIds = findOrderIdsBySearchCriteria(searchInput);
        
        if (orderIds.isEmpty()) {
            return List.of();
        }

        // 2. Get latest events for each order
        List<OrderEventEntity> latestEvents = orderEventRepository.findLatestEventsByOrderIds(orderIds);

        // 3. Build OrderStates from messages (parallel processing)
        return latestEvents.parallelStream()
            .map(this::buildOrderStateFromEvent)
            .filter(Objects::nonNull)
            .collect(Collectors.toList());
    }

    /**
     * Find order states with pagination
     */
    public Page<OrderState> findOrderStates(OrderHistorySearchInput searchInput, Pageable pageable) {
        LOGGER.debug("Searching for order states with criteria: {} and pagination: {}", searchInput, pageable);

        // 1. Count total results
        long totalCount = countOrdersBySearchCriteria(searchInput);
        
        if (totalCount == 0) {
            return new PageImpl<>(List.of(), pageable, 0);
        }

        // 2. Find order IDs with pagination (this is simplified - you might need custom pagination)
        List<String> orderIds = findOrderIdsBySearchCriteria(searchInput);
        
        // Apply pagination manually (in a real implementation, you'd do this in the query)
        int start = (int) pageable.getOffset();
        int end = Math.min(start + pageable.getPageSize(), orderIds.size());
        List<String> paginatedOrderIds = orderIds.subList(start, end);

        // 3. Get latest events for paginated order IDs
        List<OrderEventEntity> latestEvents = orderEventRepository.findLatestEventsByOrderIds(paginatedOrderIds);

        // 4. Build OrderStates
        List<OrderState> orderStates = latestEvents.parallelStream()
            .map(this::buildOrderStateFromEvent)
            .filter(Objects::nonNull)
            .collect(Collectors.toList());

        return new PageImpl<>(orderStates, pageable, totalCount);
    }

    /**
     * Find single order state by order ID
     */
    public Optional<OrderState> findOrderStateByOrderId(String orderId) {
        LOGGER.debug("Finding order state for order ID: {}", orderId);

        Optional<OrderEventEntity> latestEvent = orderEventRepository.findLatestEventByOrderId(orderId);
        
        return latestEvent.map(this::buildOrderStateFromEvent);
    }

    /**
     * Get order history (all events) for a specific order
     */
    public List<OrderState> getOrderHistory(String orderId) {
        LOGGER.debug("Getting order history for order ID: {}", orderId);

        List<OrderEventEntity> events = orderEventRepository.findByOrderIdOrderByMessageTimestampAsc(orderId);

        return events.stream()
            .map(this::buildOrderStateFromEvent)
            .filter(Objects::nonNull)
            .collect(Collectors.toList());
    }

    private List<String> findOrderIdsBySearchCriteria(OrderHistorySearchInput searchInput) {
        // This is a simplified implementation using the basic query method
        // In a real implementation, you'd build dynamic queries based on the search input
        return searchIndexRepository.findOrderIdsBySearchCriteria(
            extractPortfolioId(searchInput),
            extractInstrumentId(searchInput),
            extractOrderStatus(searchInput),
            extractRootOrderId(searchInput),
            extractParentOrderId(searchInput),
            extractStartTime(searchInput),
            extractEndTime(searchInput)
        );
    }

    private long countOrdersBySearchCriteria(OrderHistorySearchInput searchInput) {
        return searchIndexRepository.countBySearchCriteria(
            extractPortfolioId(searchInput),
            extractInstrumentId(searchInput),
            extractOrderStatus(searchInput),
            extractRootOrderId(searchInput),
            extractParentOrderId(searchInput),
            extractStartTime(searchInput),
            extractEndTime(searchInput)
        );
    }

    private OrderState buildOrderStateFromEvent(OrderEventEntity event) {
        if (event == null) {
            return null;
        }

        try {
            Message response = deserializeProto(event.getProtoBlob(), event.getMessageType());
            return buildOrderStateFromResponse(response);
        } catch (Exception e) {
            LOGGER.error("Failed to build OrderState from event {}: {}", event.getId(), e.getMessage(), e);
            return null;
        }
    }

    private Message deserializeProto(byte[] protoBlob, String messageType) throws InvalidProtocolBufferException {
        switch (messageType) {
            case "OemsResponse":
                return OemsResponse.parseFrom(protoBlob);
            case "ClientResponse":
                return ClientResponse.parseFrom(protoBlob);
            default:
                throw new IllegalArgumentException("Unsupported message type: " + messageType);
        }
    }

    private OrderState buildOrderStateFromResponse(Message response) {
        if (response instanceof OemsResponse oemsResponse) {
            return OrderStateMapper.buildFromOemsRequestAndResponse(oemsResponse.getRequest(), oemsResponse);
        } else if (response instanceof ClientResponse clientResponse) {
            return OrderStateMapper.buildFromClientRequestAndResponse(clientResponse.getRequest(), clientResponse);
        }
        throw new IllegalArgumentException("Unsupported message type: " + response.getClass());
    }

    // Helper methods to extract search criteria from OrderHistorySearchInput
    // These would need to be implemented based on your actual OrderHistorySearchInput structure

    private String extractPortfolioId(OrderHistorySearchInput searchInput) {
        // TODO: Implement based on your OrderHistorySearchInput structure
        return null;
    }

    private String extractInstrumentId(OrderHistorySearchInput searchInput) {
        // TODO: Implement based on your OrderHistorySearchInput structure
        return null;
    }

    private String extractOrderStatus(OrderHistorySearchInput searchInput) {
        // TODO: Implement based on your OrderHistorySearchInput structure
        return null;
    }

    private String extractRootOrderId(OrderHistorySearchInput searchInput) {
        // TODO: Implement based on your OrderHistorySearchInput structure
        return null;
    }

    private String extractParentOrderId(OrderHistorySearchInput searchInput) {
        // TODO: Implement based on your OrderHistorySearchInput structure
        return null;
    }

    private OffsetDateTime extractStartTime(OrderHistorySearchInput searchInput) {
        // TODO: Implement based on your OrderHistorySearchInput structure
        return null;
    }

    private OffsetDateTime extractEndTime(OrderHistorySearchInput searchInput) {
        // TODO: Implement based on your OrderHistorySearchInput structure
        return null;
    }
}
