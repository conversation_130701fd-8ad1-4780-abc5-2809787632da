package io.wyden.orderhistory.service;

import io.micrometer.core.instrument.MeterRegistry;
import io.micrometer.core.instrument.Timer;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import java.time.Duration;
import java.time.Instant;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * Monitor query performance to determine when indexes are needed
 */
@Component
public class QueryPerformanceMonitor {

    private static final Logger LOGGER = LoggerFactory.getLogger(QueryPerformanceMonitor.class);

    private final MeterRegistry meterRegistry;
    private final Map<String, QueryStats> queryStats = new ConcurrentHashMap<>();

    public QueryPerformanceMonitor(MeterRegistry meterRegistry) {
        this.meterRegistry = meterRegistry;
    }

    /**
     * Record query execution time and analyze if indexes are needed
     */
    public <T> T recordQuery(String queryType, String criteria, QueryExecutor<T> executor) {
        Instant start = Instant.now();
        Timer.Sample sample = Timer.start(meterRegistry);
        
        try {
            T result = executor.execute();
            
            Duration duration = Duration.between(start, Instant.now());
            sample.stop(Timer.builder("order_history.query.duration")
                .tag("query_type", queryType)
                .tag("criteria", criteria)
                .register(meterRegistry));
            
            recordQueryStats(queryType, criteria, duration);
            
            // Alert if query is slow
            if (duration.toMillis() > 100) { // 100ms threshold
                LOGGER.warn("Slow query detected: {} with criteria {} took {}ms", 
                    queryType, criteria, duration.toMillis());
                
                if (duration.toMillis() > 1000) { // 1s threshold
                    suggestIndex(queryType, criteria, duration);
                }
            }
            
            return result;
            
        } catch (Exception e) {
            sample.stop(Timer.builder("order_history.query.duration")
                .tag("query_type", queryType)
                .tag("criteria", criteria)
                .tag("status", "error")
                .register(meterRegistry));
            throw e;
        }
    }

    private void recordQueryStats(String queryType, String criteria, Duration duration) {
        String key = queryType + ":" + criteria;
        queryStats.compute(key, (k, stats) -> {
            if (stats == null) {
                return new QueryStats(1, duration.toMillis(), duration.toMillis(), duration.toMillis());
            } else {
                return new QueryStats(
                    stats.count + 1,
                    Math.min(stats.minMs, duration.toMillis()),
                    Math.max(stats.maxMs, duration.toMillis()),
                    (stats.avgMs * stats.count + duration.toMillis()) / (stats.count + 1)
                );
            }
        });
    }

    private void suggestIndex(String queryType, String criteria, Duration duration) {
        String suggestion = generateIndexSuggestion(queryType, criteria);
        
        LOGGER.error("""
            PERFORMANCE ALERT: Very slow query detected!
            Query Type: {}
            Criteria: {}
            Duration: {}ms
            Suggested Index: {}
            
            Consider adding this index to improve performance:
            {}
            """, 
            queryType, criteria, duration.toMillis(), suggestion, suggestion);
    }

    private String generateIndexSuggestion(String queryType, String criteria) {
        // Simple heuristics for index suggestions
        if (criteria.contains("portfolio_id")) {
            if (criteria.contains("order_status")) {
                return "CREATE INDEX idx_search_portfolio_status ON order_history_search_index (portfolio_id, order_status);";
            } else if (criteria.contains("updated_at")) {
                return "CREATE INDEX idx_search_portfolio_updated ON order_history_search_index (portfolio_id, updated_at DESC);";
            } else {
                return "CREATE INDEX idx_search_portfolio_id ON order_history_search_index (portfolio_id);";
            }
        } else if (criteria.contains("instrument_id")) {
            return "CREATE INDEX idx_search_instrument_id ON order_history_search_index (instrument_id);";
        } else if (criteria.contains("root_order_id")) {
            return "CREATE INDEX idx_search_root_order_id ON order_history_search_index (root_order_id);";
        } else if (criteria.contains("updated_at")) {
            return "CREATE INDEX idx_search_updated_at ON order_history_search_index (updated_at DESC);";
        } else {
            return "-- Analyze query pattern: " + criteria;
        }
    }

    /**
     * Get query statistics for analysis
     */
    public Map<String, QueryStats> getQueryStats() {
        return Map.copyOf(queryStats);
    }

    /**
     * Get top slow queries
     */
    public void logSlowQueries() {
        queryStats.entrySet().stream()
            .filter(entry -> entry.getValue().avgMs > 50) // 50ms threshold
            .sorted((e1, e2) -> Long.compare(e2.getValue().avgMs, e1.getValue().avgMs))
            .limit(10)
            .forEach(entry -> {
                QueryStats stats = entry.getValue();
                LOGGER.info("Slow query: {} - Count: {}, Avg: {}ms, Max: {}ms", 
                    entry.getKey(), stats.count, stats.avgMs, stats.maxMs);
            });
    }

    @FunctionalInterface
    public interface QueryExecutor<T> {
        T execute();
    }

    public static class QueryStats {
        public final long count;
        public final long minMs;
        public final long maxMs;
        public final long avgMs;

        public QueryStats(long count, long minMs, long maxMs, long avgMs) {
            this.count = count;
            this.minMs = minMs;
            this.maxMs = maxMs;
            this.avgMs = avgMs;
        }

        @Override
        public String toString() {
            return String.format("QueryStats{count=%d, min=%dms, max=%dms, avg=%dms}", 
                count, minMs, maxMs, avgMs);
        }
    }
}
