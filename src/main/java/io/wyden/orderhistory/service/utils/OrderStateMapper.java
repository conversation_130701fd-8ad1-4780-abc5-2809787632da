package io.wyden.orderhistory.service.utils;

import io.wyden.published.client.ClientRequest;
import io.wyden.published.client.ClientResponse;
import io.wyden.published.oems.OemsRequest;
import io.wyden.published.oems.OemsResponse;
import io.wyden.published.reporting.OrderState;

import io.wyden.cloudutils.tools.DateUtils;
import io.wyden.orderhistory.model.OrderStateEntity;
import io.wyden.published.client.ClientOrderStatus;
import io.wyden.published.client.ClientRequest;
import io.wyden.published.client.ClientResponse;
import io.wyden.published.oems.OemsOrderCategory;
import io.wyden.published.oems.OemsOrderType;
import io.wyden.published.oems.OemsRequest;
import io.wyden.published.oems.OemsResponse;
import io.wyden.published.referencedata.Portfolio;
import io.wyden.published.referencedata.PortfolioType;
import io.wyden.published.reporting.OrderState;
import io.wyden.published.reporting.OrderStatus;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.time.OffsetDateTime;
import java.time.ZonedDateTime;
import java.util.List;
import java.util.Objects;
import java.util.UUID;

import static io.wyden.orderhistory.service.utils.ProtobufUtils.clientToOrderStatus;
import static io.wyden.orderhistory.service.utils.ProtobufUtils.oemsToOrderStatus;
import static io.wyden.orderhistory.service.utils.ProtobufUtils.toInstrumentType;
import static io.wyden.orderhistory.service.utils.ProtobufUtils.toOrderCategory;
import static io.wyden.orderhistory.service.utils.ProtobufUtils.toOrderStatus;
import static io.wyden.orderhistory.service.utils.ProtobufUtils.toOrderType;
import static io.wyden.orderhistory.service.utils.ProtobufUtils.toSide;
import static io.wyden.orderhistory.service.utils.ProtobufUtils.toTiff;
import static io.wyden.published.oems.OemsInstrumentType.INSTRUMENT_TYPE_UNSPECIFIED;
import static org.apache.commons.lang3.StringUtils.EMPTY;

public final class OrderStateMapper {

    private static final Logger LOGGER = LoggerFactory.getLogger(OrderStateMapper.class);
    private static final String ZERO_STRING = "0";

    public static OrderStateEntity asOrderState(ClientRequest orderRequest, Portfolio portfolio) {
        OrderStateEntity.OrderStateEntityBuilder builder = OrderStateEntity.OrderStateEntityBuilder.builder()
            .id(UUID.randomUUID().toString())
            .orderId(orderRequest.getOrderId())
            .clientId(orderRequest.getClientId())
            .clOrderId(orderRequest.getClOrderId())
            .origClOrderId(orderRequest.getOrigClOrderId())
            .portfolioId(orderRequest.getPortfolioId())
            .portfolioType(portfolio != null && !PortfolioType.PORTFOLIO_TYPE_UNSPECIFIED.equals(portfolio.getPortfolioType()) ? portfolio.getPortfolioType().name() : null)
            .orderStatus(OrderStatus.PENDING_NEW.name())
            .orderQty(orderRequest.getQuantity())
            .currency(orderRequest.getCurrency())
            .limitPrice(orderRequest.getPrice())
            .stopPrice(orderRequest.getStopPrice())
            .tif(orderRequest.getTif().toString())
            .filledQty(ZERO_STRING)
            .remainingQty(orderRequest.getQuantity())
            .lastQty(ZERO_STRING)
            .avgPrice(ZERO_STRING)
            .lastPrice(ZERO_STRING)
            .reason(EMPTY)
            .side(orderRequest.getSide().toString())
            .createdAt(StringUtils.isNotEmpty(orderRequest.getCreatedAt()) ? ZonedDateTime.parse(orderRequest.getCreatedAt()).toOffsetDateTime() : OffsetDateTime.now())
            .updatedAt(StringUtils.isNotEmpty(orderRequest.getCreatedAt()) ? ZonedDateTime.parse(orderRequest.getCreatedAt()).toOffsetDateTime() : OffsetDateTime.now())
            .lastRequestResult(EMPTY)
            .expireTime(mapExpireTime(orderRequest.getExpireTime()))
            .orderType(orderRequest.getOrderType().name())
            .venueAccounts(orderRequest.getVenueAccountsList())
            .rootOrderId(orderRequest.getOrderId())
            .orderCategory(OemsOrderCategory.ORDER_CATEGORY_UNSPECIFIED.name())
            .sequenceNumber(1);
        if (StringUtils.isNotEmpty(orderRequest.getInstrumentId())) {
            builder.instrumentId(orderRequest.getInstrumentId());
        } else if (StringUtils.isNotEmpty(orderRequest.getSymbol()) && StringUtils.isEmpty(orderRequest.getInstrumentId())) {
            builder.symbol(orderRequest.getSymbol());
            builder.instrumentType(orderRequest.getInstrumentType().name());
        } else {
            LOGGER.warn("No instrument id or symbol provided, skipping ClientRequest");
            return null;
        }

        return builder.build();
    }

    public static OrderStateEntity merge(OrderStateEntity orderStateEntity, ClientResponse response, Portfolio counterPortfolio) {
        return OrderStateEntity.OrderStateEntityBuilder.toBuilder(orderStateEntity)
            .id(UUID.randomUUID().toString())
            .orderStatus(clientToOrderStatus(response.getOrderStatus()).name())
            .orderQty(response.getOrderQty())
            .currency(StringUtils.isNotEmpty(response.getCurrency()) ? response.getCurrency() : orderStateEntity.currency())
            .filledQty(response.getCumQty())
            .remainingQty(response.getLeavesQty())
            .lastQty(response.getLastQty())
            .avgPrice(response.getAvgPrice())
            .lastPrice(response.getLastPrice())
            .reason(response.getReason())
            .lastRequestResult(response.getRequestResult().getValueDescriptor().getName())
            .updatedAt(StringUtils.isNotEmpty(response.getTimestamp()) ? ZonedDateTime.parse(response.getTimestamp()).toOffsetDateTime() :
                StringUtils.isNotEmpty(response.getMetadata().getCreatedAt()) ? ZonedDateTime.parse(response.getMetadata().getCreatedAt()).toOffsetDateTime() : OffsetDateTime.now())
            .underlyingVenueAccount(response.getUnderlyingVenueAccount())
            .counterPortfolioId(response.getCounterPortfolioId())
            .counterPortfolioType(counterPortfolio != null && !PortfolioType.PORTFOLIO_TYPE_UNSPECIFIED.equals(counterPortfolio.getPortfolioType()) ? counterPortfolio.getPortfolioType().name() : null)
            .sequenceNumber(incrementSequenceNumber(orderStateEntity.sequenceNumber()))
            .build();
    }

    public static OrderStateEntity asOrderState(OemsRequest orderRequest, Portfolio portfolio) {
        return OrderStateEntity.OrderStateEntityBuilder.builder()
            .id(UUID.randomUUID().toString())
            .orderId(orderRequest.getOrderId())
            .clientId(orderRequest.getClientId())
            .portfolioId(orderRequest.getPortfolioId())
            .portfolioType(portfolio != null && !PortfolioType.PORTFOLIO_TYPE_UNSPECIFIED.equals(portfolio.getPortfolioType()) ? portfolio.getPortfolioType().name() : null)
            .orderType(orderRequest.getOrderType().name())
            .orderStatus(OrderStatus.PENDING_NEW.name())
            .orderQty(orderRequest.getQuantity())
            .currency(StringUtils.firstNonEmpty(orderRequest.getCurrency(), orderRequest.getBaseCurrency()))
            .limitPrice(orderRequest.getPrice())
            .stopPrice(orderRequest.getStopPrice())
            .tif(orderRequest.getTif().toString())
            .filledQty(ZERO_STRING)
            .remainingQty(orderRequest.getQuantity())
            .lastQty(ZERO_STRING)
            .avgPrice(ZERO_STRING)
            .lastPrice(ZERO_STRING)
            .reason(EMPTY)
            .lastRequestResult(EMPTY)
            .side(orderRequest.getSide().toString())
            .instrumentId(orderRequest.getInstrumentId())
            .symbol(orderRequest.getSymbol())
            .instrumentType(!orderRequest.getInstrumentType().equals(INSTRUMENT_TYPE_UNSPECIFIED) ? orderRequest.getInstrumentType().name() : null)
            .venueAccounts(StringUtils.isNotEmpty(orderRequest.getVenueAccount()) ? List.of(orderRequest.getVenueAccount()) : orderRequest.getVenueAccountsList())
            .createdAt(StringUtils.isNotEmpty(orderRequest.getMetadata().getCreatedAt()) ? ZonedDateTime.parse(orderRequest.getMetadata().getCreatedAt()).toOffsetDateTime() : OffsetDateTime.now())
            .updatedAt(StringUtils.isNotEmpty(orderRequest.getMetadata().getCreatedAt()) ? ZonedDateTime.parse(orderRequest.getMetadata().getCreatedAt()).toOffsetDateTime() : OffsetDateTime.now())
            .expireTime(mapExpireTime(orderRequest.getExpireTime()))
            .orderCategory(orderRequest.getOrderCategory().name())
            .parentOrderId(orderRequest.getParentOrderId())
            .sequenceNumber(1)
            .rootOrderId(orderRequest.getRootOrderId())
            .clOrderId(orderRequest.getClientRootOrderId())
            .build();
    }

    public static OrderStateEntity asOrderState(OemsResponse oemsResponse, Portfolio portfolio) {
        return OrderStateEntity.OrderStateEntityBuilder.builder()
            .id(UUID.randomUUID().toString())
            .orderId(oemsResponse.getOrderId())
            .clientId(oemsResponse.getClientId())
            .portfolioId(oemsResponse.getPortfolioId())
            .portfolioType(portfolio != null && !PortfolioType.PORTFOLIO_TYPE_UNSPECIFIED.equals(portfolio.getPortfolioType()) ? portfolio.getPortfolioType().name() : null)
            .orderType(OemsOrderType.ORDER_TYPE_UNSPECIFIED.name())
            .orderStatus(oemsToOrderStatus(oemsResponse.getOrderStatus()).name())
            .orderQty(oemsResponse.getOrderQty())
            .lastQty(oemsResponse.getLastQty())
            .filledQty(oemsResponse.getCumQty())
            .remainingQty(oemsResponse.getLeavesQty())
            .avgPrice(oemsResponse.getAvgPrice())
            .lastPrice(oemsResponse.getLastPrice())
            .currency(StringUtils.firstNonEmpty(oemsResponse.getCurrency(), oemsResponse.getBaseCurrency()))
            .tif(oemsResponse.getTif().toString())
            .reason(oemsResponse.getReason())
            .lastRequestResult(oemsResponse.getRequestResult().getValueDescriptor().getName())
            .side(oemsResponse.getSide().toString())
            .instrumentId(oemsResponse.getInstrumentId())
            .symbol(oemsResponse.getSymbol())
            .instrumentType(!oemsResponse.getInstrumentType().equals(INSTRUMENT_TYPE_UNSPECIFIED) ? oemsResponse.getInstrumentType().name() : null)
            .venueAccounts(StringUtils.isNotEmpty(oemsResponse.getVenueAccount()) ? List.of(oemsResponse.getVenueAccount()) : List.of())
            .createdAt(StringUtils.isNotEmpty(oemsResponse.getMetadata().getCreatedAt()) ? ZonedDateTime.parse(oemsResponse.getMetadata().getCreatedAt()).toOffsetDateTime() : OffsetDateTime.now())
            .updatedAt(StringUtils.isNotEmpty(oemsResponse.getMetadata().getCreatedAt()) ? ZonedDateTime.parse(oemsResponse.getMetadata().getCreatedAt()).toOffsetDateTime() : OffsetDateTime.now())
            .expireTime(mapExpireTime(oemsResponse.getExpireTime()))
            .orderCategory(oemsResponse.getOrderCategory().name())
            .parentOrderId(oemsResponse.getParentOrderId())
            .rootOrderId(oemsResponse.getRootOrderId())
            .clOrderId(oemsResponse.getClientRootOrderId())
            .extOrderId(oemsResponse.getExtId())
            .sequenceNumber(1)
            .build();
    }

    public static OrderStateEntity merge(OrderStateEntity orderStateEntity, OemsRequest oemsRequest) {
        return OrderStateEntity.OrderStateEntityBuilder.toBuilder(orderStateEntity)
            .id(UUID.randomUUID().toString())
            .remainingQty(oemsRequest.getQuantity())
            .currency(StringUtils.firstNonEmpty(oemsRequest.getCurrency(), oemsRequest.getBaseCurrency(), orderStateEntity.currency()))
            .updatedAt(StringUtils.isNotEmpty(oemsRequest.getMetadata().getCreatedAt()) ? ZonedDateTime.parse(oemsRequest.getMetadata().getCreatedAt()).toOffsetDateTime() : OffsetDateTime.now())
            .expireTime(mapExpireTime(oemsRequest.getExpireTime()))
            .orderCategory(oemsRequest.getOrderCategory().name())
            .parentOrderId(oemsRequest.getParentOrderId())
            .sequenceNumber(incrementSequenceNumber(orderStateEntity.sequenceNumber()))
            .rootOrderId(oemsRequest.getRootOrderId())
            .build();
    }

    public static OrderStateEntity merge(OrderStateEntity orderStateEntity, OemsResponse executionReportResponse) {
        return OrderStateEntity.OrderStateEntityBuilder.toBuilder(orderStateEntity)
            .id(UUID.randomUUID().toString())
            .orderStatus(oemsToOrderStatus(executionReportResponse.getOrderStatus()).name())
            .orderQty(executionReportResponse.getOrderQty())
            .currency(StringUtils.firstNonEmpty(executionReportResponse.getCurrency(), executionReportResponse.getCurrency(), executionReportResponse.getBaseCurrency(), orderStateEntity.currency()))
            .filledQty(executionReportResponse.getCumQty())
            .remainingQty(executionReportResponse.getLeavesQty())
            .lastQty(executionReportResponse.getLastQty())
            .avgPrice(executionReportResponse.getAvgPrice())
            .lastPrice(executionReportResponse.getLastPrice())
            .reason(executionReportResponse.getReason())
            .lastRequestResult(executionReportResponse.getRequestResult().getValueDescriptor().getName())
            .updatedAt(StringUtils.isNotEmpty(executionReportResponse.getSystemTimestamp()) ? ZonedDateTime.parse(executionReportResponse.getSystemTimestamp()).toOffsetDateTime() :
                StringUtils.isNotEmpty(executionReportResponse.getMetadata().getCreatedAt()) ? ZonedDateTime.parse(executionReportResponse.getMetadata().getCreatedAt()).toOffsetDateTime() : OffsetDateTime.now())
            .venueTimestamp(StringUtils.isNotEmpty(executionReportResponse.getVenueTimestamp()) ? ZonedDateTime.parse(executionReportResponse.getVenueTimestamp()).toOffsetDateTime() : OffsetDateTime.now())
            .rootOrderId(executionReportResponse.getRootOrderId())
            .orderCategory(executionReportResponse.getOrderCategory().name())
            .parentOrderId(executionReportResponse.getParentOrderId())
            .extOrderId(executionReportResponse.getExtId())
            .sequenceNumber(incrementSequenceNumber(orderStateEntity.sequenceNumber()))
            .build();
    }

    public static @NotNull OrderStateEntity updateOrderStateToCanceled(OrderStateEntity orderStateEntity) {
        return OrderStateEntity.OrderStateEntityBuilder.toBuilder(orderStateEntity)
            .id(UUID.randomUUID().toString())
            .orderStatus(OrderStatus.CANCELED.name())
            .remainingQty(ZERO_STRING)
            .sequenceNumber(incrementSequenceNumber(orderStateEntity.sequenceNumber()))
            .build();
    }

    public static @NotNull OrderStateEntity updateOrderStateStatus(OrderStateEntity orderStateEntity, ClientOrderStatus orderStatus) {
        return OrderStateEntity.OrderStateEntityBuilder.toBuilder(orderStateEntity)
            .id(UUID.randomUUID().toString())
            .orderStatus(orderStatus.name())
            .sequenceNumber(incrementSequenceNumber(orderStateEntity.sequenceNumber()))
            .build();
    }

    public static OrderState entityToProto(OrderStateEntity orderStateEntity) {
        OrderState.Builder builder = OrderState.newBuilder()
            .setId(ObjectUtils.firstNonNull(orderStateEntity.id(), EMPTY))
            .setOrderId(orderStateEntity.orderId())
            .setClientId(orderStateEntity.clientId())
            .setClOrderId(ObjectUtils.firstNonNull(orderStateEntity.clOrderId(), EMPTY))
            .setOrigClOrderId(ObjectUtils.firstNonNull(orderStateEntity.origClOrderId(), EMPTY))
            .setPortfolioId(orderStateEntity.portfolioId())
            .setOrderStatus(toOrderStatus(orderStateEntity.orderStatus()))
            .setOrderQty(orderStateEntity.orderQty())
            .setCurrency(ObjectUtils.firstNonNull(orderStateEntity.currency(), EMPTY))
            .setLimitPrice(ObjectUtils.firstNonNull(orderStateEntity.limitPrice(), EMPTY))
            .setStopPrice(ObjectUtils.firstNonNull(orderStateEntity.stopPrice(), EMPTY))
            .setTif(toTiff(orderStateEntity.tif()))
            .setFilledQty(ObjectUtils.firstNonNull(orderStateEntity.filledQty(), EMPTY))
            .setRemainingQty(orderStateEntity.remainingQty())
            .setLastQty(ObjectUtils.firstNonNull(orderStateEntity.lastQty(), EMPTY))
            .setAvgPrice(ObjectUtils.firstNonNull(orderStateEntity.avgPrice(), EMPTY))
            .setLastPrice(ObjectUtils.firstNonNull(orderStateEntity.lastPrice(), EMPTY))
            .setReason(ObjectUtils.firstNonNull(orderStateEntity.reason(), EMPTY))
            .setSide(toSide(orderStateEntity.side()))
            .setInstrumentId(ObjectUtils.firstNonNull(orderStateEntity.instrumentId(), EMPTY))
            .setVenueTimestamp(Objects.nonNull(orderStateEntity.venueTimestamp()) ? orderStateEntity.venueTimestamp().toString() : EMPTY)
            .setCreatedAt(Objects.nonNull(orderStateEntity.createdAt()) ? orderStateEntity.createdAt().toString() : EMPTY)
            .setUpdatedAt(Objects.nonNull(orderStateEntity.updatedAt()) ? orderStateEntity.updatedAt().toString() : EMPTY)
            .setLastRequestResult(ObjectUtils.firstNonNull(orderStateEntity.lastRequestResult(), EMPTY))
            .setSequenceNumber(ObjectUtils.firstNonNull(orderStateEntity.sequenceNumber(), 0))
            .setExpireTime(Objects.nonNull(orderStateEntity.expireTime()) ? DateUtils.toFixUtcTime(orderStateEntity.expireTime().toZonedDateTime()) : EMPTY)
            .setOrderCategory(toOrderCategory(orderStateEntity.orderCategory()))
            .setParentOrderId(ObjectUtils.firstNonNull(orderStateEntity.parentOrderId(), EMPTY))
            .setOrderType(toOrderType(orderStateEntity.orderType()))
            .setSymbol(ObjectUtils.firstNonNull(orderStateEntity.symbol(), EMPTY))
            .setInstrumentType(toInstrumentType(orderStateEntity.instrumentType()))
            .addAllVenueAccounts(ObjectUtils.firstNonNull(orderStateEntity.venueAccounts(), List.of()))
            .setUnderlyingVenueAccount(ObjectUtils.firstNonNull(orderStateEntity.underlyingVenueAccount(), EMPTY))
            .setRootOrderId(ObjectUtils.firstNonNull(orderStateEntity.rootOrderId(), EMPTY))
            .setCounterPortfolioId(ObjectUtils.firstNonNull(orderStateEntity.counterPortfolioId(), EMPTY))
            .setExtOrderId(ObjectUtils.firstNonNull(orderStateEntity.extOrderId(), EMPTY));

        if (orderStateEntity.venueAccounts().size() == 1) {
            builder.setVenueAccountId(orderStateEntity.venueAccounts().get(0));
        }

        return builder.build();
    }

    public static OffsetDateTime mapExpireTime(String expireTime) {
        if (StringUtils.isNotEmpty(expireTime)) {
            ZonedDateTime expireDateTime = DateUtils.fromFixUtcTime(expireTime);
            assert expireDateTime != null;
            return expireDateTime.toOffsetDateTime();
        }
        return null;
    }

    private static int incrementSequenceNumber(Integer sequenceNumber) {
        return sequenceNumber + 1;
    }

    // New methods for the refactored architecture

    /**
     * Build OrderState from OemsRequest and OemsResponse
     */
    public static OrderState buildFromOemsRequestAndResponse(OemsRequest request, OemsResponse response) {
        return OrderStateMapperExtensions.buildFromOemsRequestAndResponse(request, response);
    }

    /**
     * Build OrderState from ClientRequest and ClientResponse
     */
    public static OrderState buildFromClientRequestAndResponse(ClientRequest request, ClientResponse response) {
        return OrderStateMapperExtensions.buildFromClientRequestAndResponse(request, response);
    }
}
