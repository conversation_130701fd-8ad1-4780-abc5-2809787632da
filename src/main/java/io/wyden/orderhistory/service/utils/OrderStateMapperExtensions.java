package io.wyden.orderhistory.service.utils;

import io.wyden.published.client.ClientRequest;
import io.wyden.published.client.ClientResponse;
import io.wyden.published.oems.OemsRequest;
import io.wyden.published.oems.OemsResponse;
import io.wyden.published.reporting.OrderState;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.time.ZonedDateTime;
import java.util.UUID;

/**
 * Extensions to OrderStateMapper for the new architecture
 * These methods build OrderState by applying Response values on Request
 */
public class OrderStateMapperExtensions {

    private static final Logger LOGGER = LoggerFactory.getLogger(OrderStateMapperExtensions.class);

    /**
     * Build OrderState from OemsRequest and OemsResponse
     * The Response contains the Request, so we apply Response values on the embedded Request
     */
    public static OrderState buildFromOemsRequestAndResponse(OemsRequest request, OemsResponse response) {
        try {
            return OrderState.newBuilder()
                .setId(UUID.randomUUID().toString())
                
                // Core identifiers from Request
                .setOrderId(request.getOrderId())
                .setClientId(request.getClientId())
                .setClOrderId(request.getClOrderId())
                .setPortfolioId(request.getPortfolioId())
                .setInstrumentId(request.getInstrumentId())
                .setParentOrderId(request.getParentOrderId())
                .setRootOrderId(request.getRootOrderId())
                
                // Order details from Request
                .setOrderQty(request.getQuantity())
                .setLimitPrice(request.getLimitPrice())
                .setStopPrice(request.getStopPrice())
                .setSide(mapOemsSide(request.getSide()))
                .setTif(mapOemsTif(request.getTif()))
                .setOrderType(request.getOrderType().name())
                .setOrderCategory(request.getOrderCategory().name())
                
                // Execution details from Response (these override Request values)
                .setOrderStatus(mapOemsOrderStatus(response.getOrderStatus()))
                .setFilledQty(response.getCumQty())
                .setRemainingQty(response.getLeavesQty())
                .setLastQty(response.getLastQty())
                .setAvgPrice(response.getAvgPrice())
                .setLastPrice(response.getLastPrice())
                .setReason(response.getText())
                
                // Timestamps from Response
                .setVenueTimestamp(response.getVenueTimestamp())
                .setCreatedAt(request.hasMetadata() ? request.getMetadata().getCreatedAt() : getCurrentIsoTime())
                .setUpdatedAt(response.hasMetadata() ? response.getMetadata().getCreatedAt() : getCurrentIsoTime())
                
                // Additional fields from Response
                .setExtOrderId(response.getExtId())
                .setCurrency(response.getCurrency())
                
                .build();
                
        } catch (Exception e) {
            LOGGER.error("Failed to build OrderState from OemsRequest and OemsResponse: {}", e.getMessage(), e);
            throw new RuntimeException("Failed to build OrderState", e);
        }
    }

    /**
     * Build OrderState from ClientRequest and ClientResponse
     * The Response contains the Request, so we apply Response values on the embedded Request
     */
    public static OrderState buildFromClientRequestAndResponse(ClientRequest request, ClientResponse response) {
        try {
            return OrderState.newBuilder()
                .setId(UUID.randomUUID().toString())
                
                // Core identifiers from Request
                .setOrderId(request.getOrderId())
                .setClientId(request.getClientId())
                .setClOrderId(request.getClOrderId())
                .setOrigClOrderId(request.getOrigClOrderId())
                .setPortfolioId(request.getPortfolioId())
                .setInstrumentId(request.getInstrumentId())
                .setParentOrderId(request.getParentOrderId())
                .setRootOrderId(request.getRootOrderId())
                
                // Order details from Request
                .setOrderQty(request.getQuantity())
                .setLimitPrice(request.getLimitPrice())
                .setStopPrice(request.getStopPrice())
                .setSide(mapClientSide(request.getSide()))
                .setTif(mapClientTif(request.getTif()))
                .setOrderType(request.getOrderType().name())
                .setOrderCategory(request.getOrderCategory().name())
                
                // Execution details from Response (these override Request values)
                .setOrderStatus(mapClientOrderStatus(response.getOrderStatus()))
                .setFilledQty(response.getCumQty())
                .setRemainingQty(response.getLeavesQty())
                .setLastQty(response.getLastQty())
                .setAvgPrice(response.getAvgPrice())
                .setLastPrice(response.getLastPrice())
                .setReason(response.getText())
                
                // Timestamps from Response
                .setVenueTimestamp(response.getVenueTimestamp())
                .setCreatedAt(request.hasMetadata() ? request.getMetadata().getCreatedAt() : getCurrentIsoTime())
                .setUpdatedAt(response.hasMetadata() ? response.getMetadata().getCreatedAt() : getCurrentIsoTime())
                
                // Additional fields from Response
                .setExtOrderId(response.getExtId())
                .setCurrency(response.getCurrency())
                
                .build();
                
        } catch (Exception e) {
            LOGGER.error("Failed to build OrderState from ClientRequest and ClientResponse: {}", e.getMessage(), e);
            throw new RuntimeException("Failed to build OrderState", e);
        }
    }

    // Helper methods for mapping enums - these would need to be implemented based on your actual proto definitions

    private static io.wyden.published.reporting.OrderStatus mapOemsOrderStatus(io.wyden.published.oems.OemsOrderStatus status) {
        // TODO: Implement mapping from OemsOrderStatus to reporting.OrderStatus
        try {
            return io.wyden.published.reporting.OrderStatus.valueOf(status.name());
        } catch (IllegalArgumentException e) {
            LOGGER.warn("Unknown OemsOrderStatus: {}, defaulting to STATUS_UNKNOWN", status);
            return io.wyden.published.reporting.OrderStatus.STATUS_UNKNOWN;
        }
    }

    private static io.wyden.published.reporting.OrderStatus mapClientOrderStatus(io.wyden.published.client.ClientOrderStatus status) {
        // TODO: Implement mapping from ClientOrderStatus to reporting.OrderStatus
        try {
            return io.wyden.published.reporting.OrderStatus.valueOf(status.name());
        } catch (IllegalArgumentException e) {
            LOGGER.warn("Unknown ClientOrderStatus: {}, defaulting to STATUS_UNKNOWN", status);
            return io.wyden.published.reporting.OrderStatus.STATUS_UNKNOWN;
        }
    }

    private static io.wyden.published.reporting.Side mapOemsSide(io.wyden.published.oems.OemsSide side) {
        // TODO: Implement mapping from OemsSide to reporting.Side
        try {
            return io.wyden.published.reporting.Side.valueOf(side.name());
        } catch (IllegalArgumentException e) {
            LOGGER.warn("Unknown OemsSide: {}, defaulting to SIDE_UNKNOWN", side);
            return io.wyden.published.reporting.Side.SIDE_UNKNOWN;
        }
    }

    private static io.wyden.published.reporting.Side mapClientSide(io.wyden.published.client.ClientSide side) {
        // TODO: Implement mapping from ClientSide to reporting.Side
        try {
            return io.wyden.published.reporting.Side.valueOf(side.name());
        } catch (IllegalArgumentException e) {
            LOGGER.warn("Unknown ClientSide: {}, defaulting to SIDE_UNKNOWN", side);
            return io.wyden.published.reporting.Side.SIDE_UNKNOWN;
        }
    }

    private static io.wyden.published.reporting.TIF mapOemsTif(io.wyden.published.oems.OemsTIF tif) {
        // TODO: Implement mapping from OemsTIF to reporting.TIF
        try {
            return io.wyden.published.reporting.TIF.valueOf(tif.name());
        } catch (IllegalArgumentException e) {
            LOGGER.warn("Unknown OemsTIF: {}, defaulting to TIF_UNKNOWN", tif);
            return io.wyden.published.reporting.TIF.TIF_UNKNOWN;
        }
    }

    private static io.wyden.published.reporting.TIF mapClientTif(io.wyden.published.client.ClientTIF tif) {
        // TODO: Implement mapping from ClientTIF to reporting.TIF
        try {
            return io.wyden.published.reporting.TIF.valueOf(tif.name());
        } catch (IllegalArgumentException e) {
            LOGGER.warn("Unknown ClientTIF: {}, defaulting to TIF_UNKNOWN", tif);
            return io.wyden.published.reporting.TIF.TIF_UNKNOWN;
        }
    }

    private static String getCurrentIsoTime() {
        return ZonedDateTime.now().toOffsetDateTime().toString();
    }
}
