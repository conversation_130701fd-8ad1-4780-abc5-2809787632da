package io.wyden.orderhistory.service;

import io.wyden.orderhistory.repository.OrderEventRepository;
import io.wyden.orderhistory.repository.OrderHistorySearchIndexRepository;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.time.OffsetDateTime;

/**
 * Monitor for the async processing system
 * Tracks processing lag, failed events, and system health
 */
@Component
public class ProcessingMonitor {

    private static final Logger LOGGER = LoggerFactory.getLogger(ProcessingMonitor.class);

    private final OrderEventRepository orderEventRepository;
    private final OrderHistorySearchIndexRepository searchIndexRepository;
    private final AsyncOrderStateProcessor asyncProcessor;
    
    @Value("${order.processing.lag.threshold.minutes:5}")
    private int lagThresholdMinutes;
    
    @Value("${order.processing.lag.alert.threshold:1000}")
    private long lagAlertThreshold;

    public ProcessingMonitor(
            OrderEventRepository orderEventRepository,
            OrderHistorySearchIndexRepository searchIndexRepository,
            AsyncOrderStateProcessor asyncProcessor) {
        this.orderEventRepository = orderEventRepository;
        this.searchIndexRepository = searchIndexRepository;
        this.asyncProcessor = asyncProcessor;
    }

    /**
     * Check for processing lag every 30 seconds
     */
    @Scheduled(fixedDelay = 30000)
    public void checkProcessingLag() {
        try {
            long oldUnprocessedCount = orderEventRepository.countOldUnprocessedEvents();
            
            if (oldUnprocessedCount > lagAlertThreshold) {
                LOGGER.warn("High processing lag detected: {} events older than {} minutes", 
                    oldUnprocessedCount, lagThresholdMinutes);
                
                // TODO: Send alert to monitoring system
                alertHighProcessingLag(oldUnprocessedCount);
            } else if (oldUnprocessedCount > 0) {
                LOGGER.info("Processing lag: {} events older than {} minutes", 
                    oldUnprocessedCount, lagThresholdMinutes);
            }
            
        } catch (Exception e) {
            LOGGER.error("Failed to check processing lag: {}", e.getMessage(), e);
        }
    }

    /**
     * Check for stale search index entries every 2 minutes
     */
    @Scheduled(fixedDelay = 120000)
    public void checkSearchIndexStaleness() {
        try {
            OffsetDateTime threshold = OffsetDateTime.now().minusMinutes(lagThresholdMinutes);
            long staleCount = searchIndexRepository.countOrdersWithOldTimestamp(threshold);
            
            if (staleCount > 0) {
                LOGGER.warn("Found {} orders with stale search index (older than {})", staleCount, threshold);
                
                // TODO: Trigger reprocessing of stale orders
                // TODO: Send alert to monitoring system
            }
            
        } catch (Exception e) {
            LOGGER.error("Failed to check search index staleness: {}", e.getMessage(), e);
        }
    }

    /**
     * Log processing statistics every 5 minutes
     */
    @Scheduled(fixedDelay = 300000)
    public void logProcessingStatistics() {
        try {
            long totalEvents = orderEventRepository.count();
            long totalSearchIndexEntries = searchIndexRepository.count();
            long oldUnprocessedEvents = orderEventRepository.countOldUnprocessedEvents();
            
            LOGGER.info("Processing Statistics - Total Events: {}, Search Index Entries: {}, Old Unprocessed: {}", 
                totalEvents, totalSearchIndexEntries, oldUnprocessedEvents);
                
            // Calculate processing rate
            if (totalEvents > 0) {
                double processingRate = ((double) (totalEvents - oldUnprocessedEvents) / totalEvents) * 100;
                LOGGER.info("Processing Rate: {:.2f}%", processingRate);
            }
            
        } catch (Exception e) {
            LOGGER.error("Failed to log processing statistics: {}", e.getMessage(), e);
        }
    }

    /**
     * Health check method that can be called by external monitoring
     */
    public ProcessingHealthStatus getHealthStatus() {
        try {
            long oldUnprocessedCount = orderEventRepository.countOldUnprocessedEvents();
            OffsetDateTime threshold = OffsetDateTime.now().minusMinutes(lagThresholdMinutes);
            long staleSearchIndexCount = searchIndexRepository.countOrdersWithOldTimestamp(threshold);
            
            boolean healthy = oldUnprocessedCount < lagAlertThreshold && staleSearchIndexCount == 0;
            
            return new ProcessingHealthStatus(
                healthy,
                oldUnprocessedCount,
                staleSearchIndexCount,
                lagThresholdMinutes
            );
            
        } catch (Exception e) {
            LOGGER.error("Failed to get health status: {}", e.getMessage(), e);
            return new ProcessingHealthStatus(false, -1, -1, lagThresholdMinutes);
        }
    }

    private void alertHighProcessingLag(long lagCount) {
        // TODO: Implement alerting mechanism
        // This could send alerts to:
        // - Slack/Teams
        // - PagerDuty
        // - Email
        // - Metrics system (Prometheus, etc.)
        
        LOGGER.error("ALERT: High processing lag - {} unprocessed events", lagCount);
    }

    /**
     * Manual trigger to retry processing of old events
     */
    public void retryOldEvents() {
        try {
            var oldEvents = orderEventRepository.findEventsOlderThanMinutes(lagThresholdMinutes);
            
            LOGGER.info("Retrying processing for {} old events", oldEvents.size());
            
            oldEvents.forEach(event -> {
                try {
                    asyncProcessor.scheduleProcessing(event.getId());
                } catch (Exception e) {
                    LOGGER.error("Failed to schedule retry for event {}: {}", event.getId(), e.getMessage());
                }
            });
            
        } catch (Exception e) {
            LOGGER.error("Failed to retry old events: {}", e.getMessage(), e);
        }
    }

    /**
     * Health status data class
     */
    public static class ProcessingHealthStatus {
        private final boolean healthy;
        private final long oldUnprocessedCount;
        private final long staleSearchIndexCount;
        private final int lagThresholdMinutes;

        public ProcessingHealthStatus(boolean healthy, long oldUnprocessedCount, 
                                    long staleSearchIndexCount, int lagThresholdMinutes) {
            this.healthy = healthy;
            this.oldUnprocessedCount = oldUnprocessedCount;
            this.staleSearchIndexCount = staleSearchIndexCount;
            this.lagThresholdMinutes = lagThresholdMinutes;
        }

        public boolean isHealthy() { return healthy; }
        public long getOldUnprocessedCount() { return oldUnprocessedCount; }
        public long getStaleSearchIndexCount() { return staleSearchIndexCount; }
        public int getLagThresholdMinutes() { return lagThresholdMinutes; }

        @Override
        public String toString() {
            return String.format("ProcessingHealthStatus{healthy=%s, oldUnprocessed=%d, staleSearchIndex=%d, threshold=%dm}", 
                healthy, oldUnprocessedCount, staleSearchIndexCount, lagThresholdMinutes);
        }
    }
}
