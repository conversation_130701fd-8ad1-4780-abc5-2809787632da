package io.wyden.orderhistory.model;

import jakarta.persistence.*;
import java.time.OffsetDateTime;
import java.util.List;

@Entity
@Table(name = "order_history_search_index", indexes = {
    @Index(name = "idx_search_portfolio_id", columnList = "portfolio_id"),
    @Index(name = "idx_search_instrument_id", columnList = "instrument_id"),
    @Index(name = "idx_search_order_status", columnList = "order_status"),
    @Index(name = "idx_search_created_at", columnList = "created_at"),
    @Index(name = "idx_search_updated_at", columnList = "updated_at"),
    @Index(name = "idx_search_root_order_id", columnList = "root_order_id"),
    @Index(name = "idx_search_portfolio_status", columnList = "portfolio_id, order_status")
})
public class OrderHistorySearchIndex {

    @Id
    @Column(name = "order_id", nullable = false)
    private String orderId;

    @Column(name = "latest_message_timestamp", nullable = false)
    private OffsetDateTime latestMessageTimestamp;

    @Column(name = "latest_event_id")
    private Long latestEventId;

    @Column(name = "portfolio_id")
    private String portfolioId;

    @Column(name = "portfolio_type")
    private String portfolioType;

    @Column(name = "venue_accounts", columnDefinition = "text[]")
    private List<String> venueAccounts;

    @Column(name = "instrument_id")
    private String instrumentId;

    @Column(name = "cl_order_id")
    private String clOrderId;

    @Column(name = "tif")
    private String tif;

    @Column(name = "result")
    private String result;

    @Column(name = "reason")
    private String reason;

    @Column(name = "order_status")
    private String orderStatus;

    @Column(name = "order_category")
    private String orderCategory;

    @Column(name = "parent_order_id")
    private String parentOrderId;

    @Column(name = "root_order_id")
    private String rootOrderId;

    @Column(name = "ext_order_id")
    private String extOrderId;

    @Column(name = "created_at")
    private OffsetDateTime createdAt;

    @Column(name = "updated_at")
    private OffsetDateTime updatedAt;

    // Constructors
    public OrderHistorySearchIndex() {}

    // Getters and Setters
    public String getOrderId() {
        return orderId;
    }

    public void setOrderId(String orderId) {
        this.orderId = orderId;
    }

    public OffsetDateTime getLatestMessageTimestamp() {
        return latestMessageTimestamp;
    }

    public void setLatestMessageTimestamp(OffsetDateTime latestMessageTimestamp) {
        this.latestMessageTimestamp = latestMessageTimestamp;
    }

    public Long getLatestEventId() {
        return latestEventId;
    }

    public void setLatestEventId(Long latestEventId) {
        this.latestEventId = latestEventId;
    }

    public String getPortfolioId() {
        return portfolioId;
    }

    public void setPortfolioId(String portfolioId) {
        this.portfolioId = portfolioId;
    }

    public String getPortfolioType() {
        return portfolioType;
    }

    public void setPortfolioType(String portfolioType) {
        this.portfolioType = portfolioType;
    }

    public List<String> getVenueAccounts() {
        return venueAccounts;
    }

    public void setVenueAccounts(List<String> venueAccounts) {
        this.venueAccounts = venueAccounts;
    }

    public String getInstrumentId() {
        return instrumentId;
    }

    public void setInstrumentId(String instrumentId) {
        this.instrumentId = instrumentId;
    }

    public String getClOrderId() {
        return clOrderId;
    }

    public void setClOrderId(String clOrderId) {
        this.clOrderId = clOrderId;
    }

    public String getTif() {
        return tif;
    }

    public void setTif(String tif) {
        this.tif = tif;
    }

    public String getResult() {
        return result;
    }

    public void setResult(String result) {
        this.result = result;
    }

    public String getReason() {
        return reason;
    }

    public void setReason(String reason) {
        this.reason = reason;
    }

    public String getOrderStatus() {
        return orderStatus;
    }

    public void setOrderStatus(String orderStatus) {
        this.orderStatus = orderStatus;
    }

    public String getOrderCategory() {
        return orderCategory;
    }

    public void setOrderCategory(String orderCategory) {
        this.orderCategory = orderCategory;
    }

    public String getParentOrderId() {
        return parentOrderId;
    }

    public void setParentOrderId(String parentOrderId) {
        this.parentOrderId = parentOrderId;
    }

    public String getRootOrderId() {
        return rootOrderId;
    }

    public void setRootOrderId(String rootOrderId) {
        this.rootOrderId = rootOrderId;
    }

    public String getExtOrderId() {
        return extOrderId;
    }

    public void setExtOrderId(String extOrderId) {
        this.extOrderId = extOrderId;
    }

    public OffsetDateTime getCreatedAt() {
        return createdAt;
    }

    public void setCreatedAt(OffsetDateTime createdAt) {
        this.createdAt = createdAt;
    }

    public OffsetDateTime getUpdatedAt() {
        return updatedAt;
    }

    public void setUpdatedAt(OffsetDateTime updatedAt) {
        this.updatedAt = updatedAt;
    }

    // Builder pattern
    public static Builder builder() {
        return new Builder();
    }

    public static class Builder {
        private final OrderHistorySearchIndex instance = new OrderHistorySearchIndex();

        public Builder orderId(String orderId) {
            instance.orderId = orderId;
            return this;
        }

        public Builder latestMessageTimestamp(OffsetDateTime latestMessageTimestamp) {
            instance.latestMessageTimestamp = latestMessageTimestamp;
            return this;
        }

        public Builder latestEventId(Long latestEventId) {
            instance.latestEventId = latestEventId;
            return this;
        }

        public Builder portfolioId(String portfolioId) {
            instance.portfolioId = portfolioId;
            return this;
        }

        public Builder portfolioType(String portfolioType) {
            instance.portfolioType = portfolioType;
            return this;
        }

        public Builder venueAccounts(List<String> venueAccounts) {
            instance.venueAccounts = venueAccounts;
            return this;
        }

        public Builder instrumentId(String instrumentId) {
            instance.instrumentId = instrumentId;
            return this;
        }

        public Builder clOrderId(String clOrderId) {
            instance.clOrderId = clOrderId;
            return this;
        }

        public Builder tif(String tif) {
            instance.tif = tif;
            return this;
        }

        public Builder result(String result) {
            instance.result = result;
            return this;
        }

        public Builder reason(String reason) {
            instance.reason = reason;
            return this;
        }

        public Builder orderStatus(String orderStatus) {
            instance.orderStatus = orderStatus;
            return this;
        }

        public Builder orderCategory(String orderCategory) {
            instance.orderCategory = orderCategory;
            return this;
        }

        public Builder parentOrderId(String parentOrderId) {
            instance.parentOrderId = parentOrderId;
            return this;
        }

        public Builder rootOrderId(String rootOrderId) {
            instance.rootOrderId = rootOrderId;
            return this;
        }

        public Builder extOrderId(String extOrderId) {
            instance.extOrderId = extOrderId;
            return this;
        }

        public Builder createdAt(OffsetDateTime createdAt) {
            instance.createdAt = createdAt;
            return this;
        }

        public Builder updatedAt(OffsetDateTime updatedAt) {
            instance.updatedAt = updatedAt;
            return this;
        }

        public OrderHistorySearchIndex build() {
            return instance;
        }
    }

    @Override
    public String toString() {
        return "OrderHistorySearchIndex{" +
                "orderId='" + orderId + '\'' +
                ", latestMessageTimestamp=" + latestMessageTimestamp +
                ", latestEventId=" + latestEventId +
                ", portfolioId='" + portfolioId + '\'' +
                ", orderStatus='" + orderStatus + '\'' +
                ", instrumentId='" + instrumentId + '\'' +
                '}';
    }
}
