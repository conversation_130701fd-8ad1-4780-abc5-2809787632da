package io.wyden.orderhistory.model;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.Index;
import jakarta.persistence.Table;

import java.time.OffsetDateTime;

@Entity
@Table(name = "order_events", indexes = {
    @Index(name = "idx_order_events_order_id", columnList = "order_id"),
    @Index(name = "idx_order_events_latest", columnList = "order_id, message_timestamp DESC")
})
public class OrderEventEntity {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(name = "order_id", nullable = false)
    private String orderId;

    @Column(name = "message_type", nullable = false)
    private String messageType;

    @Column(name = "message_timestamp", nullable = false)
    private OffsetDateTime messageTimestamp;

    @Column(name = "proto_blob", nullable = false)
    private byte[] protoBlob;

    @Column(name = "created_at", nullable = false)
    private OffsetDateTime createdAt;

    // Constructors
    public OrderEventEntity() {
        this.createdAt = OffsetDateTime.now();
    }

    public OrderEventEntity(String orderId, String messageType, OffsetDateTime messageTimestamp, byte[] protoBlob) {
        this();
        this.orderId = orderId;
        this.messageType = messageType;
        this.messageTimestamp = messageTimestamp;
        this.protoBlob = protoBlob;
    }

    // Getters and Setters
    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getOrderId() {
        return orderId;
    }

    public void setOrderId(String orderId) {
        this.orderId = orderId;
    }

    public String getMessageType() {
        return messageType;
    }

    public void setMessageType(String messageType) {
        this.messageType = messageType;
    }

    public OffsetDateTime getMessageTimestamp() {
        return messageTimestamp;
    }

    public void setMessageTimestamp(OffsetDateTime messageTimestamp) {
        this.messageTimestamp = messageTimestamp;
    }

    public byte[] getProtoBlob() {
        return protoBlob;
    }

    public void setProtoBlob(byte[] protoBlob) {
        this.protoBlob = protoBlob;
    }

    public OffsetDateTime getCreatedAt() {
        return createdAt;
    }

    public void setCreatedAt(OffsetDateTime createdAt) {
        this.createdAt = createdAt;
    }

    // Builder pattern
    public static Builder builder() {
        return new Builder();
    }

    public static class Builder {
        private String orderId;
        private String messageType;
        private OffsetDateTime messageTimestamp;
        private byte[] protoBlob;

        public Builder orderId(String orderId) {
            this.orderId = orderId;
            return this;
        }

        public Builder messageType(String messageType) {
            this.messageType = messageType;
            return this;
        }

        public Builder messageTimestamp(OffsetDateTime messageTimestamp) {
            this.messageTimestamp = messageTimestamp;
            return this;
        }

        public Builder protoBlob(byte[] protoBlob) {
            this.protoBlob = protoBlob;
            return this;
        }

        public OrderEventEntity build() {
            return new OrderEventEntity(orderId, messageType, messageTimestamp, protoBlob);
        }
    }

    @Override
    public String toString() {
        return "OrderEventEntity{" +
                "id=" + id +
                ", orderId='" + orderId + '\'' +
                ", messageType='" + messageType + '\'' +
                ", messageTimestamp=" + messageTimestamp +
                ", createdAt=" + createdAt +
                '}';
    }
}
