package io.wyden.orderhistory.repository;

import io.wyden.orderhistory.model.OrderHistorySearchIndex;
import io.wyden.orderhistory.model.OrderHistorySearchInput;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.OffsetDateTime;
import java.util.List;
import java.util.Optional;

@Repository
public interface OrderHistorySearchIndexRepository extends JpaRepository<OrderHistorySearchIndex, String> {

    /**
     * Find search index by order ID
     */
    Optional<OrderHistorySearchIndex> findByOrderId(String orderId);

    /**
     * Find order IDs by portfolio ID
     */
    @Query("SELECT s.orderId FROM OrderHistorySearchIndex s WHERE s.portfolioId = :portfolioId")
    List<String> findOrderIdsByPortfolioId(@Param("portfolioId") String portfolioId);

    /**
     * Find order IDs by portfolio ID and order status
     */
    @Query("SELECT s.orderId FROM OrderHistorySearchIndex s WHERE s.portfolioId = :portfolioId AND s.orderStatus = :orderStatus")
    List<String> findOrderIdsByPortfolioIdAndOrderStatus(
        @Param("portfolioId") String portfolioId, 
        @Param("orderStatus") String orderStatus
    );

    /**
     * Find order IDs by instrument ID
     */
    @Query("SELECT s.orderId FROM OrderHistorySearchIndex s WHERE s.instrumentId = :instrumentId")
    List<String> findOrderIdsByInstrumentId(@Param("instrumentId") String instrumentId);

    /**
     * Find order IDs by root order ID
     */
    @Query("SELECT s.orderId FROM OrderHistorySearchIndex s WHERE s.rootOrderId = :rootOrderId")
    List<String> findOrderIdsByRootOrderId(@Param("rootOrderId") String rootOrderId);

    /**
     * Find order IDs by parent order ID
     */
    @Query("SELECT s.orderId FROM OrderHistorySearchIndex s WHERE s.parentOrderId = :parentOrderId")
    List<String> findOrderIdsByParentOrderId(@Param("parentOrderId") String parentOrderId);

    /**
     * Find order IDs by time range
     */
    @Query("SELECT s.orderId FROM OrderHistorySearchIndex s WHERE s.createdAt BETWEEN :startTime AND :endTime")
    List<String> findOrderIdsByTimeRange(
        @Param("startTime") OffsetDateTime startTime,
        @Param("endTime") OffsetDateTime endTime
    );

    /**
     * Complex search query - this will be implemented in a custom repository
     * For now, we'll use a simple approach and extend it later
     */
    @Query("""
        SELECT s.orderId FROM OrderHistorySearchIndex s 
        WHERE (:portfolioId IS NULL OR s.portfolioId = :portfolioId)
        AND (:instrumentId IS NULL OR s.instrumentId = :instrumentId)
        AND (:orderStatus IS NULL OR s.orderStatus = :orderStatus)
        AND (:rootOrderId IS NULL OR s.rootOrderId = :rootOrderId)
        AND (:parentOrderId IS NULL OR s.parentOrderId = :parentOrderId)
        AND (:startTime IS NULL OR s.createdAt >= :startTime)
        AND (:endTime IS NULL OR s.createdAt <= :endTime)
        ORDER BY s.createdAt DESC
        """)
    List<String> findOrderIdsBySearchCriteria(
        @Param("portfolioId") String portfolioId,
        @Param("instrumentId") String instrumentId,
        @Param("orderStatus") String orderStatus,
        @Param("rootOrderId") String rootOrderId,
        @Param("parentOrderId") String parentOrderId,
        @Param("startTime") OffsetDateTime startTime,
        @Param("endTime") OffsetDateTime endTime
    );

    /**
     * Count orders by search criteria
     */
    @Query("""
        SELECT COUNT(s) FROM OrderHistorySearchIndex s 
        WHERE (:portfolioId IS NULL OR s.portfolioId = :portfolioId)
        AND (:instrumentId IS NULL OR s.instrumentId = :instrumentId)
        AND (:orderStatus IS NULL OR s.orderStatus = :orderStatus)
        AND (:rootOrderId IS NULL OR s.rootOrderId = :rootOrderId)
        AND (:parentOrderId IS NULL OR s.parentOrderId = :parentOrderId)
        AND (:startTime IS NULL OR s.createdAt >= :startTime)
        AND (:endTime IS NULL OR s.createdAt <= :endTime)
        """)
    long countBySearchCriteria(
        @Param("portfolioId") String portfolioId,
        @Param("instrumentId") String instrumentId,
        @Param("orderStatus") String orderStatus,
        @Param("rootOrderId") String rootOrderId,
        @Param("parentOrderId") String parentOrderId,
        @Param("startTime") OffsetDateTime startTime,
        @Param("endTime") OffsetDateTime endTime
    );

    /**
     * Find orders updated after a specific timestamp (for monitoring lag)
     */
    @Query("SELECT COUNT(s) FROM OrderHistorySearchIndex s WHERE s.latestMessageTimestamp < :threshold")
    long countOrdersWithOldTimestamp(@Param("threshold") OffsetDateTime threshold);
}
