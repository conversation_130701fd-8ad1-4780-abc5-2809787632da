package io.wyden.orderhistory.repository;

import io.wyden.orderhistory.model.OrderEventEntity;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Map;
import java.util.Optional;

@Repository
public interface OrderEventRepository extends JpaRepository<OrderEventEntity, Long> {

    /**
     * Find all events for a specific order, ordered by message timestamp
     */
    List<OrderEventEntity> findByOrderIdOrderByMessageTimestampAsc(String orderId);

    /**
     * Find the latest event for a specific order
     */
    @Query("SELECT e FROM OrderEventEntity e WHERE e.orderId = :orderId ORDER BY e.messageTimestamp DESC LIMIT 1")
    Optional<OrderEventEntity> findLatestEventByOrderId(@Param("orderId") String orderId);

    /**
     * Find latest events for multiple order IDs
     */
    @Query("""
        SELECT e FROM OrderEventEntity e 
        WHERE e.id IN (
            SELECT MAX(e2.id) FROM OrderEventEntity e2 
            WHERE e2.orderId IN :orderIds 
            GROUP BY e2.orderId
        )
        """)
    List<OrderEventEntity> findLatestEventsByOrderIds(@Param("orderIds") List<String> orderIds);

    /**
     * Count unprocessed events (for monitoring)
     */
    @Query("SELECT COUNT(e) FROM OrderEventEntity e WHERE e.createdAt < CURRENT_TIMESTAMP - INTERVAL '5 minutes'")
    long countOldUnprocessedEvents();

    /**
     * Find events older than specified minutes for monitoring
     */
    @Query("SELECT e FROM OrderEventEntity e WHERE e.createdAt < CURRENT_TIMESTAMP - INTERVAL ':minutes minutes'")
    List<OrderEventEntity> findEventsOlderThanMinutes(@Param("minutes") int minutes);

    /**
     * Find all events for an order within a time range
     */
    @Query("""
        SELECT e FROM OrderEventEntity e 
        WHERE e.orderId = :orderId 
        AND e.messageTimestamp BETWEEN :startTime AND :endTime 
        ORDER BY e.messageTimestamp ASC
        """)
    List<OrderEventEntity> findByOrderIdAndTimeRange(
        @Param("orderId") String orderId,
        @Param("startTime") java.time.OffsetDateTime startTime,
        @Param("endTime") java.time.OffsetDateTime endTime
    );
}
