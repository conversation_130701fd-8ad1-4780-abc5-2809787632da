package io.wyden.orderhistory.config;

import io.wyden.orderhistory.service.AsyncOrderStateProcessor;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.annotation.EnableScheduling;

/**
 * Configuration for the new async processing architecture
 */
@Configuration
@EnableScheduling
public class AsyncProcessingConfig {

    /**
     * Enable the new simplified consumer when the feature flag is set
     */
    @Bean
    @ConditionalOnProperty(
        name = "order.history.simplified.enabled", 
        havingValue = "true", 
        matchIfMissing = false
    )
    public String simplifiedConsumerEnabled() {
        return "simplified-consumer-enabled";
    }

    /**
     * Configuration properties for async processing
     */
    @Bean
    public AsyncProcessingProperties asyncProcessingProperties() {
        return new AsyncProcessingProperties();
    }

    /**
     * Properties class for async processing configuration
     */
    public static class AsyncProcessingProperties {
        
        private int lagThresholdMinutes = 5;
        private long lagAlertThreshold = 1000;
        private boolean retryEnabled = true;
        private int maxRetryAttempts = 3;
        private long retryDelayMs = 5000;

        // Getters and setters
        public int getLagThresholdMinutes() {
            return lagThresholdMinutes;
        }

        public void setLagThresholdMinutes(int lagThresholdMinutes) {
            this.lagThresholdMinutes = lagThresholdMinutes;
        }

        public long getLagAlertThreshold() {
            return lagAlertThreshold;
        }

        public void setLagAlertThreshold(long lagAlertThreshold) {
            this.lagAlertThreshold = lagAlertThreshold;
        }

        public boolean isRetryEnabled() {
            return retryEnabled;
        }

        public void setRetryEnabled(boolean retryEnabled) {
            this.retryEnabled = retryEnabled;
        }

        public int getMaxRetryAttempts() {
            return maxRetryAttempts;
        }

        public void setMaxRetryAttempts(int maxRetryAttempts) {
            this.maxRetryAttempts = maxRetryAttempts;
        }

        public long getRetryDelayMs() {
            return retryDelayMs;
        }

        public void setRetryDelayMs(long retryDelayMs) {
            this.retryDelayMs = retryDelayMs;
        }
    }
}
