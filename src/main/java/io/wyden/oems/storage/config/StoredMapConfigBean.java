package io.wyden.oems.storage.config;

import com.hazelcast.config.Config;
import com.hazelcast.config.MapStoreConfig;
import io.wyden.cloudutils.hazelcast.HazelcastMapConfig;

public class StoredMapConfigBean extends EphemeralMapConfigBean {

    private final MapStoreConfig mapStoreConfig;

    public StoredMapConfigBean(HazelcastMapConfig hazelcastMapConfig, MapStoreConfig mapStoreConfig) {
        super(hazelcastMapConfig);
        this.mapStoreConfig = mapStoreConfig;
    }

    @Override
    public void applyConfig(Config config) {
        hazelcastMapConfig.applyConfig(config, mapStoreConfig);
    }
}
