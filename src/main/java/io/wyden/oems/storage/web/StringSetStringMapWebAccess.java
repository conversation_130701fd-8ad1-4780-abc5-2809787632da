package io.wyden.oems.storage.web;

import com.hazelcast.core.HazelcastInstance;
import io.wyden.cloudutils.hazelcast.HazelcastMapConfig;
import io.wyden.oems.storage.config.BaseMapStore;
import org.apache.commons.lang3.NotImplementedException;
import org.apache.commons.lang3.RegExUtils;

import java.util.Set;
import javax.annotation.Nullable;
import javax.annotation.ParametersAreNonnullByDefault;

@ParametersAreNonnullByDefault
public class StringSetStringMapWebAccess extends AbstractWebAccess {

    private final HazelcastMapConfig hzMapConfig;
    private final BaseMapStore<String, Set<String>> mapStore;

    public StringSetStringMapWebAccess(HazelcastInstance hz,
                                       HazelcastMapConfig hzMapConfig,
                                       @Nullable BaseMapStore<String, Set<String>> mapStore) {
        super(hz);
        this.hzMapConfig = hzMapConfig;
        this.mapStore = mapStore;
    }

    @Override
    public String getName() {
        if (mapStore != null) {
            return this.mapStore.getMapName();
        } else {
            return RegExUtils.removePattern(getMapName(), "_.*$");
        }
    }

    @Override
    public String getMapName() {
        return hzMapConfig.getMapName();
    }

    @Override
    protected String keyToString(Object key) {
        return (String) key;
    }

    @Override
    protected Object stringToKey(String keyString) {
        return keyString;
    }

    @Override
    protected String valueToString(Object value) {
        return value.toString();
    }

    @Override
    protected Object stringToValue(@Nullable Object oldValue, String updateJson) {
        throw new NotImplementedException("Set value not implemented");
    }
}
