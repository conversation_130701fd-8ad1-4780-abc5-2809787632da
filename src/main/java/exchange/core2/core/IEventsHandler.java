package exchange.core2.core;

import java.util.List;

import exchange.core2.core.common.L2MarketData;
import exchange.core2.core.common.MatcherTradeEvent;

/**
 * Convenient events handler interface for non latency-critical applications.<br>
 * Custom handler implementation should be attached to SimpleEventProcessor.<br>
 * Handler method are invoked from single thread in following order:
 * <table summary="execution order">
 * <tr><td>1. </td><td> commandResult</td></tr>
 * <tr><td>2A.  </td><td> optional reduceEvent <td> optional tradeEvent</td></tr>
 * <tr><td>2B. </td><td> <td>optional rejectEvent</td></tr>
 * <tr><td>3. </td><td> orderBook - mandatory for ApiOrderBookRequest, optional for other commands</td></tr>
 * </table>
 * Events processing will stop immediately if any handler throws an exception - you should consider wrapping logic into try-catch block if necessary.
 */
public interface IEventsHandler {

    void addSymbolSpecificationResult(
            long correlationId);

    void placeOrderResult(
            long price,
            long size,
            long acceptedAmount,
            long orderId,
            exchange.core2.core.common.OrderAction action,
            exchange.core2.core.common.OrderType orderType,
            long uid,
            int symbol,
            exchange.core2.core.common.cmd.CommandResultCode resultCode,
            int serviceFlags,
            long orderUuidMostSigBits,
            long orderUuidLeastSigBits,
            long timestamp,
            long seq);

    void moveOrderResult(
            long orderId,
            long newPrice,
            long uid,
            int symbol,
            exchange.core2.core.common.cmd.CommandResultCode resultCode,
            int serviceFlags,
            long orderUuidMostSigBits,
            long orderUuidLeastSigBits,
            long timestamp,
            long seq);

    void reduceOrderResult(
            long orderId,
            long uid,
            int symbol,
            long reduceSize,
            exchange.core2.core.common.cmd.CommandResultCode resultCode,
            int serviceFlags,
            long orderUuidMostSigBits,
            long orderUuidLeastSigBits,
            long timestamp,
            long seq);

    void cancelOrderResult(
            long orderId,
            long uid,
            int symbol,
            exchange.core2.core.common.cmd.CommandResultCode resultCode,
            int serviceFlags,
            long orderUuidMostSigBits,
            long orderUuidLeastSigBits,
            long timestamp,
            long seq);

    void orderBookResult(
            int symbol,
            int size,
            exchange.core2.core.common.cmd.CommandResultCode resultCode,
            long timestamp,
            long seq);

    void tradeEvent(
            int symbol,
            long price,
            long totalVolume,
            long takerOrderId,
            long takerUid,
            long takerTradeId,
            long matchId,
            exchange.core2.core.common.OrderAction takerAction,
            boolean takerOrderCompleted,
            long takerOrderUuidMostSigBits,
            long takerOrderUuidLeastSigBits,
            long timestamp,
            int tradesCount,
            List<MatcherTradeEvent> matcherTradeEvents);

    void reduceEvent(
            int symbol,
            long reducedVolume,
            boolean orderCompleted,
            long orderUuidMostSigBits,
            long orderUuidLeastSigBits,
            long price,
            long orderId,
            long uid,
            long timestamp);

    void rejectEvent(
            int symbol,
            long rejectedVolume,
            long orderUuidMostSigBits,
            long orderUuidLeastSigBits,
            long price,
            long orderId,
            long uid,
            long timestamp);

    void orderBook(
        int symbol,
        L2MarketData marketData,
        long timestamp,
        long seq);

}


