-- Migration script for the new Order History architecture
-- Creates the new tables for event sourcing approach

-- Create order_events table for storing raw proto messages
CREATE TABLE order_events (
    id BIGSERIAL PRIMARY KEY,
    order_id TEXT NOT NULL,
    message_type TEXT NOT NULL CHECK (message_type IN ('OemsResponse', 'ClientResponse')),
    message_timestamp TIMESTAMPTZ NOT NULL,
    proto_blob BYTEA NOT NULL,
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW()
);

-- Create indexes for order_events
CREATE INDEX idx_order_events_order_id ON order_events (order_id);
CREATE INDEX idx_order_events_latest ON order_events (order_id, message_timestamp DESC);
CREATE INDEX idx_order_events_created_at ON order_events (created_at);
CREATE INDEX idx_order_events_message_type ON order_events (message_type);

-- Create order_history_search_index table for fast searching
CREATE TABLE order_history_search_index (
    order_id TEXT PRIMARY KEY,
    latest_message_timestamp TIMESTAMPTZ NOT NULL,
    latest_event_id BIGINT REFERENCES order_events(id),
    
    -- Search fields extracted from OrderState
    portfolio_id TEXT,
    portfolio_type TEXT,
    venue_accounts TEXT[],
    instrument_id TEXT,
    cl_order_id TEXT,
    tif TEXT,
    result TEXT,
    reason TEXT,
    order_status TEXT,
    order_category TEXT,
    parent_order_id TEXT,
    root_order_id TEXT,
    ext_order_id TEXT,
    created_at TIMESTAMPTZ,
    updated_at TIMESTAMPTZ
);

-- MINIMAL INDEXES APPROACH - Start with no indexes for maximum write performance
-- Only essential indexes that are absolutely required

-- Primary key index (automatic)
-- No additional indexes initially - we'll add them based on actual query patterns

-- Optional: Single index on updated_at for cursor pagination (if needed)
-- CREATE INDEX idx_search_updated_at ON order_history_search_index (updated_at DESC);

-- Future indexes to consider based on monitoring:
-- CREATE INDEX idx_search_portfolio_id ON order_history_search_index (portfolio_id);
-- CREATE INDEX idx_search_recent_orders ON order_history_search_index (updated_at DESC)
--   WHERE updated_at > NOW() - INTERVAL '7 days';
-- CREATE INDEX idx_search_active_orders ON order_history_search_index (portfolio_id, order_status)
--   WHERE order_status IN ('NEW', 'PARTIALLY_FILLED');

-- Add comments for documentation
COMMENT ON TABLE order_events IS 'Stores raw proto messages (OemsResponse/ClientResponse) for event sourcing';
COMMENT ON COLUMN order_events.message_timestamp IS 'Timestamp from the proto message (venue_timestamp or system_timestamp)';
COMMENT ON COLUMN order_events.proto_blob IS 'Serialized protobuf message as bytes';

COMMENT ON TABLE order_history_search_index IS 'Search index with extracted fields from OrderState for fast querying';
COMMENT ON COLUMN order_history_search_index.latest_message_timestamp IS 'Timestamp of the latest processed message for this order';
COMMENT ON COLUMN order_history_search_index.latest_event_id IS 'Reference to the latest processed event';

-- Grant permissions (adjust as needed for your environment)
-- GRANT SELECT, INSERT, UPDATE ON order_events TO order_history_service;
-- GRANT SELECT, INSERT, UPDATE, DELETE ON order_history_search_index TO order_history_service;
-- GRANT USAGE ON SEQUENCE order_events_id_seq TO order_history_service;
