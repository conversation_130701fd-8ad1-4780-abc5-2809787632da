package io.wyden.smartrecommendationengine.service.marketdata;

import io.wyden.audit.client.AuditEventsClient;
import io.wyden.published.marketdata.InstrumentKey;
import io.wyden.smartrecommendationengine.infra.SimpleIntegrationTestBase;
import io.wyden.smartrecommendationengine.service.marketdata.model.MdRequest;
import org.jetbrains.annotations.NotNull;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mockito;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.boot.test.util.TestPropertyValues;
import org.springframework.context.ApplicationContextInitializer;
import org.springframework.context.ConfigurableApplicationContext;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit.jupiter.SpringExtension;

import java.util.concurrent.TimeUnit;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;

@SpringBootTest(properties = {"spring.main.allow-bean-definition-overriding=true", "logging.level.root=debug"})
@ExtendWith(SpringExtension.class)
@ContextConfiguration(initializers = MarketDataConsumerTest.Initializer.class)
public class MarketDataConsumerTest extends SimpleIntegrationTestBase {

    public static final String TEST_CLIENT_ID = "test-client";

    public static class Initializer implements ApplicationContextInitializer<ConfigurableApplicationContext> {

        @Override
        public void initialize(@NotNull ConfigurableApplicationContext applicationContext) {

            // send out md heartbeat every 3 seconds
            // unsubscribe after 5 seconds after actual unsubscribe
            // prolong md give up delay to 30 sec (how long we wait for md to arrive)

            var values = TestPropertyValues.of(
                "rabbitmq.host=" + RABBIT_MQ.getHost(),
                "rabbitmq.port=" + RABBIT_MQ.getMappedPort(5672),
                "rabbitmq.username=" + RABBIT_MQ.getAdminUsername(),
                "rabbitmq.password=" + RABBIT_MQ.getAdminPassword(),

                "market-data.client.heartbeat-interval=" + "3",
                "market-data.initialGiveUpDelay=" + "30",
                "market-data.subscription.giveUpDelay=" + "7",
                "toggle.pricing-service=" + "true"
            );

            values.applyTo(applicationContext);
        }
    }

    @MockBean
    private MarketDataRequestEmitter marketDataRequestEmitter;
    @MockBean
    private AuditEventsClient auditEventClient;

    @Autowired
    private MarketDataConsumer mockMarketDataConsumer;

    @Test
    void shouldNotUnsubscribeRightAway() throws InterruptedException {
        // given
        String requestId = "123";
        String instrumentId = "BTC/USD@BMX";
        InstrumentKey instrumentKey = InstrumentKey.newBuilder().setInstrumentId(instrumentId).build();
        MdRequest mdRequest = new MdRequest(TEST_CLIENT_ID, requestId);

        // when
        mockMarketDataConsumer.subscribe(instrumentKey, mdRequest);
        TimeUnit.SECONDS.sleep(5);

        // then
        // send out 2 heartbeats
        Mockito.verify(marketDataRequestEmitter, Mockito.atLeast(2)).subscribe(any(InstrumentKey.class), anyString());
        Mockito.verify(marketDataRequestEmitter, Mockito.atMost(4)).subscribe(any(InstrumentKey.class), anyString());
        Mockito.reset(marketDataRequestEmitter);

        // when
        mockMarketDataConsumer.scheduleUnsubscription(instrumentKey, mdRequest);
        TimeUnit.SECONDS.sleep(10);

        // then
        // send out only 2 heartbeats (subscription is alive for 5 more seconds)
        Mockito.verify(marketDataRequestEmitter, Mockito.atLeast(2)).subscribe(any(InstrumentKey.class), anyString());
        Mockito.verify(marketDataRequestEmitter, Mockito.atMost(4)).subscribe(any(InstrumentKey.class), anyString());
    }
}
