package io.wyden.sor.service.fsm;

import io.wyden.published.oems.OemsOrderStatus;
import io.wyden.published.oems.OemsResponse;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.math.BigDecimal;

/**
 * Intermediate state, coexists with other intermediate states, but always takes precedence before them.
 * Order moves to PendingCancel when order in New or PartialFill receives Cancel request.
 * Can transition to:
 *  - Filled - on child order trade ExecutionReport fulfilling the order
 *  - Cancelled:
 *      - when max retries count for smart order has been reached
 *      - on child order terminal state (CANCELED, REJECTED, FILLED) if there was already some trade for this order before to prevent executions on multiple venues
 *      - on child order CancelReject
 * Child order NEW ExecutionReport does not change order state, but update underlying state.
 * Child order trade execution orders not fulfilling the order do not change the state, but update remainingQty and underlying state.
 */
class StatusPendingCancel extends StatusNew {

    private static final Logger LOGGER = LoggerFactory.getLogger(StatusPendingCancel.class);
    private static final OrderStatus instance = new StatusPendingCancel();

    static OrderStatus create() {
        return instance;
    }

    @Override
    Precedence getPrecedence() {
        return Precedence.PENDING_CANCEL;
    }

    @Override
    OemsOrderStatus toOemsOrderStatus() {
        return OemsOrderStatus.STATUS_PENDING_CANCEL;
    }

    @Override
    void onChildCancelReject(OrderContext context, OemsResponse venueCancelReject) {
        if (context.getOrderState().isForceCancel()) {
            LOGGER.info("Ignoring child order cancel reject, smart order is force cancelled");
            return;
        }

        if (context.getOrderState().getFilledQuantity() == null ||
            context.getOrderState().getFilledQuantity().equals(BigDecimal.ZERO)) {
            context.cancelSmartOrder(context.getOrderState().getOemsRequest(), "Child order cancel rejected");
        } else {
            context.handleChildCancelRejected(venueCancelReject);
        }
    }
}
