package io.wyden.sor.service.fsm;

import io.wyden.published.oems.OemsOrderStatus;
import io.wyden.published.oems.OemsRequest;
import io.wyden.published.oems.OemsResponse;
import io.wyden.published.oems.OemsSide;
import io.wyden.sor.model.SmartOrderState;
import org.junit.jupiter.api.Test;

import java.math.BigDecimal;

import static org.assertj.core.api.Assertions.assertThat;


class ApplyExecutionTest {

    public static final String QUANTITY = "100";
    public static final String ZERO = "0";
    public static final String FULL_EXECUTION_AVG_PRICE = "50000.0";

    public static final String PARTIAL_FILL_1_QUANTITY = "20";
    public static final String PARTIAL_FILL_1_AVG_PRICE = "40000.0";
    public static final String PARTIAL_FILL_1_LEAVES_QUANTITY = "80";

    public static final String PARTIAL_FILL_2_QUANTITY = "80";
    public static final String PARTIAL_FILL_2_AVG_PRICE = "60000.0";

    public static final String PARTIAL_FILL_AVG_PRICE = "56000.0"; // (40000*20+60000*80)/100

    @Test
    void testFromNewToFilled() {
        OrderState orderState = new OrderState(newUnfilledState());
        OemsResponse report = OemsResponse.newBuilder()
            .setResponseType(OemsResponse.OemsResponseType.EXECUTION_REPORT)
            .setExecutionId("e1")
            .setLastQty(QUANTITY)
            .setLastPrice(FULL_EXECUTION_AVG_PRICE)
            .build();
        StatusNew.applyChildExecution(orderState, report);

        assertThat(orderState)
            .extracting("orderStatus", "avgPrice", "quantity", "filledQuantity", "remainingQuantity")
            .containsExactly(new StatusFilled(), new BigDecimal(FULL_EXECUTION_AVG_PRICE), new BigDecimal(QUANTITY), new BigDecimal(QUANTITY), new BigDecimal(ZERO));

        assertThat(orderState.getExecutions()).hasSize(1);

        assertThat(orderState.getExecutions().get(0))
            .extracting("quantity", "price")
            .containsExactly(QUANTITY, FULL_EXECUTION_AVG_PRICE);
    }

    @Test
    void testFromNewToPartiallyFilledToFilled() {
        OrderState orderState = new OrderState(newUnfilledState());
        OemsResponse report1 = OemsResponse.newBuilder()
            .setResponseType(OemsResponse.OemsResponseType.EXECUTION_REPORT)
            .setExecutionId("e1")
            .setLastQty(PARTIAL_FILL_1_QUANTITY)
            .setLastPrice(PARTIAL_FILL_1_AVG_PRICE)
            .build();
        StatusNew.applyChildExecution(orderState, report1);

        assertThat(orderState)
            .extracting("orderStatus", "avgPrice", "quantity", "filledQuantity", "remainingQuantity")
            .containsExactly(new StatusPartialFill(), new BigDecimal(PARTIAL_FILL_1_AVG_PRICE), new BigDecimal(QUANTITY),
                new BigDecimal(PARTIAL_FILL_1_QUANTITY), new BigDecimal(PARTIAL_FILL_1_LEAVES_QUANTITY));

        OemsResponse report2 = OemsResponse.newBuilder()
            .setResponseType(OemsResponse.OemsResponseType.EXECUTION_REPORT)
            .setExecutionId("e2")
            .setLastQty(PARTIAL_FILL_2_QUANTITY)
            .setLastPrice(PARTIAL_FILL_2_AVG_PRICE)
            .build();
        StatusNew.applyChildExecution(orderState, report2);

        assertThat(orderState)
            .extracting("orderStatus", "avgPrice", "quantity", "filledQuantity", "remainingQuantity")
            .containsExactly(new StatusFilled(), new BigDecimal(PARTIAL_FILL_AVG_PRICE),
                new BigDecimal(QUANTITY), new BigDecimal(QUANTITY), new BigDecimal(ZERO));
    }

    private SmartOrderState newUnfilledState() {
        return SmartOrderState.newBuilder()
            .addCurrentStatus(OemsOrderStatus.STATUS_NEW)
            .setAvgPrice("0")
            .setQuantity(QUANTITY)
            .setRemainingQuantity(QUANTITY)
            .setFilledQuantity(ZERO)
            .setRequest(OemsRequest.newBuilder()
                .setSide(OemsSide.BUY)
                .build())
            .build();
    }
}
