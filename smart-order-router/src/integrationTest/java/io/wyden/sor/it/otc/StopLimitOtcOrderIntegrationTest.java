package io.wyden.sor.it.otc;

import io.wyden.published.marketdata.InstrumentKey;
import io.wyden.published.oems.OemsExecType;
import io.wyden.published.oems.OemsOrderStatus;
import io.wyden.published.oems.OemsOrderType;
import io.wyden.published.oems.OemsRequest;
import io.wyden.published.oems.OemsResponse;
import io.wyden.published.smartrecommendationengine.BestExecutionRequest;
import org.junit.jupiter.api.Test;
import org.springframework.test.util.ReflectionTestUtils;

import java.math.BigDecimal;
import java.util.concurrent.TimeUnit;

import static org.assertj.core.api.Assertions.assertThat;

class StopLimitOtcOrderIntegrationTest extends SOROtcIntegrationTestBase {

    public static final OemsOrderType OEMS_ORDER_TYPE = OemsOrderType.STOP_LIMIT;
    public static final OemsOrderType OEMS_CHILD_ORDER_TYPE = OemsOrderType.LIMIT;
    
    @Test
    void stopLimitOrderImmediateExecution() {
        OemsRequest smartOrder = orderGateway.emitsOemsRequest(testingData.clientSmartOrderRequestOtc(OEMS_ORDER_TYPE));

        // broadcast request for execution recommendations to SRE
        BestExecutionRequest bestExecutionRequest = smartRecommendationEngine.awaitBestExecutionBroadcastRequests(smartOrder.getOrderId());
        assertThat(bestExecutionRequest.getSide()).isEqualTo(smartOrder.getSide());
        assertThat(bestExecutionRequest.getQuantity()).isEqualTo(smartOrder.getQuantity());
        assertThat(bestExecutionRequest.getInstrumentsList()).extracting(InstrumentKey::getInstrumentId).containsExactlyInAnyOrder(
            BTCUSD_B2C2_INSTRUMENT_ID, BTCUSD_CF_INSTRUMENT_ID);

        // SRE responds with immediate recommendations
        smartRecommendationEngine.emitExecutionRecommendationsOtcDone(bestExecutionRequest.getRecommendationSubscriptionId());

        // SOR sends out new child order request
        OemsRequest childOrder = collider.awaitNewChildOrderRequest(smartOrder.getOrderId(), CF);
        assertThat(childOrder).satisfies(request -> {
            assertThat(request.getInstrumentId()).isEqualTo(BTCUSD_CF_INSTRUMENT_ID);
            assertThat(request.getMetadata().getTarget()).isEqualTo(CF);
            assertThat(request.getQuantity()).isEqualTo(smartOrder.getQuantity());
            assertThat(request.getOrderType()).isEqualTo(OEMS_CHILD_ORDER_TYPE);
            assertThat(request.getSide()).isEqualTo(smartOrder.getSide());
            assertThat(request.getParentOrderId()).isEqualTo(smartOrder.getOrderId());
        });

        // child reported as new
        collider.emitExecutionReportNew(childOrder);

        // ER NEW for parent
        OemsResponse parentExecNew = orderGateway.awaitExecutionReportWithOrderStatusReceived(smartOrder.getOrderId(), OemsOrderStatus.STATUS_NEW);
        assertThat(parentExecNew).satisfies(response -> {
            assertExecReportFor(response, smartOrder);
            assertThat(response.getExecType()).isEqualTo(OemsExecType.NEW);
            assertThat(response.getOrderStatus()).isEqualTo(OemsOrderStatus.STATUS_NEW);
            assertThat(response.getCumQty()).isEqualTo("0");
            assertThat(response.getLeavesQty()).isEqualTo(smartOrder.getQuantity());
            assertThat(response.getLastPrice()).isEqualTo("0");
            assertThat(response.getAvgPrice()).isEqualTo("0");
        });

        // child is filled
        collider.emitExecutionReportFilled(childOrder);

        // ER FILLED for parent
        OemsResponse parentExecFilled = orderGateway.awaitExecutionReportWithOrderStatusReceived(smartOrder.getOrderId(), OemsOrderStatus.STATUS_FILLED);
        assertThat(parentExecFilled).satisfies(response -> {
            assertExecReportFor(response, smartOrder);
            assertThat(response.getExecType()).isEqualTo(OemsExecType.FILL);
            assertThat(response.getOrderStatus()).isEqualTo(OemsOrderStatus.STATUS_FILLED);
            assertThat(response.getCumQty()).isEqualTo(smartOrder.getQuantity());
            assertThat(response.getLeavesQty()).isEqualTo("0");
            assertThat(new BigDecimal(response.getLastPrice())).isEqualByComparingTo(testingData.executionPrice);
            assertThat(new BigDecimal(response.getAvgPrice())).isEqualByComparingTo(testingData.executionPrice);
        });
    }


    @Test
    void stopLimitOrderDelayedExecution() {
        OemsRequest smartOrder = orderGateway.emitsOemsRequest(testingData.clientSmartOrderRequestOtc(OEMS_ORDER_TYPE));

        // broadcast request for execution recommendations to SRE
        BestExecutionRequest bestExecutionRequest = smartRecommendationEngine.awaitBestExecutionBroadcastRequests(smartOrder.getOrderId());
        assertThat(bestExecutionRequest.getSide()).isEqualTo(smartOrder.getSide());
        assertThat(bestExecutionRequest.getQuantity()).isEqualTo(smartOrder.getQuantity());
        assertThat(bestExecutionRequest.getInstrumentsList()).extracting(InstrumentKey::getInstrumentId).containsExactlyInAnyOrder(
            BTCUSD_B2C2_INSTRUMENT_ID, BTCUSD_CF_INSTRUMENT_ID);

        // no recommendations yet, SRE sends out heartbeat message
        smartRecommendationEngine.emitExecutionRecommendationsProcessing(bestExecutionRequest.getRecommendationSubscriptionId());

        // SOR doesn't send any child orders
        collider.ensureNoChildOrderRequests(smartOrder.getOrderId());

        // recommendations received from SRE
        smartRecommendationEngine.emitExecutionRecommendationsOtcDone(bestExecutionRequest.getRecommendationSubscriptionId());

        // SOR sends out new child order request
        OemsRequest childOrder = collider.awaitNewChildOrderRequest(smartOrder.getOrderId(), CF);
        assertThat(childOrder).satisfies(request -> {
            assertThat(request.getInstrumentId()).isEqualTo(BTCUSD_CF_INSTRUMENT_ID);
            assertThat(request.getMetadata().getTarget()).isEqualTo(CF);
            assertThat(request.getQuantity()).isEqualTo(smartOrder.getQuantity());
            assertThat(request.getOrderType()).isEqualTo(OEMS_CHILD_ORDER_TYPE);
            assertThat(request.getSide()).isEqualTo(smartOrder.getSide());
            assertThat(request.getParentOrderId()).isEqualTo(smartOrder.getOrderId());
        });

        // child reported as new
        collider.emitExecutionReportNew(childOrder);

        // child is filled
        collider.emitExecutionReportFilled(childOrder);

        // ER FILLED for parent
        OemsResponse parentExecFilled = orderGateway.awaitExecutionReportWithOrderStatusReceived(smartOrder.getOrderId(), OemsOrderStatus.STATUS_FILLED);
        assertThat(parentExecFilled).satisfies(response -> {
            assertExecReportFor(response, smartOrder);
            assertThat(response.getExecType()).isEqualTo(OemsExecType.FILL);
            assertThat(response.getOrderStatus()).isEqualTo(OemsOrderStatus.STATUS_FILLED);
            assertThat(response.getCumQty()).isEqualTo(smartOrder.getQuantity());
            assertThat(response.getLeavesQty()).isEqualTo("0");
            assertThat(new BigDecimal(response.getLastPrice())).isEqualByComparingTo(testingData.executionPrice);
            assertThat(new BigDecimal(response.getAvgPrice())).isEqualByComparingTo(testingData.executionPrice);
        });
    }


    @Test
    void shouldRetryOnCancel() {
        OemsRequest smartOrder = orderGateway.emitsOemsRequest(testingData.clientSmartOrderRequestOtc(OEMS_ORDER_TYPE));

        // broadcast request for execution recommendations to SRE
        BestExecutionRequest bestExecutionRequest = smartRecommendationEngine.awaitBestExecutionBroadcastRequests(smartOrder.getOrderId());
        assertThat(bestExecutionRequest.getSide()).isEqualTo(smartOrder.getSide());
        assertThat(bestExecutionRequest.getQuantity()).isEqualTo(smartOrder.getQuantity());
        assertThat(bestExecutionRequest.getInstrumentsList()).extracting(InstrumentKey::getInstrumentId).containsExactlyInAnyOrder(
            BTCUSD_B2C2_INSTRUMENT_ID, BTCUSD_CF_INSTRUMENT_ID);

        // SRE responds with immediate recommendations
        smartRecommendationEngine.emitExecutionRecommendationsOtcDone(bestExecutionRequest.getRecommendationSubscriptionId());

        // SOR sends out new child order request
        OemsRequest childOrder = collider.awaitNewChildOrderRequest(smartOrder.getOrderId(), CF);
        assertThat(childOrder).satisfies(request -> {
            assertThat(request.getInstrumentId()).isEqualTo(BTCUSD_CF_INSTRUMENT_ID);
            assertThat(request.getMetadata().getTarget()).isEqualTo(CF);
            assertThat(request.getQuantity()).isEqualTo(smartOrder.getQuantity());
            assertThat(request.getOrderType()).isEqualTo(OEMS_CHILD_ORDER_TYPE);
            assertThat(request.getSide()).isEqualTo(smartOrder.getSide());
            assertThat(request.getParentOrderId()).isEqualTo(smartOrder.getOrderId());
        });

        // child reported as new
        collider.emitExecutionReportNew(childOrder);

        // child is canceled
        collider.emitExecutionReportCanceled(childOrder);

        // SOR sends out second child order request
        OemsRequest childOrder1 = collider.awaitNewChildOrderRequest(smartOrder.getOrderId(), CF);
        assertThat(childOrder1).satisfies(request -> {
            assertThat(request.getInstrumentId()).isEqualTo(BTCUSD_CF_INSTRUMENT_ID);
            assertThat(request.getMetadata().getTarget()).isEqualTo(CF);
            assertThat(request.getQuantity()).isEqualTo(smartOrder.getQuantity());
            assertThat(request.getOrderType()).isEqualTo(OEMS_CHILD_ORDER_TYPE);
            assertThat(request.getSide()).isEqualTo(smartOrder.getSide());
            assertThat(request.getParentOrderId()).isEqualTo(smartOrder.getOrderId());
        });

        // second child is filled
        collider.emitExecutionReportFilled(childOrder1);

        // ER FILLED for parent
        OemsResponse parentExecFilled = orderGateway.awaitExecutionReportWithOrderStatusReceived(smartOrder.getOrderId(), OemsOrderStatus.STATUS_FILLED);
        assertThat(parentExecFilled).satisfies(response -> {
            assertExecReportFor(response, smartOrder);
            assertThat(response.getExecutionId()).isNotBlank();
            assertThat(response.getExecType()).isEqualTo(OemsExecType.FILL);
            assertThat(response.getOrderStatus()).isEqualTo(OemsOrderStatus.STATUS_FILLED);
            assertThat(response.getCumQty()).isEqualTo(smartOrder.getQuantity());
            assertThat(response.getLeavesQty()).isEqualTo("0");
            assertThat(new BigDecimal(response.getLastPrice())).isEqualByComparingTo(testingData.executionPrice);
            assertThat(new BigDecimal(response.getAvgPrice())).isEqualByComparingTo(testingData.executionPrice);
        });
    }

    @Test
    void shouldRetryOnReject() {
        OemsRequest smartOrder = orderGateway.emitsOemsRequest(testingData.clientSmartOrderRequestOtc(OEMS_ORDER_TYPE));

        // broadcast request for execution recommendations to SRE
        BestExecutionRequest bestExecutionRequest = smartRecommendationEngine.awaitBestExecutionBroadcastRequests(smartOrder.getOrderId());
        assertThat(bestExecutionRequest.getSide()).isEqualTo(smartOrder.getSide());
        assertThat(bestExecutionRequest.getQuantity()).isEqualTo(smartOrder.getQuantity());
        assertThat(bestExecutionRequest.getInstrumentsList()).extracting(InstrumentKey::getInstrumentId).containsExactlyInAnyOrder(
            BTCUSD_B2C2_INSTRUMENT_ID, BTCUSD_CF_INSTRUMENT_ID);

        // SRE responds with immediate recommendations
        smartRecommendationEngine.emitExecutionRecommendationsOtcDone(bestExecutionRequest.getRecommendationSubscriptionId());

        // SOR sends out new child order request
        OemsRequest childOrder = collider.awaitNewChildOrderRequest(smartOrder.getOrderId(), CF);
        assertThat(childOrder).satisfies(request -> {
            assertThat(request.getInstrumentId()).isEqualTo(BTCUSD_CF_INSTRUMENT_ID);
            assertThat(request.getMetadata().getTarget()).isEqualTo(CF);
            assertThat(request.getQuantity()).isEqualTo(smartOrder.getQuantity());
            assertThat(request.getOrderType()).isEqualTo(OEMS_CHILD_ORDER_TYPE);
            assertThat(request.getSide()).isEqualTo(smartOrder.getSide());
            assertThat(request.getParentOrderId()).isEqualTo(smartOrder.getOrderId());
        });

        // child reported as new
        collider.emitExecutionReportNew(childOrder);

        // child is rejected
        collider.emitExecutionReportRejected(childOrder);

        // SOR sends out second child order request
        OemsRequest childOrder1 = collider.awaitNewChildOrderRequest(smartOrder.getOrderId(), CF);
        assertThat(childOrder1).satisfies(request -> {
            assertThat(request.getInstrumentId()).isEqualTo(BTCUSD_CF_INSTRUMENT_ID);
            assertThat(request.getMetadata().getTarget()).isEqualTo(CF);
            assertThat(request.getQuantity()).isEqualTo(smartOrder.getQuantity());
            assertThat(request.getOrderType()).isEqualTo(OEMS_CHILD_ORDER_TYPE);
            assertThat(request.getSide()).isEqualTo(smartOrder.getSide());
            assertThat(request.getParentOrderId()).isEqualTo(smartOrder.getOrderId());
        });

        // second child is filled
        collider.emitExecutionReportFilled(childOrder1);

        // ER FILLED for parent
        OemsResponse parentExecFilled = orderGateway.awaitExecutionReportWithOrderStatusReceived(smartOrder.getOrderId(), OemsOrderStatus.STATUS_FILLED);
        assertThat(parentExecFilled).satisfies(response -> {
            assertExecReportFor(response, smartOrder);
            assertThat(response.getExecutionId()).isNotBlank();
            assertThat(response.getExecType()).isEqualTo(OemsExecType.FILL);
            assertThat(response.getOrderStatus()).isEqualTo(OemsOrderStatus.STATUS_FILLED);
            assertThat(response.getCumQty()).isEqualTo(smartOrder.getQuantity());
            assertThat(response.getLeavesQty()).isEqualTo("0");
            assertThat(new BigDecimal(response.getLastPrice())).isEqualByComparingTo(testingData.executionPrice);
            assertThat(new BigDecimal(response.getAvgPrice())).isEqualByComparingTo(testingData.executionPrice);
        });
    }

    @Test
    void shouldNotSendSecondChildOnPartialFillAndCancel() {
        OemsRequest smartOrder = orderGateway.emitsOemsRequest(testingData.clientSmartOrderRequestOtc(OEMS_ORDER_TYPE));

        // broadcast request for execution recommendations to SRE
        BestExecutionRequest bestExecutionRequest = smartRecommendationEngine.awaitBestExecutionBroadcastRequests(smartOrder.getOrderId());
        assertThat(bestExecutionRequest.getSide()).isEqualTo(smartOrder.getSide());
        assertThat(bestExecutionRequest.getQuantity()).isEqualTo(smartOrder.getQuantity());
        assertThat(bestExecutionRequest.getInstrumentsList()).extracting(InstrumentKey::getInstrumentId).containsExactlyInAnyOrder(
            BTCUSD_B2C2_INSTRUMENT_ID, BTCUSD_CF_INSTRUMENT_ID);

        // SRE responds with immediate recommendations
        smartRecommendationEngine.emitExecutionRecommendationsOtcDone(bestExecutionRequest.getRecommendationSubscriptionId());

        // SOR sends out new child order request
        OemsRequest childOrder = collider.awaitNewChildOrderRequest(smartOrder.getOrderId(), CF);
        assertThat(childOrder).satisfies(request -> {
            assertThat(request.getInstrumentId()).isEqualTo(BTCUSD_CF_INSTRUMENT_ID);
            assertThat(request.getMetadata().getTarget()).isEqualTo(CF);
            assertThat(request.getQuantity()).isEqualTo(smartOrder.getQuantity());
            assertThat(request.getOrderType()).isEqualTo(OEMS_CHILD_ORDER_TYPE);
            assertThat(request.getSide()).isEqualTo(smartOrder.getSide());
            assertThat(request.getParentOrderId()).isEqualTo(smartOrder.getOrderId());
        });

        // child reported as new
        collider.emitExecutionReportNew(childOrder);

        // child is partially filled
        collider.emitExecutionReportPartiallyFilled(childOrder);

        // ER PARTIALLY_FILLED for parent
        OemsResponse parentExecPartiallyFilled = orderGateway.awaitExecutionReportWithOrderStatusReceived(smartOrder.getOrderId(), OemsOrderStatus.STATUS_PARTIALLY_FILLED);
        assertThat(parentExecPartiallyFilled).satisfies(response -> {
            assertExecReportFor(response, smartOrder);
            assertThat(response.getExecType()).isEqualTo(OemsExecType.PARTIAL_FILL);
            assertThat(response.getOrderStatus()).isEqualTo(OemsOrderStatus.STATUS_PARTIALLY_FILLED);
            assertThat(response.getCumQty()).isEqualTo(testingData.halfQuantity);
            assertThat(response.getLeavesQty()).isEqualTo(testingData.halfQuantity);
            assertThat(new BigDecimal(response.getLastPrice())).isEqualByComparingTo(testingData.executionPrice);
            assertThat(new BigDecimal(response.getAvgPrice())).isEqualByComparingTo(testingData.executionPrice);
        });

        // child is canceled
        collider.emitExecutionReportCanceled(childOrder);

        // ER CANCELED for parent
        OemsResponse parentExecCanceled = orderGateway.awaitExecutionReportWithOrderStatusReceived(smartOrder.getOrderId(), OemsOrderStatus.STATUS_CANCELED);
        assertThat(parentExecCanceled).satisfies(response -> {
            assertExecReportFor(response, smartOrder);
            assertThat(response.getExecType()).isEqualTo(OemsExecType.CANCELED);
            assertThat(response.getOrderStatus()).isEqualTo(OemsOrderStatus.STATUS_CANCELED);
            assertThat(response.getCumQty()).isEqualTo(testingData.halfQuantity);
            assertThat(response.getLeavesQty()).isEqualTo(testingData.halfQuantity);
            assertThat(new BigDecimal(response.getLastPrice())).isEqualTo("0");
            assertThat(new BigDecimal(response.getAvgPrice())).isEqualByComparingTo(testingData.executionPrice);
        });
    }

    @Test
    void shouldNotSendSecondChildOnPartialFillAndReject() {
        OemsRequest smartOrder = orderGateway.emitsOemsRequest(testingData.clientSmartOrderRequestOtc(OEMS_ORDER_TYPE));

        // broadcast request for execution recommendations to SRE
        BestExecutionRequest bestExecutionRequest = smartRecommendationEngine.awaitBestExecutionBroadcastRequests(smartOrder.getOrderId());
        assertThat(bestExecutionRequest.getSide()).isEqualTo(smartOrder.getSide());
        assertThat(bestExecutionRequest.getQuantity()).isEqualTo(smartOrder.getQuantity());
        assertThat(bestExecutionRequest.getInstrumentsList()).extracting(InstrumentKey::getInstrumentId).containsExactlyInAnyOrder(
            BTCUSD_B2C2_INSTRUMENT_ID, BTCUSD_CF_INSTRUMENT_ID);

        // SRE responds with immediate recommendations
        smartRecommendationEngine.emitExecutionRecommendationsOtcDone(bestExecutionRequest.getRecommendationSubscriptionId());

        // SOR sends out new child order request
        OemsRequest childOrder = collider.awaitNewChildOrderRequest(smartOrder.getOrderId(), CF);
        assertThat(childOrder).satisfies(request -> {
            assertThat(request.getInstrumentId()).isEqualTo(BTCUSD_CF_INSTRUMENT_ID);
            assertThat(request.getMetadata().getTarget()).isEqualTo(CF);
            assertThat(request.getQuantity()).isEqualTo(smartOrder.getQuantity());
            assertThat(request.getOrderType()).isEqualTo(OEMS_CHILD_ORDER_TYPE);
            assertThat(request.getSide()).isEqualTo(smartOrder.getSide());
            assertThat(request.getParentOrderId()).isEqualTo(smartOrder.getOrderId());
        });

        // child reported as new
        collider.emitExecutionReportNew(childOrder);

        // child is partially filled
        collider.emitExecutionReportPartiallyFilled(childOrder);

        // ER PARTIALLY_FILLED for parent
        OemsResponse parentExecPartiallyFilled = orderGateway.awaitExecutionReportWithOrderStatusReceived(smartOrder.getOrderId(), OemsOrderStatus.STATUS_PARTIALLY_FILLED);
        assertThat(parentExecPartiallyFilled).satisfies(response -> {
            assertExecReportFor(response, smartOrder);
            assertThat(response.getExecType()).isEqualTo(OemsExecType.PARTIAL_FILL);
            assertThat(response.getOrderStatus()).isEqualTo(OemsOrderStatus.STATUS_PARTIALLY_FILLED);
            assertThat(response.getCumQty()).isEqualTo(testingData.halfQuantity);
            assertThat(response.getLeavesQty()).isEqualTo(testingData.halfQuantity);
            assertThat(new BigDecimal(response.getLastPrice())).isEqualByComparingTo(testingData.executionPrice);
            assertThat(new BigDecimal(response.getAvgPrice())).isEqualByComparingTo(testingData.executionPrice);
        });

        // child is rejected
        collider.emitExecutionReportRejected(childOrder);

        // ER CANCELED for parent
        OemsResponse parentExecCanceled = orderGateway.awaitExecutionReportWithOrderStatusReceived(smartOrder.getOrderId(), OemsOrderStatus.STATUS_CANCELED);
        assertThat(parentExecCanceled).satisfies(response -> {
            assertExecReportFor(response, smartOrder);
            assertThat(response.getExecType()).isEqualTo(OemsExecType.CANCELED);
            assertThat(response.getOrderStatus()).isEqualTo(OemsOrderStatus.STATUS_CANCELED);
            assertThat(response.getCumQty()).isEqualTo(testingData.halfQuantity);
            assertThat(response.getLeavesQty()).isEqualTo(testingData.halfQuantity);
            assertThat(new BigDecimal(response.getLastPrice())).isEqualTo("0");
            assertThat(new BigDecimal(response.getAvgPrice())).isEqualByComparingTo(testingData.executionPrice);
        });
    }

    @Test
    void shouldRetryOnSubmissionCheckTimeout() {
        // set submission grace period to < 1sec to be able to detect that quickly enough
        ReflectionTestUtils.setField(smartOrderRoutingService, "childOrderSubmissionGracePeriod", 10);

        OemsRequest smartOrder = orderGateway.emitsOemsRequest(testingData.clientSmartOrderRequestOtc(OEMS_ORDER_TYPE));

        // broadcast request for execution recommendations to SRE
        BestExecutionRequest bestExecutionRequest = smartRecommendationEngine.awaitBestExecutionBroadcastRequests(smartOrder.getOrderId());
        assertThat(bestExecutionRequest.getSide()).isEqualTo(smartOrder.getSide());
        assertThat(bestExecutionRequest.getQuantity()).isEqualTo(smartOrder.getQuantity());
        assertThat(bestExecutionRequest.getInstrumentsList()).extracting(InstrumentKey::getInstrumentId).containsExactlyInAnyOrder(
            BTCUSD_B2C2_INSTRUMENT_ID, BTCUSD_CF_INSTRUMENT_ID);

        // SRE responds with immediate recommendations
        smartRecommendationEngine.emitExecutionRecommendationsOtcDone(bestExecutionRequest.getRecommendationSubscriptionId());

        // SOR sends out new child order request
        OemsRequest childOrder = collider.awaitNewChildOrderRequest(smartOrder.getOrderId(), CF);
        assertThat(childOrder).satisfies(request -> {
            assertThat(request.getInstrumentId()).isEqualTo(BTCUSD_CF_INSTRUMENT_ID);
            assertThat(request.getMetadata().getTarget()).isEqualTo(CF);
            assertThat(request.getQuantity()).isEqualTo(smartOrder.getQuantity());
            assertThat(request.getOrderType()).isEqualTo(OEMS_CHILD_ORDER_TYPE);
            assertThat(request.getSide()).isEqualTo(smartOrder.getSide());
            assertThat(request.getParentOrderId()).isEqualTo(smartOrder.getOrderId());
        });

        // childOrderSubmissionGracePeriod times out
        // child is not reported as NEW -> child is canceled
        OemsRequest childCancel = collider.awaitChildCancelOrderRequest(smartOrder.getOrderId(), CF);
        assertThat(childCancel).satisfies(request -> {
            assertThat(request.getInstrumentId()).isEqualTo(BTCUSD_CF_INSTRUMENT_ID);
            assertThat(request.getMetadata().getTarget()).isEqualTo(CF);
            assertThat(request.getQuantity()).isEqualTo(smartOrder.getQuantity());
            assertThat(request.getOrderType()).isEqualTo(OEMS_CHILD_ORDER_TYPE);
            assertThat(request.getSide()).isEqualTo(smartOrder.getSide());
            assertThat(request.getParentOrderId()).isEqualTo(smartOrder.getOrderId());
        });

        // child reported as canceled
        collider.emitExecutionReportCanceled(childOrder);

        // SOR sends out second child for second candidate
        OemsRequest childOrder1 = collider.awaitNewChildOrderRequest(smartOrder.getOrderId(), CF);
        assertThat(childOrder1).satisfies(request -> {
            assertThat(request.getInstrumentId()).isEqualTo(BTCUSD_CF_INSTRUMENT_ID);
            assertThat(request.getMetadata().getTarget()).isEqualTo(CF);
            assertThat(request.getQuantity()).isEqualTo(smartOrder.getQuantity());
            assertThat(request.getOrderType()).isEqualTo(OEMS_CHILD_ORDER_TYPE);
            assertThat(request.getSide()).isEqualTo(smartOrder.getSide());
            assertThat(request.getParentOrderId()).isEqualTo(smartOrder.getOrderId());
        });

        // second child is filled
        collider.emitExecutionReportFilled(childOrder1);

        // ER FILLED for parent
        OemsResponse parentExecFilled = orderGateway.awaitExecutionReportWithOrderStatusReceived(smartOrder.getOrderId(), OemsOrderStatus.STATUS_FILLED);
        assertThat(parentExecFilled).satisfies(response -> {
            assertExecReportFor(response, smartOrder);
            assertThat(response.getExecutionId()).isNotBlank();
            assertThat(response.getExecType()).isEqualTo(OemsExecType.FILL);
            assertThat(response.getOrderStatus()).isEqualTo(OemsOrderStatus.STATUS_FILLED);
            assertThat(response.getCumQty()).isEqualTo(smartOrder.getQuantity());
            assertThat(response.getLeavesQty()).isEqualTo("0");
            assertThat(new BigDecimal(response.getLastPrice())).isEqualByComparingTo(testingData.executionPrice);
            assertThat(new BigDecimal(response.getAvgPrice())).isEqualByComparingTo(testingData.executionPrice);
        });
    }

    @Test
    void shouldCancelAllChildrenOnStopLimitOrderCancel() {
        OemsRequest smartOrder = orderGateway.emitsOemsRequest(testingData.clientSmartOrderRequestOtc(OEMS_ORDER_TYPE));

        BestExecutionRequest bestExecutionRequest = smartRecommendationEngine.awaitBestExecutionBroadcastRequests(smartOrder.getOrderId());
        assertThat(bestExecutionRequest.getSide()).isEqualTo(smartOrder.getSide());
        assertThat(bestExecutionRequest.getQuantity()).isEqualTo(smartOrder.getQuantity());
        assertThat(bestExecutionRequest.getInstrumentsList()).extracting(InstrumentKey::getInstrumentId).containsExactlyInAnyOrder(
            BTCUSD_B2C2_INSTRUMENT_ID, BTCUSD_CF_INSTRUMENT_ID);

        // SRE responds with immediate recommendations
        smartRecommendationEngine.emitExecutionRecommendationsOtcDone(bestExecutionRequest.getRecommendationSubscriptionId());

        // SOR sends out new child order request
        OemsRequest childOrder = collider.awaitNewChildOrderRequest(smartOrder.getOrderId(), CF);
        assertThat(childOrder).satisfies(request -> {
            assertThat(request.getInstrumentId()).isEqualTo(BTCUSD_CF_INSTRUMENT_ID);
            assertThat(request.getMetadata().getTarget()).isEqualTo(CF);
            assertThat(request.getQuantity()).isEqualTo(smartOrder.getQuantity());
            assertThat(request.getOrderType()).isEqualTo(OEMS_CHILD_ORDER_TYPE);
            assertThat(request.getSide()).isEqualTo(smartOrder.getSide());
            assertThat(request.getParentOrderId()).isEqualTo(smartOrder.getOrderId());
        });

        // child reported as new
        collider.emitExecutionReportNew(childOrder);

        // request to cancel parent order
        orderGateway.emitsOemsRequest(testingData.clientCancelOrderRequestOtc(OEMS_ORDER_TYPE));

        // child is canceled
        OemsRequest childCancel = collider.awaitChildCancelOrderRequest(smartOrder.getOrderId(), CF);
        assertThat(childCancel).satisfies(request -> {
            assertThat(request.getInstrumentId()).isEqualTo(BTCUSD_CF_INSTRUMENT_ID);
            assertThat(request.getMetadata().getTarget()).isEqualTo(CF);
            assertThat(request.getQuantity()).isEqualTo(smartOrder.getQuantity());
            assertThat(request.getOrderType()).isEqualTo(OEMS_CHILD_ORDER_TYPE);
            assertThat(request.getSide()).isEqualTo(smartOrder.getSide());
            assertThat(request.getParentOrderId()).isEqualTo(smartOrder.getOrderId());
        });

        // child reported as canceled
        collider.emitExecutionReportCanceled(childOrder);

        // ER CANCELED for parent
        OemsResponse parentExecCanceled = orderGateway.awaitExecutionReportWithOrderStatusReceived(smartOrder.getOrderId(), OemsOrderStatus.STATUS_CANCELED);
        assertThat(parentExecCanceled).satisfies(response -> {
            assertExecReportFor(response, smartOrder);
            assertThat(response.getExecType()).isEqualTo(OemsExecType.CANCELED);
            assertThat(response.getOrderStatus()).isEqualTo(OemsOrderStatus.STATUS_CANCELED);
            assertThat(response.getCumQty()).isEqualTo("0");
            assertThat(response.getLeavesQty()).isEqualTo(smartOrder.getQuantity());
            assertThat(response.getLastPrice()).isEqualTo("0");
            assertThat(response.getAvgPrice()).isEqualTo("0");
        });
    }

    @Test
    void shouldNotCancelParentWhenNotAllChildrenAreCanceled() {
        OemsRequest smartOrder = orderGateway.emitsOemsRequest(testingData.clientSmartOrderRequestOtc(OEMS_ORDER_TYPE));

        BestExecutionRequest bestExecutionRequest = smartRecommendationEngine.awaitBestExecutionBroadcastRequests(smartOrder.getOrderId());
        assertThat(bestExecutionRequest.getSide()).isEqualTo(smartOrder.getSide());
        assertThat(bestExecutionRequest.getQuantity()).isEqualTo(smartOrder.getQuantity());
        assertThat(bestExecutionRequest.getInstrumentsList()).extracting(InstrumentKey::getInstrumentId).containsExactlyInAnyOrder(
            BTCUSD_B2C2_INSTRUMENT_ID, BTCUSD_CF_INSTRUMENT_ID);

        // SRE responds with immediate recommendations
        smartRecommendationEngine.emitExecutionRecommendationsOtcDone(bestExecutionRequest.getRecommendationSubscriptionId());

        // SOR sends out new child order request
        OemsRequest childOrder = collider.awaitNewChildOrderRequest(smartOrder.getOrderId(), CF);
        assertThat(childOrder).satisfies(request -> {
            assertThat(request.getInstrumentId()).isEqualTo(BTCUSD_CF_INSTRUMENT_ID);
            assertThat(request.getMetadata().getTarget()).isEqualTo(CF);
            assertThat(request.getQuantity()).isEqualTo(smartOrder.getQuantity());
            assertThat(request.getOrderType()).isEqualTo(OEMS_CHILD_ORDER_TYPE);
            assertThat(request.getSide()).isEqualTo(smartOrder.getSide());
            assertThat(request.getParentOrderId()).isEqualTo(smartOrder.getOrderId());
        });

        // child reported as new
        collider.emitExecutionReportNew(childOrder);

        // request to cancel parent order
        orderGateway.emitsOemsRequest(testingData.clientCancelOrderRequestOtc(OEMS_ORDER_TYPE));

        // child is canceled
        OemsRequest childCancel = collider.awaitChildCancelOrderRequest(smartOrder.getOrderId(), CF);
        assertThat(childCancel).satisfies(request -> {
            assertThat(request.getInstrumentId()).isEqualTo(BTCUSD_CF_INSTRUMENT_ID);
            assertThat(request.getMetadata().getTarget()).isEqualTo(CF);
            assertThat(request.getQuantity()).isEqualTo(smartOrder.getQuantity());
            assertThat(request.getOrderType()).isEqualTo(OEMS_CHILD_ORDER_TYPE);
            assertThat(request.getSide()).isEqualTo(smartOrder.getSide());
            assertThat(request.getParentOrderId()).isEqualTo(smartOrder.getOrderId());
        });

        // parent is in status pending cancel
        OemsResponse parentExecCanceled = orderGateway.awaitExecutionReportWithOrderStatusReceived(smartOrder.getOrderId(), OemsOrderStatus.STATUS_PENDING_CANCEL);
        assertThat(parentExecCanceled).satisfies(response -> {
            assertExecReportFor(response, smartOrder);
            assertThat(response.getExecType()).isEqualTo(OemsExecType.PENDING_CANCEL);
            assertThat(response.getOrderStatus()).isEqualTo(OemsOrderStatus.STATUS_PENDING_CANCEL);
            assertThat(response.getCumQty()).isEqualTo("0");
            assertThat(response.getLeavesQty()).isEqualTo(smartOrder.getQuantity());
            assertThat(response.getLastPrice()).isEqualTo("0");
            assertThat(response.getAvgPrice()).isEqualTo("0");
        });

        // no ER for child cancel has been received -> parent is not canceled
        boolean statusCanceledPresent = orderGateway.isExecutionReportPresentWithOrderStatusReceived(smartOrder.getOrderId(), OemsOrderStatus.STATUS_CANCELED);
        assertThat(!statusCanceledPresent);
    }

    @Test
    void shouldSendNewBroadcastRequestWithOneCandidateOnCandidatesSuspensionTimeoutAndReject() throws InterruptedException {
        // set candidate suspension timeout to 4sec to be able to quickly detect it
        ReflectionTestUtils.setField(smartOrderRoutingService, "smartOrderCandidateSuspensionPeriod", 4000);

        OemsRequest smartOrder = orderGateway.emitsOemsRequest(testingData.clientSmartOrderRequestOtc(OEMS_ORDER_TYPE));

        // broadcast request for execution recommendations to SRE
        BestExecutionRequest bestExecutionRequest = smartRecommendationEngine.awaitBestExecutionBroadcastRequests(smartOrder.getOrderId());
        assertThat(bestExecutionRequest.getSide()).isEqualTo(smartOrder.getSide());
        assertThat(bestExecutionRequest.getQuantity()).isEqualTo(smartOrder.getQuantity());
        assertThat(bestExecutionRequest.getInstrumentsList()).extracting(InstrumentKey::getInstrumentId).containsExactlyInAnyOrder(
            BTCUSD_B2C2_INSTRUMENT_ID, BTCUSD_CF_INSTRUMENT_ID);

        // SRE responds with immediate recommendations
        smartRecommendationEngine.emitExecutionRecommendationsOtcDone(bestExecutionRequest.getRecommendationSubscriptionId());

        // SOR sends out new child order request
        OemsRequest childOrder = collider.awaitNewChildOrderRequest(smartOrder.getOrderId(), CF);
        assertThat(childOrder).satisfies(request -> {
            assertThat(request.getInstrumentId()).isEqualTo(BTCUSD_CF_INSTRUMENT_ID);
            assertThat(request.getMetadata().getTarget()).isEqualTo(CF);
            assertThat(request.getQuantity()).isEqualTo(smartOrder.getQuantity());
            assertThat(request.getOrderType()).isEqualTo(OEMS_CHILD_ORDER_TYPE);
            assertThat(request.getSide()).isEqualTo(smartOrder.getSide());
            assertThat(request.getParentOrderId()).isEqualTo(smartOrder.getOrderId());
        });

        // child reported as new
        collider.emitExecutionReportNew(childOrder);

        // child is rejected
        collider.emitExecutionReportRejected(childOrder);

        // SOR sends out second child order request
        OemsRequest childOrder1 = collider.awaitNewChildOrderRequest(smartOrder.getOrderId(), CF);
        assertThat(childOrder1).satisfies(request -> {
            assertThat(request.getInstrumentId()).isEqualTo(BTCUSD_CF_INSTRUMENT_ID);
            assertThat(request.getMetadata().getTarget()).isEqualTo(CF);
            assertThat(request.getQuantity()).isEqualTo(smartOrder.getQuantity());
            assertThat(request.getOrderType()).isEqualTo(OEMS_CHILD_ORDER_TYPE);
            assertThat(request.getSide()).isEqualTo(smartOrder.getSide());
            assertThat(request.getParentOrderId()).isEqualTo(smartOrder.getOrderId());
        });

        // child reported as new
        collider.emitExecutionReportNew(childOrder1);

        // child is rejected
        collider.emitExecutionReportRejected(childOrder1);

        // SOR sends out third child order request
        OemsRequest childOrder2 = collider.awaitNewChildOrderRequest(smartOrder.getOrderId(), CF);
        assertThat(childOrder2).satisfies(request -> {
            assertThat(request.getInstrumentId()).isEqualTo(BTCUSD_CF_INSTRUMENT_ID);
            assertThat(request.getMetadata().getTarget()).isEqualTo(CF);
            assertThat(request.getQuantity()).isEqualTo(smartOrder.getQuantity());
            assertThat(request.getOrderType()).isEqualTo(OEMS_CHILD_ORDER_TYPE);
            assertThat(request.getSide()).isEqualTo(smartOrder.getSide());
            assertThat(request.getParentOrderId()).isEqualTo(smartOrder.getOrderId());
        });

        // child reported as new
        collider.emitExecutionReportNew(childOrder2);

        // wait a little
        TimeUnit.SECONDS.sleep(2);

        // third child is rejected, candidate is suspended for 4sec
        collider.emitExecutionReportRejected(childOrder2);

        // SOR sends out child order request for second candidate
        OemsRequest childOrder3 = collider.awaitNewChildOrderRequest(smartOrder.getOrderId(), B2C2);
        assertThat(childOrder3).satisfies(request -> {
            assertThat(request.getInstrumentId()).isEqualTo(BTCUSD_B2C2_INSTRUMENT_ID);
            assertThat(request.getMetadata().getTarget()).isEqualTo(B2C2);
            assertThat(request.getQuantity()).isEqualTo(smartOrder.getQuantity());
            assertThat(request.getOrderType()).isEqualTo(OEMS_CHILD_ORDER_TYPE);
            assertThat(request.getSide()).isEqualTo(smartOrder.getSide());
            assertThat(request.getParentOrderId()).isEqualTo(smartOrder.getOrderId());
        });

        // child reported as new
        collider.emitExecutionReportNew(childOrder3);

        // child is rejected
        collider.emitExecutionReportRejected(childOrder3);

        // SOR sends out second child order request
        OemsRequest childOrder4 = collider.awaitNewChildOrderRequest(smartOrder.getOrderId(), B2C2);
        assertThat(childOrder4).satisfies(request -> {
            assertThat(request.getInstrumentId()).isEqualTo(BTCUSD_B2C2_INSTRUMENT_ID);
            assertThat(request.getMetadata().getTarget()).isEqualTo(B2C2);
            assertThat(request.getQuantity()).isEqualTo(smartOrder.getQuantity());
            assertThat(request.getOrderType()).isEqualTo(OEMS_CHILD_ORDER_TYPE);
            assertThat(request.getSide()).isEqualTo(smartOrder.getSide());
            assertThat(request.getParentOrderId()).isEqualTo(smartOrder.getOrderId());
        });

        // child reported as new
        collider.emitExecutionReportNew(childOrder4);

        // child is rejected
        collider.emitExecutionReportRejected(childOrder4);

        // SOR sends out third child order request
        OemsRequest childOrder5 = collider.awaitNewChildOrderRequest(smartOrder.getOrderId(), B2C2);
        assertThat(childOrder5).satisfies(request -> {
            assertThat(request.getInstrumentId()).isEqualTo(BTCUSD_B2C2_INSTRUMENT_ID);
            assertThat(request.getMetadata().getTarget()).isEqualTo(B2C2);
            assertThat(request.getQuantity()).isEqualTo(smartOrder.getQuantity());
            assertThat(request.getOrderType()).isEqualTo(OEMS_CHILD_ORDER_TYPE);
            assertThat(request.getSide()).isEqualTo(smartOrder.getSide());
            assertThat(request.getParentOrderId()).isEqualTo(smartOrder.getOrderId());
        });

        // child reported as new
        collider.emitExecutionReportNew(childOrder5);

        // wait a little till first candidate is not suspended anymore
        TimeUnit.SECONDS.sleep(2);

        // third child is rejected, candidate is suspended for 4sec
        collider.emitExecutionReportRejected(childOrder5);

        // broadcast request for execution recommendations to SRE with just one candidate (CF)
        bestExecutionRequest = smartRecommendationEngine.awaitBestExecutionBroadcastRequests(smartOrder.getOrderId());
        assertThat(bestExecutionRequest.getSide()).isEqualTo(smartOrder.getSide());
        assertThat(bestExecutionRequest.getQuantity()).isEqualTo(smartOrder.getQuantity());
        assertThat(bestExecutionRequest.getInstrumentsList()).extracting(InstrumentKey::getInstrumentId).containsExactly(BTCUSD_CF_INSTRUMENT_ID);

        // SRE responds with recommendations for CF
        smartRecommendationEngine.emitExecutionRecommendationsDoneForCF(bestExecutionRequest.getRecommendationSubscriptionId());

        // SOR sends out new child order request
        OemsRequest childOrder6 = collider.awaitNewChildOrderRequest(smartOrder.getOrderId(), CF);
        assertThat(childOrder6).satisfies(request -> {
            assertThat(request.getInstrumentId()).isEqualTo(BTCUSD_CF_INSTRUMENT_ID);
            assertThat(request.getMetadata().getTarget()).isEqualTo(CF);
            assertThat(request.getQuantity()).isEqualTo(smartOrder.getQuantity());
            assertThat(request.getOrderType()).isEqualTo(OEMS_CHILD_ORDER_TYPE);
            assertThat(request.getSide()).isEqualTo(smartOrder.getSide());
            assertThat(request.getParentOrderId()).isEqualTo(smartOrder.getOrderId());
        });

        // child reported as new
        collider.emitExecutionReportNew(childOrder6);

        // child is rejected
        collider.emitExecutionReportRejected(childOrder6);

        // SOR sends out second child order request
        OemsRequest childOrder7 = collider.awaitNewChildOrderRequest(smartOrder.getOrderId(), CF);
        assertThat(childOrder7).satisfies(request -> {
            assertThat(request.getInstrumentId()).isEqualTo(BTCUSD_CF_INSTRUMENT_ID);
            assertThat(request.getMetadata().getTarget()).isEqualTo(CF);
            assertThat(request.getQuantity()).isEqualTo(smartOrder.getQuantity());
            assertThat(request.getOrderType()).isEqualTo(OEMS_CHILD_ORDER_TYPE);
            assertThat(request.getSide()).isEqualTo(smartOrder.getSide());
            assertThat(request.getParentOrderId()).isEqualTo(smartOrder.getOrderId());
        });

        // child reported as new
        collider.emitExecutionReportNew(childOrder7);

        // child is rejected
        collider.emitExecutionReportRejected(childOrder7);

        // SOR sends out third child order request
        OemsRequest childOrder8 = collider.awaitNewChildOrderRequest(smartOrder.getOrderId(), CF);
        assertThat(childOrder8).satisfies(request -> {
            assertThat(request.getInstrumentId()).isEqualTo(BTCUSD_CF_INSTRUMENT_ID);
            assertThat(request.getMetadata().getTarget()).isEqualTo(CF);
            assertThat(request.getQuantity()).isEqualTo(smartOrder.getQuantity());
            assertThat(request.getOrderType()).isEqualTo(OEMS_CHILD_ORDER_TYPE);
            assertThat(request.getSide()).isEqualTo(smartOrder.getSide());
            assertThat(request.getParentOrderId()).isEqualTo(smartOrder.getOrderId());
        });

        // child reported as new
        collider.emitExecutionReportNew(childOrder8);

        // wait a little till second candidate is not suspended anymore
        TimeUnit.SECONDS.sleep(2);

        // child is rejected, candidate is suspended for 4sec
        collider.emitExecutionReportRejected(childOrder8);

        // broadcast request for execution recommendations to SRE with just one candidate (B2C2)
        bestExecutionRequest = smartRecommendationEngine.awaitBestExecutionBroadcastRequests(smartOrder.getOrderId());
        assertThat(bestExecutionRequest.getSide()).isEqualTo(smartOrder.getSide());
        assertThat(bestExecutionRequest.getQuantity()).isEqualTo(smartOrder.getQuantity());
        assertThat(bestExecutionRequest.getInstrumentsList()).extracting(InstrumentKey::getInstrumentId).containsExactly(BTCUSD_B2C2_INSTRUMENT_ID);
    }

    @Test
    void shouldNotSendNewBroadcastRequestBeforeCandidatesSuspensionTimeoutAndReject() {
        OemsRequest smartOrder = orderGateway.emitsOemsRequest(testingData.clientSmartOrderRequestOtc(OEMS_ORDER_TYPE));

        // broadcast request for execution recommendations to SRE
        BestExecutionRequest bestExecutionRequest = smartRecommendationEngine.awaitBestExecutionBroadcastRequests(smartOrder.getOrderId());
        assertThat(bestExecutionRequest.getSide()).isEqualTo(smartOrder.getSide());
        assertThat(bestExecutionRequest.getQuantity()).isEqualTo(smartOrder.getQuantity());
        assertThat(bestExecutionRequest.getInstrumentsList()).extracting(InstrumentKey::getInstrumentId).containsExactlyInAnyOrder(
            BTCUSD_B2C2_INSTRUMENT_ID, BTCUSD_CF_INSTRUMENT_ID);

        // SRE responds with immediate recommendations
        smartRecommendationEngine.emitExecutionRecommendationsOtcDone(bestExecutionRequest.getRecommendationSubscriptionId());

        // SOR sends out new child order request
        OemsRequest childOrder = collider.awaitNewChildOrderRequest(smartOrder.getOrderId(), CF);
        assertThat(childOrder).satisfies(request -> {
            assertThat(request.getInstrumentId()).isEqualTo(BTCUSD_CF_INSTRUMENT_ID);
            assertThat(request.getMetadata().getTarget()).isEqualTo(CF);
            assertThat(request.getQuantity()).isEqualTo(smartOrder.getQuantity());
            assertThat(request.getOrderType()).isEqualTo(OEMS_CHILD_ORDER_TYPE);
            assertThat(request.getSide()).isEqualTo(smartOrder.getSide());
            assertThat(request.getParentOrderId()).isEqualTo(smartOrder.getOrderId());
        });

        // child reported as new
        collider.emitExecutionReportNew(childOrder);

        // child is rejected
        collider.emitExecutionReportRejected(childOrder);

        // SOR sends out second child order request
        OemsRequest childOrder1 = collider.awaitNewChildOrderRequest(smartOrder.getOrderId(), CF);
        assertThat(childOrder1).satisfies(request -> {
            assertThat(request.getInstrumentId()).isEqualTo(BTCUSD_CF_INSTRUMENT_ID);
            assertThat(request.getMetadata().getTarget()).isEqualTo(CF);
            assertThat(request.getQuantity()).isEqualTo(smartOrder.getQuantity());
            assertThat(request.getOrderType()).isEqualTo(OEMS_CHILD_ORDER_TYPE);
            assertThat(request.getSide()).isEqualTo(smartOrder.getSide());
            assertThat(request.getParentOrderId()).isEqualTo(smartOrder.getOrderId());
        });

        // child reported as new
        collider.emitExecutionReportNew(childOrder1);

        // child is rejected
        collider.emitExecutionReportRejected(childOrder1);

        // SOR sends out third child order request
        OemsRequest childOrder2 = collider.awaitNewChildOrderRequest(smartOrder.getOrderId(), CF);
        assertThat(childOrder2).satisfies(request -> {
            assertThat(request.getInstrumentId()).isEqualTo(BTCUSD_CF_INSTRUMENT_ID);
            assertThat(request.getMetadata().getTarget()).isEqualTo(CF);
            assertThat(request.getQuantity()).isEqualTo(smartOrder.getQuantity());
            assertThat(request.getOrderType()).isEqualTo(OEMS_CHILD_ORDER_TYPE);
            assertThat(request.getSide()).isEqualTo(smartOrder.getSide());
            assertThat(request.getParentOrderId()).isEqualTo(smartOrder.getOrderId());
        });

        // child reported as new
        collider.emitExecutionReportNew(childOrder2);

        // third child is rejected, candidate is suspended for 8sec
        collider.emitExecutionReportRejected(childOrder2);

        // SOR sends out child order request for second candidate
        OemsRequest childOrder3 = collider.awaitNewChildOrderRequest(smartOrder.getOrderId(), B2C2);
        assertThat(childOrder3).satisfies(request -> {
            assertThat(request.getInstrumentId()).isEqualTo(BTCUSD_B2C2_INSTRUMENT_ID);
            assertThat(request.getMetadata().getTarget()).isEqualTo(B2C2);
            assertThat(request.getQuantity()).isEqualTo(smartOrder.getQuantity());
            assertThat(request.getOrderType()).isEqualTo(OEMS_CHILD_ORDER_TYPE);
            assertThat(request.getSide()).isEqualTo(smartOrder.getSide());
            assertThat(request.getParentOrderId()).isEqualTo(smartOrder.getOrderId());
        });

        // child reported as new
        collider.emitExecutionReportNew(childOrder3);

        // child is rejected
        collider.emitExecutionReportRejected(childOrder3);

        // SOR sends out second child order request
        OemsRequest childOrder4 = collider.awaitNewChildOrderRequest(smartOrder.getOrderId(), B2C2);
        assertThat(childOrder4).satisfies(request -> {
            assertThat(request.getInstrumentId()).isEqualTo(BTCUSD_B2C2_INSTRUMENT_ID);
            assertThat(request.getMetadata().getTarget()).isEqualTo(B2C2);
            assertThat(request.getQuantity()).isEqualTo(smartOrder.getQuantity());
            assertThat(request.getOrderType()).isEqualTo(OEMS_CHILD_ORDER_TYPE);
            assertThat(request.getSide()).isEqualTo(smartOrder.getSide());
            assertThat(request.getParentOrderId()).isEqualTo(smartOrder.getOrderId());
        });

        // child reported as new
        collider.emitExecutionReportNew(childOrder4);

        // child is rejected
        collider.emitExecutionReportRejected(childOrder4);

        // SOR sends out third child order request
        OemsRequest childOrder5 = collider.awaitNewChildOrderRequest(smartOrder.getOrderId(), B2C2);
        assertThat(childOrder5).satisfies(request -> {
            assertThat(request.getInstrumentId()).isEqualTo(BTCUSD_B2C2_INSTRUMENT_ID);
            assertThat(request.getMetadata().getTarget()).isEqualTo(B2C2);
            assertThat(request.getQuantity()).isEqualTo(smartOrder.getQuantity());
            assertThat(request.getOrderType()).isEqualTo(OEMS_CHILD_ORDER_TYPE);
            assertThat(request.getSide()).isEqualTo(smartOrder.getSide());
            assertThat(request.getParentOrderId()).isEqualTo(smartOrder.getOrderId());
        });

        // child reported as new
        collider.emitExecutionReportNew(childOrder5);

        // third child is rejected, candidate is suspended for 8sec
        collider.emitExecutionReportRejected(childOrder5);

        // both candidates are suspended
        smartRecommendationEngine.ensureNoMoreBestExecutionRequests(smartOrder.getOrderId());
    }

    @Test
    void shouldSendNewBroadcastRequestWithOneCandidateOnCandidatesSuspensionTimeoutAndCancel() throws InterruptedException {
        // set candidate suspension timeout to 4sec to be able to quickly detect it
        ReflectionTestUtils.setField(smartOrderRoutingService, "smartOrderCandidateSuspensionPeriod", 4000);

        OemsRequest smartOrder = orderGateway.emitsOemsRequest(testingData.clientSmartOrderRequestOtc(OEMS_ORDER_TYPE));

        // broadcast request for execution recommendations to SRE
        BestExecutionRequest bestExecutionRequest = smartRecommendationEngine.awaitBestExecutionBroadcastRequests(smartOrder.getOrderId());
        assertThat(bestExecutionRequest.getSide()).isEqualTo(smartOrder.getSide());
        assertThat(bestExecutionRequest.getQuantity()).isEqualTo(smartOrder.getQuantity());
        assertThat(bestExecutionRequest.getInstrumentsList()).extracting(InstrumentKey::getInstrumentId).containsExactlyInAnyOrder(
            BTCUSD_B2C2_INSTRUMENT_ID, BTCUSD_CF_INSTRUMENT_ID);

        // SRE responds with immediate recommendations
        smartRecommendationEngine.emitExecutionRecommendationsOtcDone(bestExecutionRequest.getRecommendationSubscriptionId());

        // SOR sends out new child order request
        OemsRequest childOrder = collider.awaitNewChildOrderRequest(smartOrder.getOrderId(), CF);
        assertThat(childOrder).satisfies(request -> {
            assertThat(request.getInstrumentId()).isEqualTo(BTCUSD_CF_INSTRUMENT_ID);
            assertThat(request.getMetadata().getTarget()).isEqualTo(CF);
            assertThat(request.getQuantity()).isEqualTo(smartOrder.getQuantity());
            assertThat(request.getOrderType()).isEqualTo(OEMS_CHILD_ORDER_TYPE);
            assertThat(request.getSide()).isEqualTo(smartOrder.getSide());
            assertThat(request.getParentOrderId()).isEqualTo(smartOrder.getOrderId());
        });

        // child reported as new
        collider.emitExecutionReportNew(childOrder);

        // child is canceled
        collider.emitExecutionReportCanceled(childOrder);

        // SOR sends out second child order request
        OemsRequest childOrder1 = collider.awaitNewChildOrderRequest(smartOrder.getOrderId(), CF);
        assertThat(childOrder1).satisfies(request -> {
            assertThat(request.getInstrumentId()).isEqualTo(BTCUSD_CF_INSTRUMENT_ID);
            assertThat(request.getMetadata().getTarget()).isEqualTo(CF);
            assertThat(request.getQuantity()).isEqualTo(smartOrder.getQuantity());
            assertThat(request.getOrderType()).isEqualTo(OEMS_CHILD_ORDER_TYPE);
            assertThat(request.getSide()).isEqualTo(smartOrder.getSide());
            assertThat(request.getParentOrderId()).isEqualTo(smartOrder.getOrderId());
        });

        // child reported as new
        collider.emitExecutionReportNew(childOrder1);

        // child is canceled
        collider.emitExecutionReportCanceled(childOrder1);

        // SOR sends out third child order request
        OemsRequest childOrder2 = collider.awaitNewChildOrderRequest(smartOrder.getOrderId(), CF);
        assertThat(childOrder2).satisfies(request -> {
            assertThat(request.getInstrumentId()).isEqualTo(BTCUSD_CF_INSTRUMENT_ID);
            assertThat(request.getMetadata().getTarget()).isEqualTo(CF);
            assertThat(request.getQuantity()).isEqualTo(smartOrder.getQuantity());
            assertThat(request.getOrderType()).isEqualTo(OEMS_CHILD_ORDER_TYPE);
            assertThat(request.getSide()).isEqualTo(smartOrder.getSide());
            assertThat(request.getParentOrderId()).isEqualTo(smartOrder.getOrderId());
        });

        // child reported as new
        collider.emitExecutionReportNew(childOrder2);

        // wait a little
        TimeUnit.SECONDS.sleep(2);

        // third child is canceled, candidate is suspended for 4sec
        collider.emitExecutionReportCanceled(childOrder2);

        // SOR sends out child order request for second candidate
        OemsRequest childOrder3 = collider.awaitNewChildOrderRequest(smartOrder.getOrderId(), B2C2);
        assertThat(childOrder3).satisfies(request -> {
            assertThat(request.getInstrumentId()).isEqualTo(BTCUSD_B2C2_INSTRUMENT_ID);
            assertThat(request.getMetadata().getTarget()).isEqualTo(B2C2);
            assertThat(request.getQuantity()).isEqualTo(smartOrder.getQuantity());
            assertThat(request.getOrderType()).isEqualTo(OEMS_CHILD_ORDER_TYPE);
            assertThat(request.getSide()).isEqualTo(smartOrder.getSide());
            assertThat(request.getParentOrderId()).isEqualTo(smartOrder.getOrderId());
        });

        // child reported as new
        collider.emitExecutionReportNew(childOrder3);

        // child is canceled
        collider.emitExecutionReportCanceled(childOrder3);

        // SOR sends out second child order request
        OemsRequest childOrder4 = collider.awaitNewChildOrderRequest(smartOrder.getOrderId(), B2C2);
        assertThat(childOrder4).satisfies(request -> {
            assertThat(request.getInstrumentId()).isEqualTo(BTCUSD_B2C2_INSTRUMENT_ID);
            assertThat(request.getMetadata().getTarget()).isEqualTo(B2C2);
            assertThat(request.getQuantity()).isEqualTo(smartOrder.getQuantity());
            assertThat(request.getOrderType()).isEqualTo(OEMS_CHILD_ORDER_TYPE);
            assertThat(request.getSide()).isEqualTo(smartOrder.getSide());
            assertThat(request.getParentOrderId()).isEqualTo(smartOrder.getOrderId());
        });

        // child reported as new
        collider.emitExecutionReportNew(childOrder4);

        // child is canceled
        collider.emitExecutionReportCanceled(childOrder4);

        // SOR sends out third child order request
        OemsRequest childOrder5 = collider.awaitNewChildOrderRequest(smartOrder.getOrderId(), B2C2);
        assertThat(childOrder5).satisfies(request -> {
            assertThat(request.getInstrumentId()).isEqualTo(BTCUSD_B2C2_INSTRUMENT_ID);
            assertThat(request.getMetadata().getTarget()).isEqualTo(B2C2);
            assertThat(request.getQuantity()).isEqualTo(smartOrder.getQuantity());
            assertThat(request.getOrderType()).isEqualTo(OEMS_CHILD_ORDER_TYPE);
            assertThat(request.getSide()).isEqualTo(smartOrder.getSide());
            assertThat(request.getParentOrderId()).isEqualTo(smartOrder.getOrderId());
        });

        // child reported as new
        collider.emitExecutionReportNew(childOrder5);

        // wait a little till first candidate is not suspended anymore
        TimeUnit.SECONDS.sleep(2);

        // third child is canceled, candidate is suspended for 4sec
        collider.emitExecutionReportCanceled(childOrder5);

        // broadcast request for execution recommendations to SRE with just one candidate (CF)
        bestExecutionRequest = smartRecommendationEngine.awaitBestExecutionBroadcastRequests(smartOrder.getOrderId());
        assertThat(bestExecutionRequest.getSide()).isEqualTo(smartOrder.getSide());
        assertThat(bestExecutionRequest.getQuantity()).isEqualTo(smartOrder.getQuantity());
        assertThat(bestExecutionRequest.getInstrumentsList()).extracting(InstrumentKey::getInstrumentId).containsExactly(BTCUSD_CF_INSTRUMENT_ID);

        // SRE responds with recommendations for CF
        smartRecommendationEngine.emitExecutionRecommendationsDoneForCF(bestExecutionRequest.getRecommendationSubscriptionId());

        // SOR sends out new child order request
        OemsRequest childOrder6 = collider.awaitNewChildOrderRequest(smartOrder.getOrderId(), CF);
        assertThat(childOrder6).satisfies(request -> {
            assertThat(request.getInstrumentId()).isEqualTo(BTCUSD_CF_INSTRUMENT_ID);
            assertThat(request.getMetadata().getTarget()).isEqualTo(CF);
            assertThat(request.getQuantity()).isEqualTo(smartOrder.getQuantity());
            assertThat(request.getOrderType()).isEqualTo(OEMS_CHILD_ORDER_TYPE);
            assertThat(request.getSide()).isEqualTo(smartOrder.getSide());
            assertThat(request.getParentOrderId()).isEqualTo(smartOrder.getOrderId());
        });

        // child reported as new
        collider.emitExecutionReportNew(childOrder6);

        // child is canceled
        collider.emitExecutionReportCanceled(childOrder6);

        // SOR sends out second child order request
        OemsRequest childOrder7 = collider.awaitNewChildOrderRequest(smartOrder.getOrderId(), CF);
        assertThat(childOrder7).satisfies(request -> {
            assertThat(request.getInstrumentId()).isEqualTo(BTCUSD_CF_INSTRUMENT_ID);
            assertThat(request.getMetadata().getTarget()).isEqualTo(CF);
            assertThat(request.getQuantity()).isEqualTo(smartOrder.getQuantity());
            assertThat(request.getOrderType()).isEqualTo(OEMS_CHILD_ORDER_TYPE);
            assertThat(request.getSide()).isEqualTo(smartOrder.getSide());
            assertThat(request.getParentOrderId()).isEqualTo(smartOrder.getOrderId());
        });

        // child reported as new
        collider.emitExecutionReportNew(childOrder7);

        // child is canceled
        collider.emitExecutionReportCanceled(childOrder7);

        // SOR sends out third child order request
        OemsRequest childOrder8 = collider.awaitNewChildOrderRequest(smartOrder.getOrderId(), CF);
        assertThat(childOrder8).satisfies(request -> {
            assertThat(request.getInstrumentId()).isEqualTo(BTCUSD_CF_INSTRUMENT_ID);
            assertThat(request.getMetadata().getTarget()).isEqualTo(CF);
            assertThat(request.getQuantity()).isEqualTo(smartOrder.getQuantity());
            assertThat(request.getOrderType()).isEqualTo(OEMS_CHILD_ORDER_TYPE);
            assertThat(request.getSide()).isEqualTo(smartOrder.getSide());
            assertThat(request.getParentOrderId()).isEqualTo(smartOrder.getOrderId());
        });

        // child reported as new
        collider.emitExecutionReportNew(childOrder8);

        // wait a little till second candidate is not suspended anymore
        TimeUnit.SECONDS.sleep(2);

        // child is canceled, candidate is suspended for 4sec
        collider.emitExecutionReportCanceled(childOrder8);

        // broadcast request for execution recommendations to SRE with just one candidate (B2C2)
        bestExecutionRequest = smartRecommendationEngine.awaitBestExecutionBroadcastRequests(smartOrder.getOrderId());
        assertThat(bestExecutionRequest.getSide()).isEqualTo(smartOrder.getSide());
        assertThat(bestExecutionRequest.getQuantity()).isEqualTo(smartOrder.getQuantity());
        assertThat(bestExecutionRequest.getInstrumentsList()).extracting(InstrumentKey::getInstrumentId).containsExactly(BTCUSD_B2C2_INSTRUMENT_ID);
    }

    @Test
    void shouldNotSendNewBroadcastRequestBeforeCandidatesSuspensionTimeoutAndCancel() {
        OemsRequest smartOrder = orderGateway.emitsOemsRequest(testingData.clientSmartOrderRequestOtc(OEMS_ORDER_TYPE));

        // broadcast request for execution recommendations to SRE
        BestExecutionRequest bestExecutionRequest = smartRecommendationEngine.awaitBestExecutionBroadcastRequests(smartOrder.getOrderId());
        assertThat(bestExecutionRequest.getSide()).isEqualTo(smartOrder.getSide());
        assertThat(bestExecutionRequest.getQuantity()).isEqualTo(smartOrder.getQuantity());
        assertThat(bestExecutionRequest.getInstrumentsList()).extracting(InstrumentKey::getInstrumentId).containsExactlyInAnyOrder(
            BTCUSD_B2C2_INSTRUMENT_ID, BTCUSD_CF_INSTRUMENT_ID);

        // SRE responds with immediate recommendations
        smartRecommendationEngine.emitExecutionRecommendationsOtcDone(bestExecutionRequest.getRecommendationSubscriptionId());

        // SOR sends out new child order request
        OemsRequest childOrder = collider.awaitNewChildOrderRequest(smartOrder.getOrderId(), CF);
        assertThat(childOrder).satisfies(request -> {
            assertThat(request.getInstrumentId()).isEqualTo(BTCUSD_CF_INSTRUMENT_ID);
            assertThat(request.getMetadata().getTarget()).isEqualTo(CF);
            assertThat(request.getQuantity()).isEqualTo(smartOrder.getQuantity());
            assertThat(request.getOrderType()).isEqualTo(OEMS_CHILD_ORDER_TYPE);
            assertThat(request.getSide()).isEqualTo(smartOrder.getSide());
            assertThat(request.getParentOrderId()).isEqualTo(smartOrder.getOrderId());
        });

        // child reported as new
        collider.emitExecutionReportNew(childOrder);

        // child is canceled
        collider.emitExecutionReportCanceled(childOrder);

        // SOR sends out second child order request
        OemsRequest childOrder1 = collider.awaitNewChildOrderRequest(smartOrder.getOrderId(), CF);
        assertThat(childOrder1).satisfies(request -> {
            assertThat(request.getInstrumentId()).isEqualTo(BTCUSD_CF_INSTRUMENT_ID);
            assertThat(request.getMetadata().getTarget()).isEqualTo(CF);
            assertThat(request.getQuantity()).isEqualTo(smartOrder.getQuantity());
            assertThat(request.getOrderType()).isEqualTo(OEMS_CHILD_ORDER_TYPE);
            assertThat(request.getSide()).isEqualTo(smartOrder.getSide());
            assertThat(request.getParentOrderId()).isEqualTo(smartOrder.getOrderId());
        });

        // child reported as new
        collider.emitExecutionReportNew(childOrder1);

        // child is canceled
        collider.emitExecutionReportCanceled(childOrder1);

        // SOR sends out third child order request
        OemsRequest childOrder2 = collider.awaitNewChildOrderRequest(smartOrder.getOrderId(), CF);
        assertThat(childOrder2).satisfies(request -> {
            assertThat(request.getInstrumentId()).isEqualTo(BTCUSD_CF_INSTRUMENT_ID);
            assertThat(request.getMetadata().getTarget()).isEqualTo(CF);
            assertThat(request.getQuantity()).isEqualTo(smartOrder.getQuantity());
            assertThat(request.getOrderType()).isEqualTo(OEMS_CHILD_ORDER_TYPE);
            assertThat(request.getSide()).isEqualTo(smartOrder.getSide());
            assertThat(request.getParentOrderId()).isEqualTo(smartOrder.getOrderId());
        });

        // child reported as new
        collider.emitExecutionReportNew(childOrder2);

        // third child is canceled, candidate is suspended for 8sec
        collider.emitExecutionReportCanceled(childOrder2);

        // SOR sends out child order request for second candidate
        OemsRequest childOrder3 = collider.awaitNewChildOrderRequest(smartOrder.getOrderId(), B2C2);
        assertThat(childOrder3).satisfies(request -> {
            assertThat(request.getInstrumentId()).isEqualTo(BTCUSD_B2C2_INSTRUMENT_ID);
            assertThat(request.getMetadata().getTarget()).isEqualTo(B2C2);
            assertThat(request.getQuantity()).isEqualTo(smartOrder.getQuantity());
            assertThat(request.getOrderType()).isEqualTo(OEMS_CHILD_ORDER_TYPE);
            assertThat(request.getSide()).isEqualTo(smartOrder.getSide());
            assertThat(request.getParentOrderId()).isEqualTo(smartOrder.getOrderId());
        });

        // child reported as new
        collider.emitExecutionReportNew(childOrder3);

        // child is canceled
        collider.emitExecutionReportCanceled(childOrder3);

        // SOR sends out second child order request
        OemsRequest childOrder4 = collider.awaitNewChildOrderRequest(smartOrder.getOrderId(), B2C2);
        assertThat(childOrder4).satisfies(request -> {
            assertThat(request.getInstrumentId()).isEqualTo(BTCUSD_B2C2_INSTRUMENT_ID);
            assertThat(request.getMetadata().getTarget()).isEqualTo(B2C2);
            assertThat(request.getQuantity()).isEqualTo(smartOrder.getQuantity());
            assertThat(request.getOrderType()).isEqualTo(OEMS_CHILD_ORDER_TYPE);
            assertThat(request.getSide()).isEqualTo(smartOrder.getSide());
            assertThat(request.getParentOrderId()).isEqualTo(smartOrder.getOrderId());
        });

        // child reported as new
        collider.emitExecutionReportNew(childOrder4);

        // child is canceled
        collider.emitExecutionReportCanceled(childOrder4);

        // SOR sends out third child order request
        OemsRequest childOrder5 = collider.awaitNewChildOrderRequest(smartOrder.getOrderId(), B2C2);
        assertThat(childOrder5).satisfies(request -> {
            assertThat(request.getInstrumentId()).isEqualTo(BTCUSD_B2C2_INSTRUMENT_ID);
            assertThat(request.getMetadata().getTarget()).isEqualTo(B2C2);
            assertThat(request.getQuantity()).isEqualTo(smartOrder.getQuantity());
            assertThat(request.getOrderType()).isEqualTo(OEMS_CHILD_ORDER_TYPE);
            assertThat(request.getSide()).isEqualTo(smartOrder.getSide());
            assertThat(request.getParentOrderId()).isEqualTo(smartOrder.getOrderId());
        });

        // child reported as new
        collider.emitExecutionReportNew(childOrder5);

        // third child is canceled, candidate is suspended for 8sec
        collider.emitExecutionReportCanceled(childOrder5);

        // both candidates are suspended
        smartRecommendationEngine.ensureNoMoreBestExecutionRequests(smartOrder.getOrderId());
    }

    @Test
    void shouldSendChildOrderForSecondCandidateWhenFirstCandidateDoesNotHaveMinExecutableQuantity() {
        OemsRequest smartOrder = orderGateway.emitsOemsRequest(testingData.clientSmartOrderRequestOtc(OEMS_ORDER_TYPE));

        // broadcast request for execution recommendations to SRE
        BestExecutionRequest bestExecutionRequest = smartRecommendationEngine.awaitBestExecutionBroadcastRequests(smartOrder.getOrderId());
        assertThat(bestExecutionRequest.getSide()).isEqualTo(smartOrder.getSide());
        assertThat(bestExecutionRequest.getQuantity()).isEqualTo(smartOrder.getQuantity());
        assertThat(bestExecutionRequest.getInstrumentsList()).extracting(InstrumentKey::getInstrumentId).containsExactlyInAnyOrder(
                BTCUSD_B2C2_INSTRUMENT_ID, BTCUSD_CF_INSTRUMENT_ID);

        // SRE responds with immediate recommendations
        smartRecommendationEngine.emitExecutionRecommendationsDoneWithOneSmallerQtyOtc(bestExecutionRequest.getRecommendationSubscriptionId());

        // SOR sends out new child order request
        OemsRequest childOrder = collider.awaitNewChildOrderRequest(smartOrder.getOrderId(), B2C2);
        assertThat(childOrder).satisfies(request -> {
            assertThat(request.getInstrumentId()).isEqualTo(BTCUSD_B2C2_INSTRUMENT_ID);
            assertThat(request.getMetadata().getTarget()).isEqualTo(B2C2);
            assertThat(request.getQuantity()).isEqualTo(smartOrder.getQuantity());
            assertThat(request.getOrderType()).isEqualTo(OEMS_CHILD_ORDER_TYPE);
            assertThat(request.getSide()).isEqualTo(smartOrder.getSide());
            assertThat(request.getParentOrderId()).isEqualTo(smartOrder.getOrderId());
        });

        // child reported as new
        collider.emitExecutionReportNew(childOrder);

        // child is filled
        collider.emitExecutionReportFilled(childOrder);

        // ER FILLED for parent
        OemsResponse parentExecFilled = orderGateway.awaitExecutionReportWithOrderStatusReceived(smartOrder.getOrderId(), OemsOrderStatus.STATUS_FILLED);
        assertThat(parentExecFilled).satisfies(response -> {
            assertExecReportFor(response, smartOrder);
            assertThat(response.getExecType()).isEqualTo(OemsExecType.FILL);
            assertThat(response.getOrderStatus()).isEqualTo(OemsOrderStatus.STATUS_FILLED);
            assertThat(response.getCumQty()).isEqualTo(smartOrder.getQuantity());
            assertThat(response.getLeavesQty()).isEqualTo("0");
            assertThat(new BigDecimal(response.getLastPrice())).isEqualByComparingTo(testingData.executionPrice);
            assertThat(new BigDecimal(response.getAvgPrice())).isEqualByComparingTo(testingData.executionPrice);
        });
    }

    @Test
    void shouldRetryWhenCandidatesDoNotHaveMinExecutableQuantity() {
        OemsRequest smartOrder = orderGateway.emitsOemsRequest(testingData.clientSmartOrderRequestOtc(OEMS_ORDER_TYPE));

        // broadcast request for execution recommendations to SRE
        BestExecutionRequest bestExecutionRequest = smartRecommendationEngine.awaitBestExecutionBroadcastRequests(smartOrder.getOrderId());
        assertThat(bestExecutionRequest.getSide()).isEqualTo(smartOrder.getSide());
        assertThat(bestExecutionRequest.getQuantity()).isEqualTo(smartOrder.getQuantity());
        assertThat(bestExecutionRequest.getInstrumentsList()).extracting(InstrumentKey::getInstrumentId).containsExactlyInAnyOrder(
                BTCUSD_B2C2_INSTRUMENT_ID, BTCUSD_CF_INSTRUMENT_ID);

        // SRE responds with immediate recommendations
        smartRecommendationEngine.emitExecutionRecommendationsDoneWithSmallerQtyOtc(bestExecutionRequest.getRecommendationSubscriptionId());

        // no child orders
        collider.ensureNoChildOrderRequests(smartOrder.getOrderId());

        // new broadcast request for execution recommendations to SRE
        BestExecutionRequest bestExecutionRequest1 = smartRecommendationEngine.awaitBestExecutionBroadcastRequests(smartOrder.getOrderId());
        assertThat(bestExecutionRequest1.getSide()).isEqualTo(smartOrder.getSide());
        assertThat(bestExecutionRequest1.getQuantity()).isEqualTo(smartOrder.getQuantity());
        assertThat(bestExecutionRequest1.getInstrumentsList()).extracting(InstrumentKey::getInstrumentId).containsExactlyInAnyOrder(
                BTCUSD_B2C2_INSTRUMENT_ID, BTCUSD_CF_INSTRUMENT_ID);
    }

    @Test
    void shouldCancelChildOnCancellationCheckTimeout() {
        // set submission grace period to 1sec to be able to detect that quickly enough
        ReflectionTestUtils.setField(smartOrderRoutingService, "childOrderSubmissionGracePeriod", 1000);

        OemsRequest smartOrder = orderGateway.emitsOemsRequest(testingData.clientSmartOrderRequestOtc(OEMS_ORDER_TYPE));

        // broadcast request for execution recommendations to SRE
        BestExecutionRequest bestExecutionRequest = smartRecommendationEngine.awaitBestExecutionBroadcastRequests(smartOrder.getOrderId());
        assertThat(bestExecutionRequest.getSide()).isEqualTo(smartOrder.getSide());
        assertThat(bestExecutionRequest.getQuantity()).isEqualTo(smartOrder.getQuantity());
        assertThat(bestExecutionRequest.getInstrumentsList()).extracting(InstrumentKey::getInstrumentId).containsExactlyInAnyOrder(
                BTCUSD_B2C2_INSTRUMENT_ID, BTCUSD_CF_INSTRUMENT_ID);
        // SRE responds with immediate recommendations
        smartRecommendationEngine.emitExecutionRecommendationsOtcDone(bestExecutionRequest.getRecommendationSubscriptionId());

        // SOR sends out new child order request
        OemsRequest childOrder = collider.awaitNewChildOrderRequest(smartOrder.getOrderId(), CF);
        assertThat(childOrder).satisfies(request -> {
            assertThat(request.getInstrumentId()).isEqualTo(BTCUSD_CF_INSTRUMENT_ID);
            assertThat(request.getMetadata().getTarget()).isEqualTo(CF);
            assertThat(request.getQuantity()).isEqualTo(smartOrder.getQuantity());
            assertThat(request.getOrderType()).isEqualTo(OEMS_CHILD_ORDER_TYPE);
            assertThat(request.getSide()).isEqualTo(smartOrder.getSide());
            assertThat(request.getParentOrderId()).isEqualTo(smartOrder.getOrderId());
        });

        // childOrderSubmissionGracePeriod times out
        // child is not reported as NEW -> child is canceled
        OemsRequest childCancel = collider.awaitChildCancelOrderRequest(smartOrder.getOrderId(), CF);
        assertThat(childCancel).satisfies(request -> {
            assertThat(request.getInstrumentId()).isEqualTo(BTCUSD_CF_INSTRUMENT_ID);
            assertThat(request.getMetadata().getTarget()).isEqualTo(CF);
            assertThat(request.getQuantity()).isEqualTo(smartOrder.getQuantity());
            assertThat(request.getOrderType()).isEqualTo(OEMS_CHILD_ORDER_TYPE);
            assertThat(request.getSide()).isEqualTo(smartOrder.getSide());
            assertThat(request.getParentOrderId()).isEqualTo(smartOrder.getOrderId());
        });

        // childOrderCancellationGracePeriod times out
        // child is canceled again, take 2
        OemsRequest childCancel2 = collider.awaitChildCancelOrderRequest(smartOrder.getOrderId(), CF);
        assertThat(childCancel2).satisfies(request -> {
            assertThat(request.getInstrumentId()).isEqualTo(BTCUSD_CF_INSTRUMENT_ID);
            assertThat(request.getMetadata().getTarget()).isEqualTo(CF);
            assertThat(request.getQuantity()).isEqualTo(smartOrder.getQuantity());
            assertThat(request.getOrderType()).isEqualTo(OEMS_CHILD_ORDER_TYPE);
            assertThat(request.getSide()).isEqualTo(smartOrder.getSide());
            assertThat(request.getParentOrderId()).isEqualTo(smartOrder.getOrderId());
        });

        // childOrderCancellationGracePeriod times out
        // child is canceled again, take 3
        OemsRequest childCancel3 = collider.awaitChildCancelOrderRequest(smartOrder.getOrderId(), CF);
        assertThat(childCancel3).satisfies(request -> {
            assertThat(request.getInstrumentId()).isEqualTo(BTCUSD_CF_INSTRUMENT_ID);
            assertThat(request.getMetadata().getTarget()).isEqualTo(CF);
            assertThat(request.getQuantity()).isEqualTo(smartOrder.getQuantity());
            assertThat(request.getOrderType()).isEqualTo(OEMS_CHILD_ORDER_TYPE);
            assertThat(request.getSide()).isEqualTo(smartOrder.getSide());
            assertThat(request.getParentOrderId()).isEqualTo(smartOrder.getOrderId());
        });

        // no more child cancel retries, no more child orders, quantity is in unknown state
        collider.ensureNoChildOrderRequests(smartOrder.getOrderId());
    }

    @Test
    void shouldNotSendNewChildOrderOnCandidatesSuspensionTimeoutWhenParentIsFilled() {
        // set candidate suspension to < 1sec to be able to detect that quickly enough
        ReflectionTestUtils.setField(smartOrderRoutingService, "smartOrderCandidateSuspensionPeriod", 10);

        OemsRequest smartOrder = orderGateway.emitsOemsRequest(testingData.clientSmartOrderRequestOtc(OEMS_ORDER_TYPE));

        // broadcast request for execution recommendations to SRE
        BestExecutionRequest bestExecutionRequest = smartRecommendationEngine.awaitBestExecutionBroadcastRequests(smartOrder.getOrderId());
        assertThat(bestExecutionRequest.getSide()).isEqualTo(smartOrder.getSide());
        assertThat(bestExecutionRequest.getQuantity()).isEqualTo(smartOrder.getQuantity());
        assertThat(bestExecutionRequest.getInstrumentsList()).extracting(InstrumentKey::getInstrumentId).containsExactlyInAnyOrder(
                BTCUSD_B2C2_INSTRUMENT_ID, BTCUSD_CF_INSTRUMENT_ID);

        // SRE responds with immediate recommendations
        smartRecommendationEngine.emitExecutionRecommendationsOtcDone(bestExecutionRequest.getRecommendationSubscriptionId());

        // SOR sends out new child order request
        OemsRequest childOrder = collider.awaitNewChildOrderRequest(smartOrder.getOrderId(), CF);
        assertThat(childOrder).satisfies(request -> {
            assertThat(request.getInstrumentId()).isEqualTo(BTCUSD_CF_INSTRUMENT_ID);
            assertThat(request.getMetadata().getTarget()).isEqualTo(CF);
            assertThat(request.getQuantity()).isEqualTo(smartOrder.getQuantity());
            assertThat(request.getOrderType()).isEqualTo(OEMS_CHILD_ORDER_TYPE);
            assertThat(request.getSide()).isEqualTo(smartOrder.getSide());
            assertThat(request.getParentOrderId()).isEqualTo(smartOrder.getOrderId());
        });

        // child reported as new
        collider.emitExecutionReportNew(childOrder);

        // child is canceled, candidate is suspended for < 1sec
        collider.emitExecutionReportCanceled(childOrder);

        // SOR sends out second child order request
        OemsRequest childOrder1 = collider.awaitNewChildOrderRequest(smartOrder.getOrderId(), CF);
        assertThat(childOrder1).satisfies(request -> {
            assertThat(request.getInstrumentId()).isEqualTo(BTCUSD_CF_INSTRUMENT_ID);
            assertThat(request.getMetadata().getTarget()).isEqualTo(CF);
            assertThat(request.getQuantity()).isEqualTo(smartOrder.getQuantity());
            assertThat(request.getOrderType()).isEqualTo(OEMS_CHILD_ORDER_TYPE);
            assertThat(request.getSide()).isEqualTo(smartOrder.getSide());
            assertThat(request.getParentOrderId()).isEqualTo(smartOrder.getOrderId());
        });

        // second child is filled
        collider.emitExecutionReportFilled(childOrder1);

        // ER FILLED for parent
        OemsResponse parentExecFilled = orderGateway.awaitExecutionReportWithOrderStatusReceived(smartOrder.getOrderId(), OemsOrderStatus.STATUS_FILLED);
        assertThat(parentExecFilled).satisfies(response -> {
            assertExecReportFor(response, smartOrder);
            assertThat(response.getExecType()).isEqualTo(OemsExecType.FILL);
            assertThat(response.getOrderStatus()).isEqualTo(OemsOrderStatus.STATUS_FILLED);
            assertThat(response.getCumQty()).isEqualTo(smartOrder.getQuantity());
            assertThat(response.getLeavesQty()).isEqualTo("0");
            assertThat(new BigDecimal(response.getLastPrice())).isEqualByComparingTo(testingData.executionPrice);
            assertThat(new BigDecimal(response.getAvgPrice())).isEqualByComparingTo(testingData.executionPrice);
        });

        // retry is not executed
        collider.ensureNoChildOrderRequests(smartOrder.getParentOrderId());
    }

    @Test
    void shouldCancelChildOrdersOnParentFilled() {
        OemsRequest smartOrder = orderGateway.emitsOemsRequest(testingData.clientSmartOrderRequestOtc(OEMS_ORDER_TYPE));

        BestExecutionRequest bestExecutionRequest = smartRecommendationEngine.awaitBestExecutionBroadcastRequests(smartOrder.getOrderId());
        assertThat(bestExecutionRequest.getSide()).isEqualTo(smartOrder.getSide());
        assertThat(bestExecutionRequest.getQuantity()).isEqualTo(smartOrder.getQuantity());
        assertThat(bestExecutionRequest.getInstrumentsList()).extracting(InstrumentKey::getInstrumentId).containsExactlyInAnyOrder(
                BTCUSD_B2C2_INSTRUMENT_ID, BTCUSD_CF_INSTRUMENT_ID);

        // SRE responds with immediate recommendations
        smartRecommendationEngine.emitExecutionRecommendationsOtcDone(bestExecutionRequest.getRecommendationSubscriptionId());

        // SOR sends out new child order request
        OemsRequest childOrder = collider.awaitNewChildOrderRequest(smartOrder.getOrderId(), CF);
        assertThat(childOrder).satisfies(request -> {
            assertThat(request.getInstrumentId()).isEqualTo(BTCUSD_CF_INSTRUMENT_ID);
            assertThat(request.getMetadata().getTarget()).isEqualTo(CF);
            assertThat(request.getQuantity()).isEqualTo(smartOrder.getQuantity());
            assertThat(request.getOrderType()).isEqualTo(OEMS_CHILD_ORDER_TYPE);
            assertThat(request.getSide()).isEqualTo(smartOrder.getSide());
            assertThat(request.getParentOrderId()).isEqualTo(smartOrder.getOrderId());
        });

        // child reported as new
        collider.emitExecutionReportNew(childOrder);

        // child is partially filled
        collider.emitExecutionReportPartiallyFilled(childOrder);

        // ER PARTIALLY_FILLED for parent
        OemsResponse parentExecPartiallyFilled = orderGateway.awaitExecutionReportWithOrderStatusReceived(smartOrder.getOrderId(), OemsOrderStatus.STATUS_PARTIALLY_FILLED);
        assertThat(parentExecPartiallyFilled).satisfies(response -> {
            assertExecReportFor(response, smartOrder);
            assertThat(response.getExecType()).isEqualTo(OemsExecType.PARTIAL_FILL);
            assertThat(response.getOrderStatus()).isEqualTo(OemsOrderStatus.STATUS_PARTIALLY_FILLED);
            assertThat(response.getCumQty()).isEqualTo(testingData.halfQuantity);
            assertThat(response.getLeavesQty()).isEqualTo(testingData.halfQuantity);
            assertThat(new BigDecimal(response.getLastPrice())).isEqualByComparingTo(testingData.executionPrice);
            assertThat(new BigDecimal(response.getAvgPrice())).isEqualByComparingTo(testingData.executionPrice);
        });

        // parent is filled (i.e. on cancel replace with smaller qty)
        collider.emitExecutionReportFilledForParent(smartOrder);

        // child is canceled
        OemsRequest childCancel = collider.awaitChildCancelOrderRequest(smartOrder.getOrderId(), CF);
        assertThat(childCancel).satisfies(request -> {
            assertThat(request.getInstrumentId()).isEqualTo(BTCUSD_CF_INSTRUMENT_ID);
            assertThat(request.getMetadata().getTarget()).isEqualTo(CF);
            assertThat(request.getOrderType()).isEqualTo(OEMS_CHILD_ORDER_TYPE);
            assertThat(request.getSide()).isEqualTo(smartOrder.getSide());
            assertThat(request.getParentOrderId()).isEqualTo(smartOrder.getOrderId());
        });
    }

    @Test
    void shouldExcludeNotUsedCandidatesOnReplacingSmartOrder() {
        OemsRequest smartOrder = orderGateway.emitsOemsRequest(testingData.clientSmartOrderRequestOtc(OEMS_ORDER_TYPE));

        BestExecutionRequest bestExecutionRequest = smartRecommendationEngine.awaitBestExecutionBroadcastRequests(smartOrder.getOrderId());
        assertThat(bestExecutionRequest.getSide()).isEqualTo(smartOrder.getSide());
        assertThat(bestExecutionRequest.getQuantity()).isEqualTo(smartOrder.getQuantity());
        assertThat(bestExecutionRequest.getInstrumentsList()).extracting(InstrumentKey::getInstrumentId).containsExactlyInAnyOrder(
                BTCUSD_B2C2_INSTRUMENT_ID, BTCUSD_CF_INSTRUMENT_ID);

        // SRE responds with immediate recommendations
        smartRecommendationEngine.emitExecutionRecommendationsOtcDone(bestExecutionRequest.getRecommendationSubscriptionId());

        // SOR sends out new child order request
        OemsRequest childOrder = collider.awaitNewChildOrderRequest(smartOrder.getOrderId(), CF);
        assertThat(childOrder).satisfies(request -> {
            assertThat(request.getInstrumentId()).isEqualTo(BTCUSD_CF_INSTRUMENT_ID);
            assertThat(request.getMetadata().getTarget()).isEqualTo(CF);
            assertThat(request.getQuantity()).isEqualTo(smartOrder.getQuantity());
            assertThat(request.getOrderType()).isEqualTo(OEMS_CHILD_ORDER_TYPE);
            assertThat(request.getSide()).isEqualTo(smartOrder.getSide());
            assertThat(request.getParentOrderId()).isEqualTo(smartOrder.getOrderId());
        });

        // child reported as new
        collider.emitExecutionReportNew(childOrder);

        // child is partially filled
        collider.emitExecutionReportPartiallyFilled(childOrder);

        // ER PARTIALLY_FILLED for parent
        OemsResponse parentExecPartiallyFilled = orderGateway.awaitExecutionReportWithOrderStatusReceived(smartOrder.getOrderId(), OemsOrderStatus.STATUS_PARTIALLY_FILLED);
        assertThat(parentExecPartiallyFilled).satisfies(response -> {
            assertExecReportFor(response, smartOrder);
            assertThat(response.getExecType()).isEqualTo(OemsExecType.PARTIAL_FILL);
            assertThat(response.getOrderStatus()).isEqualTo(OemsOrderStatus.STATUS_PARTIALLY_FILLED);
            assertThat(response.getCumQty()).isEqualTo(testingData.halfQuantity);
            assertThat(response.getLeavesQty()).isEqualTo(testingData.halfQuantity);
            assertThat(new BigDecimal(response.getLastPrice())).isEqualByComparingTo(testingData.executionPrice);
            assertThat(new BigDecimal(response.getAvgPrice())).isEqualByComparingTo(testingData.executionPrice);
        });

        // request to cancel replace parent order comes in to order gateway, it sends out cancel and then new order
        // request to cancel parent order
        orderGateway.emitsOemsRequest(testingData.clientCancelOrderRequestOtc(OEMS_ORDER_TYPE));

        // child is canceled
        OemsRequest childCancel = collider.awaitChildCancelOrderRequest(smartOrder.getOrderId(), CF);
        assertThat(childCancel).satisfies(request -> {
            assertThat(request.getInstrumentId()).isEqualTo(BTCUSD_CF_INSTRUMENT_ID);
            assertThat(request.getMetadata().getTarget()).isEqualTo(CF);
            assertThat(request.getOrderType()).isEqualTo(OEMS_CHILD_ORDER_TYPE);
            assertThat(request.getSide()).isEqualTo(smartOrder.getSide());
            assertThat(request.getParentOrderId()).isEqualTo(smartOrder.getOrderId());
        });

        // new (replacing) parent order
        OemsRequest smartReplacingOrder = orderGateway.emitsOemsRequest(
                testingData.clientReplacingSmartOrderRequestOtc(OEMS_ORDER_TYPE, smartOrder.getOrderId()));

        // B2C2 excluded to prevent execution on multiple venues, only CF available
        BestExecutionRequest bestExecutionRequest1 = smartRecommendationEngine.awaitBestExecutionBroadcastRequests(smartReplacingOrder.getOrderId());
        assertThat(bestExecutionRequest1.getSide()).isEqualTo(smartReplacingOrder.getSide());
        assertThat(bestExecutionRequest1.getQuantity()).isEqualTo(smartReplacingOrder.getQuantity());
        assertThat(bestExecutionRequest1.getInstrumentsList()).extracting(InstrumentKey::getInstrumentId).containsExactlyInAnyOrder(
                BTCUSD_CF_INSTRUMENT_ID);
    }

    @Test
    void shouldNotAllowRoutingCandidatesModificationOnReplacingSmartOrderInPartialFill() {
        OemsRequest smartOrder = orderGateway.emitsOemsRequest(testingData.clientSmartOrderRequestOtc(OEMS_ORDER_TYPE));

        BestExecutionRequest bestExecutionRequest = smartRecommendationEngine.awaitBestExecutionBroadcastRequests(smartOrder.getOrderId());
        assertThat(bestExecutionRequest.getSide()).isEqualTo(smartOrder.getSide());
        assertThat(bestExecutionRequest.getQuantity()).isEqualTo(smartOrder.getQuantity());
        assertThat(bestExecutionRequest.getInstrumentsList()).extracting(InstrumentKey::getInstrumentId).containsExactlyInAnyOrder(
                BTCUSD_B2C2_INSTRUMENT_ID, BTCUSD_CF_INSTRUMENT_ID);

        // SRE responds with immediate recommendations
        smartRecommendationEngine.emitExecutionRecommendationsOtcDone(bestExecutionRequest.getRecommendationSubscriptionId());

        // SOR sends out new child order request
        OemsRequest childOrder = collider.awaitNewChildOrderRequest(smartOrder.getOrderId(), CF);
        assertThat(childOrder).satisfies(request -> {
            assertThat(request.getInstrumentId()).isEqualTo(BTCUSD_CF_INSTRUMENT_ID);
            assertThat(request.getMetadata().getTarget()).isEqualTo(CF);
            assertThat(request.getQuantity()).isEqualTo(smartOrder.getQuantity());
            assertThat(request.getOrderType()).isEqualTo(OEMS_CHILD_ORDER_TYPE);
            assertThat(request.getSide()).isEqualTo(smartOrder.getSide());
            assertThat(request.getParentOrderId()).isEqualTo(smartOrder.getOrderId());
        });

        // child reported as new
        collider.emitExecutionReportNew(childOrder);

        // child is partially filled
        collider.emitExecutionReportPartiallyFilled(childOrder);

        // ER PARTIALLY_FILLED for parent
        OemsResponse parentExecPartiallyFilled = orderGateway.awaitExecutionReportWithOrderStatusReceived(smartOrder.getOrderId(), OemsOrderStatus.STATUS_PARTIALLY_FILLED);
        assertThat(parentExecPartiallyFilled).satisfies(response -> {
            assertExecReportFor(response, smartOrder);
            assertThat(response.getExecType()).isEqualTo(OemsExecType.PARTIAL_FILL);
            assertThat(response.getOrderStatus()).isEqualTo(OemsOrderStatus.STATUS_PARTIALLY_FILLED);
            assertThat(response.getCumQty()).isEqualTo(testingData.halfQuantity);
            assertThat(response.getLeavesQty()).isEqualTo(testingData.halfQuantity);
            assertThat(new BigDecimal(response.getLastPrice())).isEqualByComparingTo(testingData.executionPrice);
            assertThat(new BigDecimal(response.getAvgPrice())).isEqualByComparingTo(testingData.executionPrice);
        });

        // request to cancel replace parent order, order gateway sends out cancel, and then new order
        // request to cancel parent order
        orderGateway.emitsOemsRequest(testingData.clientCancelOrderRequestOtc(OEMS_ORDER_TYPE));

        // child is canceled
        OemsRequest childCancel = collider.awaitChildCancelOrderRequest(smartOrder.getOrderId(), CF);
        assertThat(childCancel).satisfies(request -> {
            assertThat(request.getInstrumentId()).isEqualTo(BTCUSD_CF_INSTRUMENT_ID);
            assertThat(request.getMetadata().getTarget()).isEqualTo(CF);
            assertThat(request.getOrderType()).isEqualTo(OEMS_CHILD_ORDER_TYPE);
            assertThat(request.getSide()).isEqualTo(smartOrder.getSide());
            assertThat(request.getParentOrderId()).isEqualTo(smartOrder.getOrderId());
        });

        // replacing order with routing candidates modified
        OemsRequest smartReplacingOrder = orderGateway.emitsOemsRequest(
                testingData.clientReplacingSmartOrderRequestForOneVenueOtc(OEMS_ORDER_TYPE, smartOrder.getOrderId()));

        // smart order rejected with validation error
        OemsResponse parentRejected = orderGateway.awaitExecutionReportWithOrderStatusReceived(smartReplacingOrder.getOrderId(), OemsOrderStatus.STATUS_REJECTED);
        assertThat(parentRejected).satisfies(response -> {
            assertExecReportFor(response, smartReplacingOrder);
            assertThat(response.getExecType()).isEqualTo(OemsExecType.REJECTED);
            assertThat(response.getOrderStatus()).isEqualTo(OemsOrderStatus.STATUS_REJECTED);
            assertThat(response.getReason()).isEqualTo("Smart order " + smartOrder.getOrderId() + ": cannot modify routing candidates, because smart order has been partially filled already.");
        });
    }

    @Test
    void shouldTryToCancelParentOnChildCancelReject() {
        // set submission grace period to < 1sec to be able to detect that quickly enough
        ReflectionTestUtils.setField(smartOrderRoutingService, "childOrderSubmissionGracePeriod", 10);

        OemsRequest smartOrder = orderGateway.emitsOemsRequest(testingData.clientSmartOrderRequestOtc(OEMS_ORDER_TYPE));

        // broadcast request for execution recommendations to SRE
        BestExecutionRequest bestExecutionRequest = smartRecommendationEngine.awaitBestExecutionBroadcastRequests(smartOrder.getOrderId());
        assertThat(bestExecutionRequest.getSide()).isEqualTo(smartOrder.getSide());
        assertThat(bestExecutionRequest.getQuantity()).isEqualTo(smartOrder.getQuantity());
        assertThat(bestExecutionRequest.getInstrumentsList()).extracting(InstrumentKey::getInstrumentId).containsExactlyInAnyOrder(
            BTCUSD_B2C2_INSTRUMENT_ID, BTCUSD_CF_INSTRUMENT_ID);

        // SRE responds with immediate recommendations
        smartRecommendationEngine.emitExecutionRecommendationsOtcDone(bestExecutionRequest.getRecommendationSubscriptionId());

        // SOR sends out new child order request
        OemsRequest childOrder = collider.awaitNewChildOrderRequest(smartOrder.getOrderId(), CF);
        assertThat(childOrder).satisfies(request -> {
            assertThat(request.getInstrumentId()).isEqualTo(BTCUSD_CF_INSTRUMENT_ID);
            assertThat(request.getMetadata().getTarget()).isEqualTo(CF);
            assertThat(request.getQuantity()).isEqualTo(smartOrder.getQuantity());
            assertThat(request.getOrderType()).isEqualTo(OEMS_CHILD_ORDER_TYPE);
            assertThat(request.getSide()).isEqualTo(smartOrder.getSide());
            assertThat(request.getParentOrderId()).isEqualTo(smartOrder.getOrderId());
        });

        // childOrderSubmissionGracePeriod times out
        // child is not reported as NEW -> child is canceled
        OemsRequest childCancel = collider.awaitChildCancelOrderRequest(smartOrder.getOrderId(), CF);
        assertThat(childCancel).satisfies(request -> {
            assertThat(request.getInstrumentId()).isEqualTo(BTCUSD_CF_INSTRUMENT_ID);
            assertThat(request.getMetadata().getTarget()).isEqualTo(CF);
            assertThat(request.getQuantity()).isEqualTo(smartOrder.getQuantity());
            assertThat(request.getOrderType()).isEqualTo(OEMS_CHILD_ORDER_TYPE);
            assertThat(request.getSide()).isEqualTo(smartOrder.getSide());
            assertThat(request.getParentOrderId()).isEqualTo(smartOrder.getOrderId());
        });

        // child cancel rejected
        collider.emitExecutionReportCancelRejected(childOrder);

        // parent reported as PENDING_CANCEL
        OemsResponse parentExecPendingCancel = orderGateway.awaitExecutionReportWithOrderStatusReceived(smartOrder.getOrderId(), OemsOrderStatus.STATUS_PENDING_CANCEL);
        assertThat(parentExecPendingCancel).satisfies(request -> {
            assertThat(request.getSide()).isEqualTo(smartOrder.getSide());
            assertThat(request.getOrderId()).isEqualTo(smartOrder.getOrderId());
            assertThat(request.getOrderStatus()).isEqualTo(OemsOrderStatus.STATUS_PENDING_CANCEL);
        });

        // no more parent cancel requests
        collider.ensureNoOrderRequests(smartOrder.getOrderId());
    }
}
