package io.wyden.audit.model;

import io.wyden.published.referencedata.PortfolioType;

import org.springframework.lang.Nullable;

import java.util.List;

public record PtcAuditSearchInput(
    @Nullable String from,
    @Nullable String to,
    List<String> portfolioIds,
    PortfolioType portfolioType,
    @Nullable Integer first,
    @Nullable String after) {

    public int pageSize() {
        return first() != null ? first() : Integer.MAX_VALUE;
    }
}
