package io.wyden.audit.web;

import io.wyden.audit.model.AuditEventDto;
import io.wyden.audit.model.AuditEventEntity;
import io.wyden.audit.model.HedgeAuditSearchInput;
import io.wyden.audit.model.MatchAuditSearchInput;
import io.wyden.audit.model.PtcAuditSearchInput;
import io.wyden.audit.service.AuditEventService;
import io.wyden.audit.utils.AuditEventMapper;
import io.wyden.published.audit.AuditEventPayloadList;
import io.wyden.published.audit.MatchPayloadList;
import io.wyden.published.common.CursorConnection;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.Collection;
import java.util.List;
import java.util.NoSuchElementException;
import javax.annotation.Nullable;

@RestController
@RequestMapping("/audit-event")
public class AuditEventController {

    private static final Logger LOGGER = LoggerFactory.getLogger(AuditEventController.class);

    private final AuditEventService auditEventService;

    public AuditEventController(AuditEventService auditEventService) {
        this.auditEventService = auditEventService;
    }

    @GetMapping(produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<List<AuditEventDto>> getAllAuditEvents(@RequestParam("type") String type) {
        List<AuditEventDto> auditEvents = auditEventService.getAuditEventsOfType(type).stream()
            .map(AuditEventMapper::entityToDto)
            .toList();
        return ResponseEntity.ok().body(auditEvents);
    }

    @PostMapping(value = "/ptc", produces = MediaType.APPLICATION_PROTOBUF_VALUE)
    public ResponseEntity<CursorConnection> getPtcAuditEvents(@RequestBody PtcAuditSearchInput ptcAuditSearchInput) {
        CursorConnection auditEventConnection =  auditEventService.getPtcAuditEvents(ptcAuditSearchInput);
        return ResponseEntity.ok().body(auditEventConnection);
    }

    @GetMapping(value = "/match", produces = MediaType.APPLICATION_PROTOBUF_VALUE)
    public ResponseEntity<MatchPayloadList> getMatchAuditEvents(@RequestParam("orderId") @Nullable String orderId,
                                                                @RequestParam("rootOrderId") @Nullable String rootOrderId,
                                                                @RequestParam("matchId") @Nullable String matchId) {
        Collection<AuditEventEntity> auditEvents = auditEventService.getMatchAuditEvents(orderId, rootOrderId, matchId);
        return ResponseEntity.ok().body(AuditEventMapper.entityToMatchList(auditEvents));
    }

    @PostMapping(value = "/match", produces = MediaType.APPLICATION_PROTOBUF_VALUE)
    public ResponseEntity<CursorConnection> getMatchAuditEvents(@RequestBody MatchAuditSearchInput matchAuditSearchInput) {
        CursorConnection auditEventConnection = auditEventService.getMatchAuditEventsPaged(matchAuditSearchInput);
        return ResponseEntity.ok().body(auditEventConnection);
    }

    @GetMapping(value = "/hedge", produces = MediaType.APPLICATION_PROTOBUF_VALUE)
    public ResponseEntity<AuditEventPayloadList> getHedgeAuditEvents(@RequestParam("matchId") @Nullable String matchId,
                                                                     @RequestParam("clobOrderId") @Nullable String clobOrderId,
                                                                     @RequestParam("hedgeOrderId") @Nullable String hedgeOrderId,
                                                                     @RequestParam("rootOrderId") @Nullable String rootOrderId) {
        Collection<AuditEventEntity> auditEvents = auditEventService.getHedgeAuditEvents(matchId, clobOrderId, hedgeOrderId, rootOrderId);
        return ResponseEntity.ok().body(AuditEventMapper.entitiesToAuditEventPayloadList(auditEvents));
    }

    @PostMapping(value = "/hedge", produces = MediaType.APPLICATION_PROTOBUF_VALUE)
    public ResponseEntity<CursorConnection> getHedgeAuditEvents(@RequestBody HedgeAuditSearchInput hedgeAuditSearchInput) {
        CursorConnection auditEventConnection = auditEventService.getHedgeAuditEventsPaged(hedgeAuditSearchInput);
        return ResponseEntity.ok().body(auditEventConnection);
    }

    @ExceptionHandler({NoSuchElementException.class})
    public ResponseEntity<String> noSuchElementExceptionHandler(NoSuchElementException exception) {
        LOGGER.warn("New NoSuchElementException {}", exception.getMessage(), exception.getCause());
        return ResponseEntity.status(HttpStatus.NOT_FOUND).body(exception.getMessage());
    }

    @ExceptionHandler({IllegalArgumentException.class})
    public ResponseEntity<String> illegalArgumentExceptionHandler(IllegalArgumentException exception) {
        LOGGER.warn("New IllegalArgumentException {}", exception.getMessage(), exception.getCause());
        return ResponseEntity.status(HttpStatus.BAD_REQUEST).body(exception.getMessage());
    }

    @ExceptionHandler({RuntimeException.class})
    public ResponseEntity<String> runtimeExceptionHandler(RuntimeException exception) {
        LOGGER.warn("New RuntimeException {}\nReturning 500 status response code.", exception.getMessage(), exception.getCause());
        return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(exception.getMessage());
    }
}
