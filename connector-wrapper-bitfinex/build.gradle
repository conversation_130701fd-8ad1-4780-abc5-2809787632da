
dependencies {
    implementation project(":connector-wrapper-base")
    implementation dependencyCatalog.connector.bitfinex
}

bootRun {
    args = ["--tracing.collector.endpoint=http://localhost:4317"]
    jvmArgs = ["-agentlib:jdwp=transport=dt_socket,server=y,suspend=n,address=*:9080"]
    environment([
            "FLUENTD_HOST": "localhost",
            "SPRING_PROFILES_ACTIVE": "dev",
            "SPRING_CLOUD_VAULT_ENABLED": "false",
            "ACCOUNT_NAME": "bitfinex"
    ])
}
