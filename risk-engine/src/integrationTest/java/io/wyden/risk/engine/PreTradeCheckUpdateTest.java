package io.wyden.risk.engine;

import io.wyden.published.oems.OemsOrderType;
import io.wyden.published.oems.OemsRequest;
import io.wyden.published.risk.PreTradeCheck;
import io.wyden.published.risk.PreTradeCheckLevel;
import io.wyden.published.risk.PreTradeCheckPropertyValue;
import io.wyden.published.risk.RequestChannel;
import io.wyden.risk.engine.pretradecheck.PreTradeCheckResult;
import io.wyden.risk.engine.pretradecheck.configuration.PreTradeCheckConfigurationService;
import io.wyden.risk.engine.pretradecheck.execution.PreTradeCheckExecutor;
import org.jetbrains.annotations.NotNull;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;
import java.util.UUID;
import java.util.concurrent.TimeUnit;

import static org.assertj.core.api.Assertions.assertThat;
import static org.awaitility.Awaitility.await;

// https://algotrader.atlassian.net/browse/AC-2299
public class PreTradeCheckUpdateTest extends RiskEngineIntegrationTestBase {
    @Autowired
    private PreTradeCheckConfigurationService preTradeCheckConfigurationService;
    @Autowired
    private PreTradeCheckExecutor preTradeCheckExecutor;

    @Test
    void shouldUpdatePtc() {
        // given
        String id = UUID.randomUUID().toString();
        PreTradeCheck ptcAllowingOnlyLimitOrder = orderTypeAllowlist(id, "LIMIT");
        PreTradeCheck ptcAllowingLimitMarketOrder = orderTypeAllowlist(id, "LIMIT", "MARKET");
        OemsRequest marketOrder = OemsRequest.newBuilder()
            .setOrderType(OemsOrderType.MARKET)
            .build();

        // when
        preTradeCheckConfigurationService.savePreTradeCheck(ptcAllowingOnlyLimitOrder);

        // then
        await()
            .atMost(10, TimeUnit.SECONDS)
            .untilAsserted(() -> {
                assertThat(preTradeCheckExecutor.check(marketOrder).getStatus()).isEqualTo(PreTradeCheckResult.Status.REJECTED);
            });

        // when
        preTradeCheckConfigurationService.savePreTradeCheck(ptcAllowingLimitMarketOrder);

        // then
        await()
            .atMost(2, TimeUnit.SECONDS)
            .untilAsserted(() -> {
                assertThat(preTradeCheckExecutor.check(marketOrder).getStatus()).isEqualTo(PreTradeCheckResult.Status.APPROVED);
            });
    }

    @NotNull
    private static PreTradeCheck orderTypeAllowlist(String id, String... allowlist) {
        return PreTradeCheck.newBuilder()
            .setId(id)
            .setType("OrderType")
            .putProperties("allowlist", PreTradeCheckPropertyValue.newBuilder().setStringArray(PreTradeCheckPropertyValue.StringArray.newBuilder().addAllStringValue(List.of(allowlist))).build())
            .setLevel(PreTradeCheckLevel.BLOCK)
            .addAllRequestChannels(List.of(RequestChannel.API, RequestChannel.UI))
            .setEnabled(true)
            .build();
    }
}
