package io.wyden.clob.gateway.service.admin;

import com.google.protobuf.Message;
import io.wyden.clob.gateway.model.ClobOrderState;
import io.wyden.clob.gateway.service.config.ClobConfigCacheFacade;
import io.wyden.clob.gateway.service.config.QuotingConfigCacheFacade;
import io.wyden.clob.gateway.service.fsm.OrderService;
import io.wyden.cloudutils.tools.ProtobufUtils;
import io.wyden.published.brokerdesk.ClobConfig;
import io.wyden.published.brokerdesk.QuotingConfig;
import io.wyden.published.oems.OemsRequest;
import org.apache.commons.lang3.ObjectUtils;
import org.jetbrains.annotations.NotNull;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import java.util.Collection;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

@Service
public class AdminService {

    private static final Logger LOGGER = LoggerFactory.getLogger(AdminService.class);

    private final ClobConfigCacheFacade clobConfigCacheFacade;
    private final QuotingConfigCacheFacade quotingConfigCacheFacade;
    private final CrudOrderCache crudOrderCache;
    private final AeronAdminService aeronAdminService;
    private final OrderService orderService;

    public AdminService(ClobConfigCacheFacade clobConfigCacheFacade,
                        QuotingConfigCacheFacade quotingConfigCacheFacade,
                        CrudOrderCache crudOrderCache,
                        AeronAdminService aeronAdminService,
                        OrderService orderService) {
        this.clobConfigCacheFacade = clobConfigCacheFacade;
        this.quotingConfigCacheFacade = quotingConfigCacheFacade;
        this.crudOrderCache = crudOrderCache;
        this.aeronAdminService = aeronAdminService;
        this.orderService = orderService;
    }

    public String getClobConfig(String account) {
        LOGGER.warn("Calling getClobConfig for account: {}", account);
        ClobConfig clobConfig = clobConfigCacheFacade.find(account);
        return messageToString(clobConfig);
    }

    public void updateClobConfig(String account, String updatedClobConfig) {
        LOGGER.warn("Calling updateClobConfig for account: {}. New CLOB config: {}", account, updatedClobConfig);
        ClobConfig clobConfig = (ClobConfig) ProtobufUtils.stringToMessage(ClobConfig.getDefaultInstance(), updatedClobConfig);
        clobConfigCacheFacade.save(account, clobConfig);
    }

    public String getQuotingConfig(long clobUid) {
        LOGGER.warn("Calling getQuotingConfig for clobUid: {}", clobUid);
        QuotingConfig quotingConfig = quotingConfigCacheFacade.find(clobUid);
        return messageToString(quotingConfig);
    }

    public String find(String orderId, String matchingEngineOrderId) {
        return Optional.ofNullable(orderId)
            .map(this::findByOrderId)
            .or(() -> Optional.ofNullable(matchingEngineOrderId)
                .map(this::findByMatchingEngineOrderId))
            .orElse(null);
    }

    public @NotNull List<String> findAllExpired() {
        LOGGER.warn("Calling findAllExpired ClobOrderStates");
        Collection<ClobOrderState> expired = crudOrderCache.findAllExpired();
        return expired.stream().map(this::messageToString).collect(Collectors.toList());
    }

    public void add(String newOrderState) {
        LOGGER.warn("Calling add ClobOrderState: {}", newOrderState);
        ClobOrderState clobOrderState = (ClobOrderState) ProtobufUtils.stringToMessage(ClobOrderState.getDefaultInstance(), newOrderState);
        crudOrderCache.add(clobOrderState);
    }

    public void update(String updatedState) {
        LOGGER.warn("Calling update ClobOrderState: {}", updatedState);
        ClobOrderState clobOrderState = (ClobOrderState) ProtobufUtils.stringToMessage(ClobOrderState.getDefaultInstance(), updatedState);
        crudOrderCache.update(clobOrderState);
    }

    public void deleteByOrderId(String orderId) {
        LOGGER.warn("Calling delete ClobOrderState by orderId: {}", orderId);
        crudOrderCache.delete(orderId);
    }

    public void delete(String deleteState) {
        LOGGER.warn("Calling delete ClobOrderState: {}", deleteState);
        ClobOrderState clobOrderState = (ClobOrderState) ProtobufUtils.stringToMessage(ClobOrderState.getDefaultInstance(), deleteState);
        crudOrderCache.delete(clobOrderState);
    }

    public String sendAeronCommand(String orderId, String commandJson) {
        LOGGER.warn("Sending Aeron command: {}", commandJson);
        return aeronAdminService.sendAeronCommand(orderId, commandJson).toString();
    }

    public void processEmulatedAeronCommand(String commandJson) {
        LOGGER.warn("Processing emulated Aeron command: {}.", commandJson);
        aeronAdminService.processEmulatedAeronMessage(Long.MIN_VALUE, commandJson);
    }

    public void cancelOrder(String oemsRequestJson) {
        LOGGER.warn("Cancelling order with request: {}", oemsRequestJson);
        OemsRequest oemsRequest = (OemsRequest) ProtobufUtils.stringToMessage(OemsRequest.getDefaultInstance(), oemsRequestJson);
        if (Objects.nonNull(oemsRequestJson) && ObjectUtils.isNotEmpty(oemsRequest.getRequestType()) && oemsRequest.getRequestType().equals(OemsRequest.OemsRequestType.CANCEL)) {
            orderService.onOemsRequest(oemsRequest);
        }
        LOGGER.warn("Order not cancelled. Invalid request: {}", oemsRequestJson);
    }

    private String findByOrderId(String orderId) {
        LOGGER.warn("Calling find ClobOrderState orderId: {}", orderId);
        ClobOrderState clobOrderState = crudOrderCache.find(orderId);
        return messageToString(clobOrderState);
    }

    private String findByMatchingEngineOrderId(String matchingEngineOrderId) {
        LOGGER.warn("Calling find ClobOrderState by matchingEngineOrderId: {}", matchingEngineOrderId);
        ClobOrderState clobOrderState = crudOrderCache.find(matchingEngineOrderId);
        return messageToString(clobOrderState);
    }

    private String messageToString(Message message) {
        return Objects.nonNull(message) ? ProtobufUtils.messageToString(message) : null;
    }
}
