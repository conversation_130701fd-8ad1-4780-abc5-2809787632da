package io.wyden.clob.gateway.it.utils;

import com.google.protobuf.Message;

import java.util.Comparator;

public class ProtobufEqualsUtil {
    public static boolean equalsIgnoringFields(Message msg1, Message msg2, int... fieldNumbersToIgnore) {
        Message.Builder msg1Copy = msg1.toBuilder();
        Message.Builder msg2Copy = msg2.toBuilder();

        // Clear fields to be ignored
        for (int fieldNumber : fieldNumbersToIgnore) {
            msg1Copy.clearField(msg1Copy.getDescriptorForType().findFieldByNumber(fieldNumber));
            msg2Copy.clearField(msg2Copy.getDescriptorForType().findFieldByNumber(fieldNumber));
        }

        // Compare remaining fields
        return msg1Copy.build().equals(msg2Copy.build());
    }

    public static Comparator<Message> compareIgnoringFields(int... fieldNumbersToIgnore) {
        return (m1, m2) -> equalsIgnoringFields(m1, m2, fieldNumbersToIgnore) ? 0 : -1;
    }
}