package io.wyden.clob.gateway.it.utils;

import io.wyden.clob.gateway.it.TestingData;
import io.wyden.published.oems.OemsExecRestatementReason;
import io.wyden.published.oems.OemsExecType;
import io.wyden.published.oems.OemsLiquidityIndicator;
import io.wyden.published.oems.OemsOrderStatus;
import io.wyden.published.oems.OemsRequest;
import io.wyden.published.oems.OemsResponse;

import javax.annotation.Nullable;

import static io.wyden.published.oems.OemsExecType.EXEC_TYPE_UNSPECIFIED;
import static io.wyden.published.oems.OemsExecType.NEW;
import static io.wyden.published.oems.OemsOrderStatus.STATUS_NEW;
import static io.wyden.published.oems.OemsResponse.OemsResponseType.CANCEL_REJECT;
import static io.wyden.published.oems.OemsResponse.OemsResponseType.EXECUTION_REPORT;

public record OemsResponseExpectations(
    OemsRequest request,
    OemsResponse.OemsResponseType responseType,
    OemsExecType execType,
    OemsOrderStatus orderStatus,
    @Nullable OemsLiquidityIndicator liquidityIndicator,
    @Nullable TestingData.VenueAccounts venueAccount,
    String orderQty,
    String cumQty,
    String leavesQty,
    String lastPrice,
    String avgPrice,
    String calculatedCcyLastQty,
    String calculatedCcyReducedQty,
    String reason,
    @Nullable OemsExecRestatementReason restatementReason
) {
    public static OemsResponseExpectations statusNew(OemsRequest request) {
        return new OemsResponseExpectations(request, EXECUTION_REPORT, NEW, STATUS_NEW, null, null, request.getQuantity(), "0", request.getQuantity(), "0", "0", null, null, "", null);
    }

    public static OemsResponseExpectations statusFilled(OemsRequest request, OemsLiquidityIndicator liquidityIndicator, TestingData.VenueAccounts venueAccount, String lastPx, String avgPx, String calculatedCcyLastQty) {
        return statusFilled(request, liquidityIndicator, venueAccount, request.getQuantity(), lastPx, avgPx, calculatedCcyLastQty);
    }

    public static OemsResponseExpectations statusFilled(OemsRequest request, OemsLiquidityIndicator liquidityIndicator, TestingData.VenueAccounts venueAccount, String orderQty, String lastPx, String avgPx, String calculatedCcyLastQty) {
        return new OemsResponseExpectations(request, EXECUTION_REPORT, OemsExecType.FILL, OemsOrderStatus.STATUS_FILLED, liquidityIndicator, venueAccount, orderQty, orderQty, "0", lastPx, avgPx, calculatedCcyLastQty, null, "", null);
    }

    public static OemsResponseExpectations statusPartiallyFilled(OemsRequest request, OemsLiquidityIndicator liquidityIndicator, TestingData.VenueAccounts venueAccount, String cumQty, String lastPx, String avgPx, String leavesQty, String calculatedCcyLastQty) {
        return new OemsResponseExpectations(request, EXECUTION_REPORT, OemsExecType.PARTIAL_FILL, OemsOrderStatus.STATUS_PARTIALLY_FILLED, liquidityIndicator, venueAccount, request.getQuantity(), cumQty, leavesQty, lastPx, avgPx, calculatedCcyLastQty, null, "", null);
    }

    public static OemsResponseExpectations statusPendingCancel(OemsRequest request, String cumQty, String avgPrice, String leavesQty) {
        return new OemsResponseExpectations(request, EXECUTION_REPORT, OemsExecType.PENDING_CANCEL, OemsOrderStatus.STATUS_PENDING_CANCEL, null, null, request.getQuantity(), cumQty, leavesQty, "0", avgPrice, null, null, "", null);
    }

    public static OemsResponseExpectations statusCanceled(OemsRequest request, String cumQty, String avgPrice, String reason, OemsExecRestatementReason restatementReason) {
        return statusCanceled(request, request.getQuantity(), cumQty, avgPrice, reason, restatementReason);
    }

    public static OemsResponseExpectations statusCanceled(OemsRequest request, String orderQty, String cumQty, String avgPrice, String reason, OemsExecRestatementReason restatementReason) {
        return new OemsResponseExpectations(request, EXECUTION_REPORT, OemsExecType.CANCELED, OemsOrderStatus.STATUS_CANCELED, null, null, orderQty, cumQty, "0", "0", avgPrice, null, null, reason, restatementReason);
    }

    public static OemsResponseExpectations statusRejected(OemsRequest request, String reason) {
        return new OemsResponseExpectations(request, EXECUTION_REPORT, OemsExecType.REJECTED, OemsOrderStatus.STATUS_REJECTED, null, null, request.getQuantity(), "0", "0", "0", "0", null, null, reason, null);
    }

    public static OemsResponseExpectations cancelReject(OemsRequest request, OemsOrderStatus orderStatus) {
        return new OemsResponseExpectations(request, CANCEL_REJECT, EXEC_TYPE_UNSPECIFIED, orderStatus, null, null, "", "", "", "", "", null, null, "Current status: " + orderStatus.name(), null);
    }

    public static OemsResponseExpectations restated(OemsRequest request, OemsOrderStatus orderStatus, String newOrderQty, String cumQty, String leavesQty, String avgPrice, String calculatedCcyReducedQty, String reason) {
        return new OemsResponseExpectations(request, EXECUTION_REPORT, OemsExecType.RESTATED, orderStatus, null, null, newOrderQty, cumQty, leavesQty, "0", avgPrice, null, calculatedCcyReducedQty, reason, OemsExecRestatementReason.PARTIAL_DECLINE_OF_ORDER_QTY);
    }

    public static OemsResponseExpectations statusExpired(OemsRequest request, String cumQty, String avgPrice) {
        return new OemsResponseExpectations(request, EXECUTION_REPORT, OemsExecType.EXPIRED, OemsOrderStatus.STATUS_EXPIRED, null, null, request.getQuantity(), cumQty, "0", "0", avgPrice, null, null, "", null);
    }
}
