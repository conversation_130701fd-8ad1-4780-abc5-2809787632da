plugins {
    id 'java'
    id 'idea'
    id 'jaco<PERSON>'
    alias dependencyCatalog.plugins.spring.boot
    alias dependencyCatalog.plugins.dependency.management
    alias dependencyCatalog.plugins.sonarqube
    alias dependencyCatalog.plugins.jacocoToCobertura
    alias dependencyCatalog.plugins.allure.plugin
}

group = 'io.wyden'
version = '0.0.1-SNAPSHOT'
sourceCompatibility = '17'

ext {
    repository_username = System.env.NEXUS_DEPLOY_USERNAME
    repository_password = System.env.NEXUS_DEPLOY_PASSWORD
}

repositories {
    mavenLocal()
    maven {
        name 'nexus-releases'
        url 'https://repo.wyden.io/nexus/repository/releases/'
        credentials {
            username repository_username
            password repository_password
        }
    }
    maven {
        name 'nexus-snapshot'
        url 'https://repo.wyden.io/nexus/repository/snapshots/'
        credentials {
            username repository_username
            password repository_password
        }
    }
    mavenCentral()
}

dependencies {
    implementation dependencyCatalog.spring.boot.starter.actuator
    implementation dependencyCatalog.spring.boot.starter.web
    implementation dependencyCatalog.springdoc.openapi.starter.webmvc.ui
    implementation dependencyCatalog.rest.api.client

    annotationProcessor dependencyCatalog.spring.boot.configuration.processor

    implementation(dependencyCatalog.scenario.runner) { exclude group: 'io.netty', module: 'netty-resolver-dns-native-macos' }
    implementation dependencyCatalog.published.language.oems
    implementation dependencyCatalog.cloud.utils.tools
    implementation dependencyCatalog.guava

    testImplementation dependencyCatalog.spring.boot.starter.test
    testImplementation dependencyCatalog.junit.jupiter.api
    testImplementation dependencyCatalog.assertj.core
    testImplementation dependencyCatalog.awaitility
}

testing {
    suites {
        test {
            useJUnitJupiter()
        }

        integrationTest(JvmTestSuite) {
            dependencies {
                implementation project()
                implementation dependencyCatalog.spring.boot.starter.test
                implementation dependencyCatalog.junit.jupiter.api
                implementation dependencyCatalog.assertj.core
                implementation dependencyCatalog.awaitility
            }

            targets {
                all {
                    testTask.configure {
                        shouldRunAfter(test)
                    }
                }
            }
        }
    }
}

bootRun {
    jvmArgs = ["-agentlib:jdwp=transport=dt_socket,server=y,suspend=n,address=9105"]
    environment([
            "SPRING_PROFILES_ACTIVE": "dev"
    ])
}

tasks.named('check') {
    dependsOn(testing.suites.integrationTest)
}

sonarqube {
    properties {
        property "sonar.projectKey", "load-generator"
        property "sonar.projectName", "Load Generator"
        property "sonar.qualitygate.wait", true
    }
}

test {
    finalizedBy jacocoTestReport
}

jacocoTestReport {
    reports {
        xml.enabled true
        csv.enabled true
    }

    getExecutionData().setFrom(fileTree(buildDir).include("/jacoco/*.exec"))
}

jacocoToCobertura {
    inputFile.set(file("$buildDir/reports/jacoco/test/jacocoTestReport.xml"))
    outputFile.set(file("$buildDir/reports/jacoco/test/cobertura.xml"))
}

plugins.withType(JacocoPlugin) {
    tasks["test"].finalizedBy 'jacocoTestReport'
    tasks["integrationTest"].finalizedBy 'jacocoTestReport'
    tasks["jacocoTestReport"].finalizedBy 'jacocoToCobertura'
    tasks["jacocoToCobertura"].dependsOn 'jacocoTestReport'
}

import org.gradle.api.tasks.testing.logging.TestExceptionFormat
import org.gradle.api.tasks.testing.logging.TestLogEvent

tasks.withType(Test) {
    maxHeapSize = "2048m"
    testLogging {
        info {
            events TestLogEvent.FAILED,
                    TestLogEvent.PASSED,
                    TestLogEvent.SKIPPED
        }
        debug {
            events TestLogEvent.STARTED,
                    TestLogEvent.FAILED,
                    TestLogEvent.PASSED,
                    TestLogEvent.SKIPPED,
                    TestLogEvent.STANDARD_OUT,
                    TestLogEvent.STANDARD_ERROR
            exceptionFormat TestExceptionFormat.FULL
            showExceptions true
            showCauses true
            showStackTraces true
            showStandardStreams true
        }

        afterSuite { desc, result ->
            if (!desc.parent) { // will match the outermost suite
                def output = "Results: ${result.resultType} (${result.testCount} tests, ${result.successfulTestCount} passed, ${result.failedTestCount} failed, ${result.skippedTestCount} skipped)"
                def startItem = '|  ', endItem = '  |'
                def repeatLength = startItem.length() + output.length() + endItem.length()
                println('\n' + ('-' * repeatLength) + '\n' + startItem + output + endItem + '\n' + ('-' * repeatLength))
            }
        }
    }
}
