export const sign = (request, payload) => {
  const host = request.environment.get('RestManagementUrl');
  const url = request.url.tryGetSubstituted();
  const path = url.split(host)[1];

  const nonce = new Date().getTime().toString();
  const method = request.method;
  let body = '';

  let input = nonce + method + path;
  body = payload || request.body.tryGetSubstituted();
  if (body) {
    input = input + body;
  }

  const signature = crypto.hmac.sha256()
    .withTextSecret(request.environment.get('ApiSecret'))
    .updateWithText(input)
    .digest().toHex();

  request.variables.set('signature', signature);
  request.variables.set('nonce', nonce);

  console.log('Variables used for signing: ');
  console.log('path: ' + path);
  console.log('nonce: ' + nonce);
  console.log('body: ' + body);
  console.log('input: ' + input);

  return { signature, nonce };
};

const useBody = request => request.method !== 'GET';