### Update portfolio config

GRAPHQL {{RestApiUrl}}/graphql
Authorization: Bearer {{$auth.token("joseph_the_banker")}}

mutation createQuotingConfiguration {
    createQuotingConfiguration(
        request: {
            displayName: "test-quoting"
            nostroPortfolio: "maciekquoting2"
            sourceAccounts: ["simulator"]
            minQuantityFactor: 0.1
            maxQuantityFactor: 0.3
            maximumDepth: 0.1
            throttlingPeriod: 1
        }) {
        clobUid,
        resultsPerInstrument {
            isValid
        }
    }
}
