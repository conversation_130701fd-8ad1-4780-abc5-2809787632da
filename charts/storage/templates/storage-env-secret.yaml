apiVersion: v1
kind: Secret
metadata:
  name: {{ include "storage.fullname" . }}-env-secret
  labels:
    {{- include "storage.labels" . | nindent 4 }}
type: Opaque
data:
  DATABASE_HOST: "{{ tpl .Values.postgresql.host . | b64enc }}"
  SPRING_DATASOURCE_URL: {{ printf "jdbc:postgresql://%s:%s/%s" (tpl .Values.postgresql.host .) (tpl .Values.postgresql.port .) (tpl .Values.postgresql.schema .) | b64enc | quote }}
  SPRING_DATASOURCE_USERNAME: "{{ tpl .Values.postgresql.username . | b64enc }}"
  SPRING_DATASOURCE_PASSWORD: "{{ tpl .Values.postgresql.password . | b64enc }}"
  SPRING_FLYWAY_URL: {{ printf "jdbc:postgresql://%s:%s/%s" (tpl .Values.postgresql.host .) (tpl .Values.postgresql.port .) (tpl .Values.postgresql.schema .) | b64enc | quote }}
  SPRING_FLYWAY_USER: "{{ tpl .Values.postgresql.username . | b64enc }}"
  SPRING_FLYWAY_PASSWORD: "{{ tpl .Values.postgresql.password . | b64enc }}"
  {{- $tracingEndpoint := include "generate.tracing.endpoint" . }}
  {{- $tracingEnabled := include "generate.tracing.enabled" . }}
  {{- if and $tracingEnabled $tracingEndpoint }}
  TRACING_COLLECTOR_ENDPOINT: "{{ $tracingEndpoint | b64enc }}"
  {{- end }}
  {{- if not $tracingEnabled }}
  TRACING_COLLECTOR_ENDPOINT: {{ printf "disabled" | b64enc | quote }}
  {{- end }}