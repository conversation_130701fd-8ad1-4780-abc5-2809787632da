apiVersion: apps/v1
kind: Deployment
metadata:
  name: {{ include "wyden-ui.fullname" . }}
  labels:
    {{- include "wyden-ui.labels" . | nindent 4 }}
  {{- with .Values.annotations }}
  annotations:
    {{- toYaml . | nindent 4 }}
  {{- end }}
spec:
  replicas: {{ .Values.replicaCount }}
  selector:
    matchLabels:
      {{- include "wyden-ui.selectorLabels" . | nindent 6 }}
  template:
    metadata:
      labels:
        {{- include "wyden-ui.selectorLabels" . | nindent 8 }}
      annotations:
        checksum/nginx-configmap: {{ include (print $.Template.BasePath "/nginx-configmap.yaml") . | sha256sum }}
        checksum/config: {{ include (print $.Template.BasePath "/env-configmap.yaml") . | sha256sum }}
        {{- include "generate.podAnnotations" . | nindent 8 }}
    spec:
      {{- include "generate.imagePullSecrets" . | nindent 6 }}
      {{- if hasKey .Values "podSecurityContextOverride" }}
      securityContext:
        {{- tpl (toYaml .Values.podSecurityContextOverride | nindent 8) .}}
      {{- else if .Values.podSecurityContext.enabled }}
      securityContext:
        fsGroup: {{ .Values.podSecurityContext.fsGroup | default 1000 }}
        fsGroupChangePolicy: {{ .Values.podSecurityContext.fsGroupChangePolicy | default "OnRootMismatch" }}
      {{- end }}
      containers:
        - name: ui
          image: "{{ tpl .Values.image.registry . }}/{{ .Values.image.repository }}:{{ .Values.image.tag | default "latest" }}"
          imagePullPolicy: "{{ .Values.image.pullPolicy }}"
          ## If there is a need to enable debug mode uncomment
          # args: [nginx-debug, '-g', 'daemon off;']
          env:
          - name: REST_API_WS_URL
            value: ws{{ if eq (tpl .Values.rest.secure .) "true" }}s{{ end }}://{{ tpl .Values.rest.host . }}
          - name: REST_API_URL
            value: http{{ if eq (tpl .Values.rest.secure .) "true" }}s{{ end }}://{{ tpl .Values.rest.host . }}
          - name: KEYCLOAK_URL
            value: http{{ if eq (tpl .Values.keycloak.tls.enabled .) "true" }}s{{ end }}://{{ tpl .Values.keycloak.host . }}
          {{- with .Values.env }}
          {{- toYaml . | nindent 10 }}
          {{- end }}
          {{- if hasKey .Values "containerSecurityContextOverride" }}
          securityContext:
            {{- tpl (toYaml .Values.containerSecurityContextOverride | nindent 12) .}}
          {{- else if .Values.containerSecurityContext.enabled }}
          securityContext:
            runAsUser: {{ .Values.containerSecurityContext.runAsUser | default 1000 }}
            runAsGroup: {{ .Values.containerSecurityContext.runAsGroup | default 1000 }}
            allowPrivilegeEscalation: {{ .Values.containerSecurityContext.allowPrivilegeEscalation | default false }}
            {{- with .Values.containerSecurityContext.capabilities }}
            capabilities:
              {{- toYaml . | nindent 14 }}
            {{- end }}
          {{- end }}
          ports:
            - name: rest
              containerPort: 8080
              protocol: TCP
          volumeMounts:
          - mountPath: /etc/nginx/templates
            name: nginx-server-blocks
          - mountPath: /usr/share/nginx/html/config.json
            subPath: config.json
            name: wyden-config
          {{- if .Values.extraVolumeMounts }}
          {{- toYaml .Values.extraVolumeMounts | nindent 10}}
          {{- end }}
          resources:
            {{- toYaml .Values.resources | nindent 12 }}
      {{- include "generate.nodeSelector" . | nindent 6 }}
      {{- include "generate.tolerations" . | nindent 6 }}
      {{- include "generate.affinity" . | nindent 6 }}
      volumes:
      - configMap:
          defaultMode: 0664
          name: {{ include "wyden-ui.fullname" . }}-nginx-configmap
        name: nginx-server-blocks
      - configMap:
          defaultMode: 0664
          name: {{ include "wyden-ui.fullname" . }}-config-json
        name: wyden-config
      {{- if .Values.extraVolumes }}
      {{ toYaml .Values.extraVolumes | nindent 6}}
      {{- end }}
