apiVersion: v1
kind: ConfigMap
metadata:
  name: {{ include "wyden-ui.fullname" . }}-config-json
  annotations:
    "helm.sh/resource-policy": keep
  labels:
    {{- include "wyden-ui.labels" . | nindent 4 }}
data:
  config.json: |-
    {
      "production": {
        "VITE_APP_VERSION": "{{ .Chart.AppVersion }}",
        "VITE_CHART_VERSION": "{{ .Chart.Version }}",
        "VITE_REDIRECT_URI": "http{{ if eq (tpl .Values.ui.secure .) "true" }}s{{ end }}://{{ tpl .Values.ui.host . }}",
        {{- if and .Values.keycloak.secure }}
        {{- if eq "true" (tpl .Values.keycloak.secure .) }}
        "VITE_KEYCLOAK_URI": "https://{{ tpl .Values.keycloak.host . }}",
        {{- else if eq "false" (tpl .Values.keycloak.secure .) }}
        "VITE_KEYCLOAK_URI": "http://{{ tpl .Values.keycloak.host . }}",
        {{- else }}
        {{- if eq "true" (tpl .Values.keycloak.tls.enabled .) }}
        "VITE_KEYCLOAK_URI": "https://{{ tpl .Values.keycloak.host . }}",
        {{- else }}
        "VITE_KEYCLOAK_URI": "http://{{ tpl .Values.keycloak.host . }}",
        {{- end }}
        {{- end }}
        {{- end }}
        "VITE_REST_API_SERVER": "http{{ if eq (tpl .Values.rest.secure .) "true" }}s{{ end }}://{{ tpl .Values.rest.host . }}",
        "VITE_REST_API_SERVER_WS": "ws{{ if eq (tpl .Values.rest.secure .) "true" }}s{{ end }}://{{ tpl .Values.rest.host . }}"
      }
    }