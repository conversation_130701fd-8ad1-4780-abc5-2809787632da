
1 observations from 2024-12-12 15:15:59.570+0000 to 2024-12-12 15:15:59.570+0000 for:
io.aeron.exceptions.AeronEvent: WARN - commit-pos counter unexpectedly closed, terminating


1 observations from 2024-12-12 15:15:59.571+0000 to 2024-12-12 15:15:59.571+0000 for:
io.aeron.cluster.service.ClusterTerminationException: expected termination
	at io.aeron.cluster.service.ClusteredServiceAgent.checkForClockTick(ClusteredServiceAgent.java:1092)
	at io.aeron.cluster.service.ClusteredServiceAgent.doWork(ClusteredServiceAgent.java:235)
	at org.agrona.concurrent.AgentRunner.doWork(AgentRunner.java:304)
	at org.agrona.concurrent.AgentRunner.workLoop(AgentRunner.java:296)
	at org.agrona.concurrent.AgentRunner.run(AgentRunner.java:162)
	at java.base/java.lang.Thread.run(Unknown Source)


2 distinct errors observed.
