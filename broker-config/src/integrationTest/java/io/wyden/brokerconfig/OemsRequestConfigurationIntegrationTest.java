package io.wyden.brokerconfig;

import com.google.protobuf.Message;
import com.google.protobuf.ProtocolStringList;
import com.rabbitmq.client.BasicProperties;
import io.wyden.brokerconfig.domain.PortfolioGroupRepository;
import io.wyden.brokerconfig.domain.PortfolioRepository;
import io.wyden.brokerconfig.domain.referencedata.InstrumentsService;
import io.wyden.brokerconfig.testcontainers.TestContainersIntegrationBase;
import io.wyden.cloudutils.rabbitmq.RabbitExchange;
import io.wyden.cloudutils.rabbitmq.destination.OemsHeader;
import io.wyden.cloudutils.rabbitmq.destination.OemsTarget;
import io.wyden.published.brokerdesk.ConfigStatus;
import io.wyden.published.brokerdesk.PortfolioConfig;
import io.wyden.published.brokerdesk.PortfolioGroupConfig;
import io.wyden.published.brokerdesk.SorTarget;
import io.wyden.published.oems.FeeBasis;
import io.wyden.published.oems.FeeType;
import io.wyden.published.oems.OemsOrderType;
import io.wyden.published.oems.OemsRequest;
import io.wyden.published.oems.OemsResponse;
import io.wyden.published.oems.OemsTargetType;
import io.wyden.published.rate.Rate;
import io.wyden.published.referencedata.AssetClass;
import io.wyden.published.referencedata.BaseInstrument;
import io.wyden.published.referencedata.ForexSpotProperties;
import io.wyden.published.referencedata.Instrument;
import io.wyden.published.referencedata.VenueAccount;
import io.wyden.rate.client.RatesCacheFacade;
import io.wyden.referencedata.client.InstrumentsCacheFacade;
import io.wyden.referencedata.client.VenueAccountCacheFacade;
import org.apache.commons.lang3.tuple.Pair;
import org.awaitility.Awaitility;
import org.jetbrains.annotations.NotNull;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.mock.mockito.MockBean;

import java.math.BigDecimal;
import java.time.Duration;
import java.util.Collection;
import java.util.Collections;
import java.util.Iterator;
import java.util.Map;
import java.util.Optional;
import java.util.Set;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.TimeoutException;

import static io.wyden.brokerconfig.domain.ConfigFactory.CLIENT_SIDE_BASE_CURR;
import static io.wyden.brokerconfig.domain.ConfigFactory.CLIENT_SIDE_INSTRUMENT;
import static io.wyden.brokerconfig.domain.ConfigFactory.CLIENT_SIDE_QUOTE_CURR;
import static io.wyden.brokerconfig.domain.ConfigFactory.FIXED_FEE;
import static io.wyden.brokerconfig.domain.ConfigFactory.PERCENTAGE_FEE;
import static io.wyden.brokerconfig.domain.ConfigFactory.SOR_STREET_INSTRUMENT;
import static io.wyden.brokerconfig.domain.ConfigFactory.SOR_STREET_INSTRUMENT_OVERRIDE;
import static io.wyden.brokerconfig.domain.ConfigFactory.SOR_TRADING_ACCOUNT;
import static io.wyden.brokerconfig.domain.ConfigFactory.SOR_TRADING_ACCOUNT_OVERRIDE;
import static io.wyden.brokerconfig.domain.ConfigFactory.STREET_SIDE_INSTRUMENT;
import static io.wyden.brokerconfig.domain.ConfigFactory.createOemsRequest;
import static io.wyden.brokerconfig.domain.ConfigFactory.createPortfolioConfig;
import static io.wyden.brokerconfig.domain.ConfigFactory.createPortfolioConfigWithIncompleteExecutionSource;
import static io.wyden.brokerconfig.domain.ConfigFactory.createPortfolioGroupConfig;
import static io.wyden.brokerconfig.domain.ConfigFactory.createSorPortfolioConfig;
import static io.wyden.brokerconfig.domain.ConfigFactory.createSorPortfolioConfigWithOverride;
import static io.wyden.published.oems.OemsExecType.REJECTED;
import static io.wyden.published.oems.OemsOrderStatus.STATUS_REJECTED;
import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.Mockito.when;

public class OemsRequestConfigurationIntegrationTest extends TestContainersIntegrationBase {

    private static final String VENUE_NAME = "Venue";
    private static final String VENUE_ACCOUNT = "BITMEX-testnet-1";
    private static final String NOT_CONFIGURED = "Trading not configured, contact administrator";
    private static final String PORTFOLIO_ID = "Client 1";
    private static final Duration TIMEOUT = Duration.ofSeconds(5);
    private static final String QUANTITY = "2.5";
    private static final String PRICE = "40000";

    public static final Map<String, String> OEMS_BROKER_REQUEST_WITH_CONFIG_REQUIRED = Map.of(
        OemsHeader.MESSAGE_TYPE.getHeaderName(), OemsRequest.class.getSimpleName(),
        OemsHeader.TARGET_TYPE.getHeaderName(), OemsTargetType.BROKER_DESK.name(),
        OemsHeader.TARGET.getHeaderName(), OemsTarget.NOT_CONFIGURED.getTarget()
    );

    @Autowired
    private PortfolioGroupRepository portfolioGroupRepository;

    @Autowired
    private PortfolioRepository portfolioRepository;

    @MockBean
    private InstrumentsService instrumentsService;

    @MockBean
    private InstrumentsCacheFacade instrumentsCacheFacade;

    @MockBean
    private VenueAccountCacheFacade venueAccountCacheFacade;

    @MockBean
    private RatesCacheFacade ratesCacheFacade;

    @Autowired
    private RabbitExchange<Message> tradingIngressExchange;

    @BeforeEach
    void beforeAll() {
        when(instrumentsService.resolveClientSideVenueName(CLIENT_SIDE_INSTRUMENT)).thenReturn(VENUE_NAME);
        when(instrumentsCacheFacade.find("BTCUSD@FOREX@Bank")).thenReturn(Optional.of(Instrument.newBuilder()
            .setBaseInstrument(BaseInstrument.newBuilder()
                .setAssetClass(AssetClass.FOREX)
                .setSymbol("BTCUSD")
                .setQuoteCurrency("USD")
                .build())
            .setForexSpotProperties(ForexSpotProperties.newBuilder()
                .setBaseCurrency("BTC")
                .build())
            .build()));
        mockRate("BTC", "USD", PRICE);
        mockRate("USD", "USD", "1");
    }

    @Test
    public void shouldSavePortfolioGroupConfiguration() {
        String portfolioGroupId = "group 1";
        PortfolioGroupConfig portfolioGroup = createPortfolioGroupConfig()
            .setId(portfolioGroupId)
            .build();

        portfolioGroupRepository.save(portfolioGroup);

        Optional<PortfolioGroupConfig> optionalPortfolioGroup = portfolioGroupRepository.find(portfolioGroupId);
        assertThat(optionalPortfolioGroup).isPresent();

        PortfolioGroupConfig actualPortfolioGroup = optionalPortfolioGroup.get();
        assertThat(actualPortfolioGroup).isEqualTo(portfolioGroup);
    }

    @Test
    public void shouldSavePortfolioConfiguration() {
        String portfolioName = "portfolio 1";

        PortfolioConfig portfolio = createPortfolioConfig()
            .setName(portfolioName)
            .build();

        portfolioRepository.save(portfolio);

        Optional<PortfolioConfig> optionalPortfolio = portfolioRepository.find(portfolioName);
        assertThat(optionalPortfolio).isPresent();

        PortfolioConfig actualPortfolio = optionalPortfolio.get();
        assertThat(actualPortfolio).isEqualTo(portfolio);
    }

    @Test
    void shouldEnhanceOemsRequestWithMatchingConfiguration() throws ExecutionException, InterruptedException, TimeoutException {
        // create some configuration
        PortfolioConfig portfolio = createPortfolioConfig()
            .setId(PORTFOLIO_ID)
            .build();
        portfolioRepository.save(portfolio);

        // create temporary queue to collect 'enhanced' oemsRequest messages
        Collection<OemsRequest> producedMessages = declareQueueAndBindTo(tradingIngressExchange, OemsRequest.parser());

        // create and publish OemsRequest
        OemsRequest oemsRequest = createOemsRequestConfigRequired(PORTFOLIO_ID);

        tradingIngressExchange.publishWithHeaders(oemsRequest, OEMS_BROKER_REQUEST_WITH_CONFIG_REQUIRED)
            .get(10, TimeUnit.SECONDS);

        Awaitility.await("oemsRequest is processed")
            .atMost(TIMEOUT)
            .pollDelay(Duration.ofSeconds(1))
            .until(() -> producedMessages.size() > 1);

        Iterator<OemsRequest> iterator = producedMessages.iterator();
        OemsRequest initialOemsRequest = iterator.next();
        validateInitialOemsRequest(initialOemsRequest);

        // make sure 'enhanced' OemsRequest has configuration attached to it
        OemsRequest processedOemsRequest = iterator.next();
        validateResolvedOemsRequest(processedOemsRequest);

        // make sure that attached configuration is from preconfigured PortfolioConfig
        assertThat(processedOemsRequest.getPricingConfig()).isEqualTo(portfolio.getPricingConfig());
        assertThat(processedOemsRequest.getExecutionConfig()).isEqualTo(portfolio.getExecutionConfig());
    }

    @Test
    void shouldEnhanceOemsRequestWithMatchingConfiguration_executionConfigMissingInstrumentId() throws ExecutionException, InterruptedException, TimeoutException {
        when(venueAccountCacheFacade.venueAccountDetails(Set.of(VENUE_ACCOUNT)))
            .thenReturn(Collections.singleton(VenueAccount.newBuilder().setVenueName(VENUE_NAME).setId(VENUE_ACCOUNT).build()));
        when(instrumentsService.findInstrument(CLIENT_SIDE_BASE_CURR, CLIENT_SIDE_QUOTE_CURR, VENUE_NAME))
            .thenReturn(Optional.of(STREET_SIDE_INSTRUMENT));

        // create some configuration
        PortfolioConfig portfolio = createPortfolioConfigWithIncompleteExecutionSource()
            .setId(PORTFOLIO_ID)
            .build();
        portfolioRepository.save(portfolio);

        // create temporary queue to collect 'enhanced' oemsRequest messages
        Collection<OemsRequest> producedMessages = declareQueueAndBindTo(tradingIngressExchange, OemsRequest.parser());

        // create and publish OemsRequest
        OemsRequest oemsRequest = createOemsRequestConfigRequired(PORTFOLIO_ID);

        tradingIngressExchange.publishWithHeaders(oemsRequest, OEMS_BROKER_REQUEST_WITH_CONFIG_REQUIRED)
            .get(10, TimeUnit.SECONDS);

        Awaitility.await("oemsRequest is processed")
            .atMost(TIMEOUT)
            .pollDelay(Duration.ofSeconds(1))
            .until(() -> producedMessages.size() > 1);

        Iterator<OemsRequest> iterator = producedMessages.iterator();
        OemsRequest initialOemsRequest = iterator.next();
        validateInitialOemsRequest(initialOemsRequest);

        // make sure 'enhanced' OemsRequest has configuration attached to it
        OemsRequest processedOemsRequest = iterator.next();
        validateResolvedOemsRequest(processedOemsRequest);

        assertThat(processedOemsRequest.getPricingConfig().getPricingSourceList()).hasSize(1);
        assertThat(processedOemsRequest.getPricingConfig().getPricingSource(0))
            .extracting("venueAccount_", "instrumentId_")
            .containsExactly(VENUE_ACCOUNT, STREET_SIDE_INSTRUMENT);
    }

    @Test
    void shouldEnhanceOemsRequestWithMatchingConfiguration_executionConfigMissingInstrumentId_agencyAccountDoesNotMapToVenueName() throws ExecutionException, InterruptedException, TimeoutException {
        // create some configuration
        PortfolioConfig portfolio = createPortfolioConfigWithIncompleteExecutionSource()
            .setId(PORTFOLIO_ID)
            .build();
        portfolioRepository.save(portfolio);

        // create temporary queue to collect 'enhanced' oemsRequest messages
        Collection<OemsRequest> producedMessages = declareQueueAndBindTo(tradingIngressExchange, OemsRequest.parser());
        Collection<OemsResponse> producedOemsResponse = declareQueueAndBindTo(tradingIngressExchange, OemsResponse.parser());

        // create and publish OemsRequest
        OemsRequest oemsRequest = createOemsRequestConfigRequired(PORTFOLIO_ID);

        tradingIngressExchange.publishWithHeaders(oemsRequest, OEMS_BROKER_REQUEST_WITH_CONFIG_REQUIRED)
            .get(10, TimeUnit.SECONDS);

        Awaitility.await("oemsRequest is processed")
            .atMost(TIMEOUT)
            .pollDelay(Duration.ofSeconds(1))
            .until(() -> producedMessages.size() > 1);

        Iterator<OemsRequest> iterator = producedMessages.iterator();
        OemsRequest initialOemsRequest = iterator.next();
        validateInitialOemsRequest(initialOemsRequest);

        Awaitility.await("oemsResponse is processed")
            .atMost(TIMEOUT)
            .pollDelay(Duration.ofSeconds(1))
            .until(() -> producedOemsResponse.stream().anyMatch(response -> response.getExecType() == REJECTED));

        OemsResponse oemsResponse = producedOemsResponse.stream().filter(response -> response.getExecType() == REJECTED).findFirst().get();
        assertThat(oemsResponse.getExecType()).isEqualTo(REJECTED);
        assertThat(oemsResponse.getOrderStatus()).isEqualTo(STATUS_REJECTED);
        assertThat(oemsResponse.getReason()).isEqualTo(NOT_CONFIGURED);
    }

    @Test
    void shouldEnhanceOemsRequestWithMatchingConfiguration_executionConfigMissingInstrumentId_streetSideInstrumentCannotBeFound() throws ExecutionException, InterruptedException, TimeoutException {
        when(venueAccountCacheFacade.venueAccountDetails(Set.of(VENUE_ACCOUNT)))
            .thenReturn(Collections.singleton(VenueAccount.newBuilder().setVenueName(VENUE_NAME).setId(VENUE_ACCOUNT).build()));
        when(instrumentsService.findInstrument(CLIENT_SIDE_BASE_CURR, CLIENT_SIDE_QUOTE_CURR, VENUE_NAME))
            .thenReturn(Optional.empty());

        // create some configuration
        PortfolioConfig portfolio = createPortfolioConfigWithIncompleteExecutionSource()
            .setId(PORTFOLIO_ID)
            .build();
        portfolioRepository.save(portfolio);

        // create temporary queue to collect 'enhanced' oemsRequest messages
        Collection<OemsRequest> producedMessages = declareQueueAndBindTo(tradingIngressExchange, OemsRequest.parser());
        Collection<OemsResponse> producedOemsResponse = declareQueueAndBindTo(tradingIngressExchange, OemsResponse.parser());

        // create and publish OemsRequest
        OemsRequest oemsRequest = createOemsRequestConfigRequired(PORTFOLIO_ID);

        tradingIngressExchange.publishWithHeaders(oemsRequest, OEMS_BROKER_REQUEST_WITH_CONFIG_REQUIRED)
            .get(10, TimeUnit.SECONDS);

        Awaitility.await("oemsRequest is processed")
            .atMost(TIMEOUT)
            .pollDelay(Duration.ofSeconds(1))
            .until(() -> producedMessages.size() > 1);

        Iterator<OemsRequest> iterator = producedMessages.iterator();
        OemsRequest initialOemsRequest = iterator.next();
        validateInitialOemsRequest(initialOemsRequest);

        Awaitility.await("oemsResponse is processed")
            .atMost(TIMEOUT)
            .pollDelay(Duration.ofSeconds(1))
            .until(() -> producedOemsResponse.stream().anyMatch(response -> response.getExecType() == REJECTED));

        OemsResponse oemsResponse = producedOemsResponse.stream().filter(response -> response.getExecType() == REJECTED).findFirst().get();
        assertThat(oemsResponse.getExecType()).isEqualTo(REJECTED);
        assertThat(oemsResponse.getOrderStatus()).isEqualTo(STATUS_REJECTED);
        assertThat(oemsResponse.getReason()).isEqualTo(NOT_CONFIGURED);
    }

    @Test
    void shouldEnhanceOemsRequestWithMatchingConfiguration_sorOrderWithoutOverrides() throws ExecutionException, InterruptedException, TimeoutException {
        // create some configuration
        PortfolioConfig portfolio = createSorPortfolioConfig()
            .setId(PORTFOLIO_ID)
            .build();
        portfolioRepository.save(portfolio);

        // create temporary queue to collect 'enhanced' oemsRequest messages
        Collection<OemsRequest> producedMessages = declareQueueAndBindTo(tradingIngressExchange, OemsRequest.parser());

        // create and publish OemsRequest
        OemsRequest oemsRequest = createOemsRequestConfigRequired(PORTFOLIO_ID);

        tradingIngressExchange.publishWithHeaders(oemsRequest, OEMS_BROKER_REQUEST_WITH_CONFIG_REQUIRED)
            .get(10, TimeUnit.SECONDS);

        Awaitility.await("oemsRequest is processed")
            .atMost(TIMEOUT)
            .pollDelay(Duration.ofSeconds(1))
            .until(() -> producedMessages.size() > 1);

        Iterator<OemsRequest> iterator = producedMessages.iterator();
        OemsRequest initialOemsRequest = iterator.next();
        validateInitialOemsRequest(initialOemsRequest);

        // make sure 'enhanced' OemsRequest has configuration attached to it
        OemsRequest processedOemsRequest = iterator.next();
        validateResolvedOemsRequest(processedOemsRequest);

        ProtocolStringList sorTradingAccountsList = processedOemsRequest.getExecutionConfig().getSorTradingAccountsList();
        assertThat(sorTradingAccountsList).hasSize(1);
        assertThat(sorTradingAccountsList.get(0)).isEqualTo(SOR_TRADING_ACCOUNT);

        SorTarget sorTarget = processedOemsRequest.getExecutionConfig().getSorTarget();
        assertThat(sorTarget.getAssetClass()).isEqualTo(AssetClass.FOREX);
        assertThat(sorTarget.getSymbol()).isEqualTo(SOR_STREET_INSTRUMENT);
    }

    @Test
    void shouldEnhanceOemsRequestWithMatchingConfiguration_sorOrderWithPortfolioInstrumentOverride() throws ExecutionException, InterruptedException, TimeoutException {
        // create some configuration
        PortfolioConfig portfolio = createSorPortfolioConfigWithOverride()
            .setId(PORTFOLIO_ID)
            .build();
        portfolioRepository.save(portfolio);

        // create temporary queue to collect 'enhanced' oemsRequest messages
        Collection<OemsRequest> producedMessages = declareQueueAndBindTo(tradingIngressExchange, OemsRequest.parser());

        // create and publish OemsRequest
        OemsRequest oemsRequest = createOemsRequestConfigRequired(PORTFOLIO_ID);

        tradingIngressExchange.publishWithHeaders(oemsRequest, OEMS_BROKER_REQUEST_WITH_CONFIG_REQUIRED)
            .get(10, TimeUnit.SECONDS);

        Awaitility.await("oemsRequest is processed")
            .atMost(TIMEOUT)
            .pollDelay(Duration.ofSeconds(1))
            .until(() -> producedMessages.size() > 1);

        Iterator<OemsRequest> iterator = producedMessages.iterator();
        OemsRequest initialOemsRequest = iterator.next();
        validateInitialOemsRequest(initialOemsRequest);

        // make sure 'enhanced' OemsRequest has configuration attached to it
        OemsRequest processedOemsRequest = iterator.next();
        validateResolvedOemsRequest(processedOemsRequest);

        ProtocolStringList sorTradingAccountsList = processedOemsRequest.getExecutionConfig().getSorTradingAccountsList();
        assertThat(sorTradingAccountsList).hasSize(1);
        assertThat(sorTradingAccountsList.get(0)).isEqualTo(SOR_TRADING_ACCOUNT_OVERRIDE);

        SorTarget sorTarget = processedOemsRequest.getExecutionConfig().getSorTarget();
        assertThat(sorTarget.getAssetClass()).isEqualTo(AssetClass.FOREX);
        assertThat(sorTarget.getSymbol()).isEqualTo(SOR_STREET_INSTRUMENT_OVERRIDE);
    }

    @Test
    void shouldPassThroughNewOrderSingleRequestWithPtcRequired() throws ExecutionException, InterruptedException, TimeoutException {
        // create some configuration
        PortfolioConfig portfolio = createPortfolioConfig()
            .setId(PORTFOLIO_ID)
            .build();
        portfolioRepository.save(portfolio);

        // create temporary queue to collect 'enhanced' oemsRequest messages
        Collection<Pair<OemsRequest, BasicProperties>> producedMessages = consumeWithProperties(tradingIngressExchange, OemsRequest.parser());

        // create and publish OemsRequest
        OemsRequest oemsRequest = createOemsRequestConfigRequired(PORTFOLIO_ID);

        tradingIngressExchange.publishWithHeaders(oemsRequest, OEMS_BROKER_REQUEST_WITH_CONFIG_REQUIRED)
            .get(10, TimeUnit.SECONDS);

        Awaitility.await("oemsRequest is processed")
            .atMost(TIMEOUT)
            .pollDelay(Duration.ofSeconds(1))
            .until(() -> producedMessages.size() > 1);

        Iterator<Pair<OemsRequest, BasicProperties>> iterator = producedMessages.iterator();
        OemsRequest initialOemsRequest = iterator.next().getKey();
        validateInitialOemsRequest(initialOemsRequest);

        // make sure 'enhanced' OemsRequest has PTC attached to it
        Pair<OemsRequest, BasicProperties> processedOemsRequest = iterator.next();
        assertThat(processedOemsRequest.getKey().getPtc()).isEqualTo(OemsRequest.OemsPTCStatus.REQUIRED);
        assertThat(processedOemsRequest.getValue().getHeaders().get(OemsHeader.PTC.getHeaderName()).toString()).isEqualTo(OemsRequest.OemsPTCStatus.REQUIRED.name());

    }

    @Test
    void shouldPassThroughCancelRequestWithPtcNotRequired() throws ExecutionException, InterruptedException, TimeoutException {
        // create some configuration
        PortfolioConfig portfolio = createPortfolioConfig()
            .setId(PORTFOLIO_ID)
            .build();
        portfolioRepository.save(portfolio);

        // create temporary queue to collect 'enhanced' oemsRequest messages
        Collection<Pair<OemsRequest, BasicProperties>> producedMessages = consumeWithProperties(tradingIngressExchange, OemsRequest.parser());

        // create and publish Cancel OemsRequest
        OemsRequest oemsRequest = createOemsRequestConfigRequired(PORTFOLIO_ID).toBuilder()
            .setRequestType(OemsRequest.OemsRequestType.CANCEL)
            .build();

        tradingIngressExchange.publishWithHeaders(oemsRequest, OEMS_BROKER_REQUEST_WITH_CONFIG_REQUIRED)
            .get(10, TimeUnit.SECONDS);

        Awaitility.await("oemsRequest is processed")
            .atMost(TIMEOUT)
            .pollDelay(Duration.ofSeconds(1))
            .until(() -> producedMessages.size() > 1);

        Iterator<Pair<OemsRequest, BasicProperties>> iterator = producedMessages.iterator();
        OemsRequest initialOemsRequest = iterator.next().getKey();
        validateInitialOemsRequest(initialOemsRequest);

        // make sure 'enhanced' OemsRequest has PTC attached to it
        Pair<OemsRequest, BasicProperties> processedOemsRequest = iterator.next();
        assertThat(processedOemsRequest.getKey().getPtc()).isEqualTo(OemsRequest.OemsPTCStatus.NOT_REQUIRED);
        assertThat(processedOemsRequest.getValue().getHeaders().get(OemsHeader.PTC.getHeaderName()).toString()).isEqualTo(OemsRequest.OemsPTCStatus.NOT_REQUIRED.name());
    }

    @Test
    void shouldCalculateAndAddReservationFeesToOemsRequest() throws ExecutionException, InterruptedException, TimeoutException {
        PortfolioConfig portfolio = createPortfolioConfig()
            .setId(PORTFOLIO_ID)
            .build();
        portfolioRepository.save(portfolio);

        Collection<OemsRequest> producedMessages = declareQueueAndBindTo(tradingIngressExchange, OemsRequest.parser());

        OemsRequest oemsRequest = createOemsRequestConfigRequired(PORTFOLIO_ID)
            .toBuilder()
            .setQuantity("2.5")
            .setOrderType(OemsOrderType.MARKET)
            .clearPrice()
            .build();

        tradingIngressExchange.publishWithHeaders(oemsRequest, OEMS_BROKER_REQUEST_WITH_CONFIG_REQUIRED)
            .get(10, TimeUnit.SECONDS);

        Awaitility.await("oemsRequest is processed")
            .atMost(TIMEOUT)
            .pollDelay(Duration.ofSeconds(1))
            .until(() -> producedMessages.size() > 1);

        // Get the initial and processed messages
        Iterator<OemsRequest> iterator = producedMessages.iterator();
        OemsRequest initialOemsRequest = iterator.next();
        OemsRequest processedOemsRequest = iterator.next();

        // Verify initial request has no fees
        assertThat(initialOemsRequest.getPotentialFeesList()).isEmpty();

        // Verify processed request has fees calculated
        assertThat(processedOemsRequest.getPotentialFeesList()).isNotEmpty();

        // Calculate expected percentage fee: 2.5 BTC * 40000 USD/BTC * 0.015 = 1500 USD
        BigDecimal expectedPercentageFee = new BigDecimal(QUANTITY)
            .multiply(new BigDecimal(PRICE))
            .multiply(new BigDecimal(PERCENTAGE_FEE));

        // Verify fixed fee
        assertThat(processedOemsRequest.getPotentialFeesList())
            .anyMatch(fee ->
                fee.getType() == FeeType.FIXED_FEE &&
                    fee.getAmount().equals(FIXED_FEE) &&
                    fee.getCurrency().equals(CLIENT_SIDE_QUOTE_CURR) &&
                    fee.getBasis() == FeeBasis.ABSOLUTE
            );

        // Verify percentage fee
        assertThat(processedOemsRequest.getPotentialFeesList())
            .anyMatch(fee ->
                fee.getType() == FeeType.TRANSACTION_FEE &&
                    new BigDecimal(fee.getAmount()).compareTo(expectedPercentageFee) == 0 &&
                    fee.getCurrency().equals(CLIENT_SIDE_QUOTE_CURR) &&
                    fee.getBasis() == FeeBasis.ABSOLUTE
            );
    }

    @NotNull
    private static OemsRequest createOemsRequestConfigRequired(String portfolioId) {
        return createOemsRequest()
            .setPortfolioId(portfolioId)
            .setConfigStatus(ConfigStatus.CONFIG_REQUIRED)
            .build();
    }

    private static void validateInitialOemsRequest(OemsRequest initialOemsRequest) {
        assertThat(initialOemsRequest.hasExecutionConfig()).isFalse();
        assertThat(initialOemsRequest.hasPricingConfig()).isFalse();
        assertThat(initialOemsRequest.getConfigStatus()).isEqualTo(ConfigStatus.CONFIG_REQUIRED);
    }

    private static void validateResolvedOemsRequest(OemsRequest processedOemsRequest) {
        assertThat(processedOemsRequest.hasExecutionConfig()).isTrue();
        assertThat(processedOemsRequest.hasPricingConfig()).isTrue();
        assertThat(processedOemsRequest.getConfigStatus()).isEqualTo(ConfigStatus.CONFIG_RESOLVED);
    }

    private void mockRate(String baseCurrency, String quoteCurrency, String price) {
        when(ratesCacheFacade.find(baseCurrency, quoteCurrency)).thenReturn(Optional.of(Rate.newBuilder()
            .setBaseCurrency(baseCurrency)
            .setQuoteCurrency(quoteCurrency)
            .setValue(price)
            .build()));
    }
}
