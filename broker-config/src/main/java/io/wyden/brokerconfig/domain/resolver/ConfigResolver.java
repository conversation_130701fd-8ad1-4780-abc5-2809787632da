package io.wyden.brokerconfig.domain.resolver;

import com.google.protobuf.Message;
import io.wyden.published.marketdata.InstrumentKey;

import java.util.List;

import static org.apache.commons.lang3.StringUtils.defaultIfBlank;

public interface ConfigResolver<B extends Message.Builder, M extends Message> {

    /**
     * Resolves the configuration for the target using the provided instrumentConfig, portfolioConfig,
     * groupInstrumentConfig, and groupConfig.
     */
    void resolve(B target, M... config);

    /**
     * Returns the first non-blank string from the given inputs. If all inputs are blank, null is returned.
     */
    default String first(String s1, String s2, String s3, String s4) {
        return defaultIfBlank(s1, defaultIfBlank(s2, defaultIfBlank(s3, defaultIfBlank(s4, null))));
    }

    /**
     * Returns the first non-empty list from the given inputs. If all inputs are empty, null is returned.
     */
    default List<InstrumentKey> first(List<InstrumentKey> l1, List<InstrumentKey> l2, List<InstrumentKey> l3, List<InstrumentKey> l4) {
        return defaultIfEmpty(l1, defaultIfEmpty(l2, defaultIfEmpty(l3, defaultIfEmpty(l4, null))));
    }

    /**
     * Returns the given list if it is not empty, otherwise returns the default list.
     *
     * @param list The list to check. Can be null.
     * @param defaultList The default list to return if the input list is empty. Can be null.
     * @param <T> The type of the elements in the list.
     * @return The input list if it is not empty, otherwise the default list.
     */
     default <T> List<T> defaultIfEmpty(List<T> list, List<T> defaultList) {
        if (list == null || list.isEmpty()) {
            return defaultList;
        }

        return list;
    }
}
