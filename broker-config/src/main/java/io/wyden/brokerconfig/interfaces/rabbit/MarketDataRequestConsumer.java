package io.wyden.brokerconfig.interfaces.rabbit;

import com.rabbitmq.client.AMQP;
import io.micrometer.core.instrument.MeterRegistry;
import io.micrometer.core.instrument.Tags;
import io.opentelemetry.api.trace.SpanKind;
import io.wyden.brokerconfig.domain.marketdata.MarketDataService;
import io.wyden.cloudutils.rabbitmq.ConsumptionResult;
import io.wyden.cloudutils.rabbitmq.RabbitExchange;
import io.wyden.cloudutils.rabbitmq.RabbitIntegrator;
import io.wyden.cloudutils.rabbitmq.queue.ExpiringRabbitQueueBuilder;
import io.wyden.cloudutils.rabbitmq.queue.MessageConsumer;
import io.wyden.cloudutils.rabbitmq.queue.RabbitQueue;
import io.wyden.cloudutils.telemetry.Telemetry;
import io.wyden.cloudutils.telemetry.tracing.Tracing;
import io.wyden.published.marketdata.MarketDataRequest;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import static io.wyden.brokerconfig.interfaces.rabbit.ProtoUtil.toHumanReadableString;

@Component
public class MarketDataRequestConsumer implements MessageConsumer<MarketDataRequest>  {

    private static final Logger LOGGER = LoggerFactory.getLogger(MarketDataRequestConsumer.class);

    private static final String INBOUND_KEY = "broker-config-service";

    private final RabbitExchange<MarketDataRequest> mdRequestExchange;
    private final EventLogEmitter eventLogEmitter;
    private final String consumerName;
    private final String queueName;
    private final MarketDataService marketDataService;
    private final Tracing otlTracing;
    private final MeterRegistry meterRegistry;

    public MarketDataRequestConsumer(RabbitExchange<MarketDataRequest> mdRequestExchange,
                                     EventLogEmitter eventLogEmitter,
                                     @Value("${spring.application.name}") String consumerName,
                                     @Value("${rabbitmq.market-data-broker-config-service-queue}") String queueName,
                                     RabbitIntegrator rabbitIntegrator,
                                     MarketDataService marketDataService,
                                     Telemetry telemetry) {
        this.mdRequestExchange = mdRequestExchange;
        this.eventLogEmitter = eventLogEmitter;
        this.consumerName = consumerName;
        this.queueName = queueName;
        this.marketDataService = marketDataService;
        this.otlTracing = telemetry.getTracing();
        this.meterRegistry = telemetry.getMeterRegistry();

        declareAndBindQueue(rabbitIntegrator);
    }

    private void declareAndBindQueue(RabbitIntegrator rabbitIntegrator) {
        RabbitQueue<MarketDataRequest> queue = new ExpiringRabbitQueueBuilder<MarketDataRequest>(rabbitIntegrator)
            .setQueueName(queueName)
            .setConsumerName(consumerName)
            .declare();

        queue.attachConsumer(MarketDataRequest.parser(), this);
        queue.bindWithRoutingKey(mdRequestExchange, INBOUND_KEY);
    }

    @Override
    public ConsumptionResult consume(MarketDataRequest request, AMQP.BasicProperties basicProperties) {
        LOGGER.info("MarketDataRequest received: {}", request);

        try (var ignored = otlTracing.createSpan("broker-config-service.md-request.consume", SpanKind.CONSUMER)) {
            updateMetrics(request);
            marketDataService.onRequest(request);
        } catch (Exception e) {
            LOGGER.error("Failed to process incoming MarketDataRequest, dropping without re-queue: {}", request, e);
            eventLogEmitter.emitMarketDataRequestRejected(request, "Failed to serve MarketDataRequest: %s".formatted(toHumanReadableString(request)));
            return ConsumptionResult.failureNonRecoverable();
        }

        return ConsumptionResult.consumed();
    }

    private void updateMetrics(MarketDataRequest marketDataRequest) {
        try {
            Tags tags = Tags.of(
                "instrumentId", marketDataRequest.getInstrumentKey().getInstrumentId(),
                "venueAccount", marketDataRequest.getInstrumentKey().getVenueAccount(),
                "portfolioId", marketDataRequest.getPortfolioId(),
                "depth", String.valueOf(marketDataRequest.getMarketDepth())
            );
            meterRegistry.counter("wyden.broker-config-service.md-request.incoming", tags).increment();
        } catch (Exception ex) {
            LOGGER.warn("Metrics update failed", ex);
        }
    }
}
